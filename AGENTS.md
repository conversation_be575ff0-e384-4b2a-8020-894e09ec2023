# Repository Guidelines

## Project Structure & Module Organization
Microservices live in `services/` (gateway, text-processor, rule-matcher, vector-matcher, llm, uie, intent, brand-attribution, post-processor, error-handler). Shared clients, schemas, and helpers live in `shared/`; reuse these instead of reimplementing Kafka, Elasticsearch, or StarRocks access. Runtime configuration sits in `configs/config.yaml`, container assets (`Dockerfile`, Compose files) stay at the root, and automation scripts live under `scripts/`. Current smoke tests like `test_vector_search.py` sit at the root—mirror that layout or add `tests/` folders beside new services.

## Build, Test, and Development Commands
- `python3 scripts/test/deploy_all_optimized.py` provisions Kafka topics, ES indices, and StarRocks tables for local verification.
- `docker build --no-cache -f Dockerfile.base -t voc-base:latest .` rebuilds the shared dev image after dependency updates.
- `docker compose -f docker-compose.dev.yml up -d` launches hot-reload containers; use `logs -f <service>-dev` to trace issues.
- `pytest` or `pytest -k text_processor` runs the async test suite; add `-s` when debugging stream outputs.

## Coding Style & Naming Conventions
Write Python 3.9 code with four-space indentation and type hints on public interfaces. Format with `black services shared` and `isort --profile black services shared`; keep linters quiet via `flake8`. Follow existing module naming (`main.py` entrypoints, `*_client.py` adapters) and prefer snake_case for files, functions, and async tasks. Extend the Pydantic models in `shared/models` to keep schemas consistent across services.

## Testing Guidelines
Extend pytest suites alongside the service being modified, naming files `test_<module>.py` and async tests `async def test_*`. Use `pytest-asyncio` fixtures for coroutine handlers and mock externals through the shared clients. Exercise both success and failure paths, especially around message routing and vector matching. When editing deploy scripts, include command snippets or log excerpts in the PR to show the result.

## Commit & Pull Request Guidelines
Git history leans on lowercase prefixes (`fix:`, `feat:`) plus a short Chinese or English summary—follow that pattern and reference services touched (e.g., `fix: adjust uie retry policy`). Group related changes per commit and avoid mixing config and code refactors. Pull requests should link Jira/GitLab issues when available, outline deployment impact, and note any required data or configuration migrations. Include screenshots or sample payloads whenever API behavior changes.

## Configuration & Security Notes
Do not hard-code credentials; load secrets via environment variables consumed by `configs/config.yaml`. Keep `logs/` out of version control and scrub customer data before sharing traces. Document exposed ports when editing Compose files so ops can mirror them in production.
