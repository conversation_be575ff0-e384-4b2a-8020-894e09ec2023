# VOC2.0智能打标系统配置文件 - 生产环境多实例版本
# 支持: 6个UIE实例 + 4个LLM实例 + 4个Intent实例 + 1个规则匹配 + 1个向量匹配

# 数据库配置
database:
  # StarRocks配置
  starrocks:
    host: *************
    port: 9030
    username: root
    password: ""
    database: voc_db
  
  # MySQL配置
  mysql:
    host: ************
    port: 33306
    username: root
    password: L7bzd1gmm+db
    database: voc_model
    database1: voc_ms_be
    charset: utf8mb4

# Elasticsearch配置
elasticsearch:
  hosts:
    - http://************:29200
  username: elastic
  password: L7bzd1gmm+es
  timeout: 30

# Kafka配置
kafka:
  bootstrap_servers:
    - ************:29092
  client_id: voc-client
  group_id: voc-analysis
  auto_offset_reset: earliest

  # 生产者配置
  producer:
    acks: all                  # 对应 Spring acks: -1
    retries: 3                 # 发生错误后重试次数
    batch_size: 16384          # 批量发送大小
    buffer_memory: 33554432    # 内存缓冲
    linger_ms: 1               # 批量发送延迟
    compression_type: gzip     # 压缩类型，使用gzip保证兼容性
    key_serializer: str        # key 序列化方式
    value_serializer: str      # value 序列化方式

  # 消费者配置 - 优化版本，解决CommitFailedError和连接超时问题
  consumer:
    enable_auto_commit: false          # 禁用自动提交，改为手动提交
    # auto_commit_interval_ms: 15000   # 手动提交时不需要此参数
    auto_offset_reset: earliest        # 没有偏移量时从头消费
    fetch_min_bytes: 1                 # 最小读取字节
    fetch_max_wait_ms: 1000            # 增加到1秒，给网络更多时间
    max_poll_records: 100              # 32C64G服务器可以处理更多消息
    key_deserializer: str              # key反序列化
    value_deserializer: str            # value反序列化
    
    # 静态成员配置 - 极大降低rebalance幅度
    enable_static_membership: true     # 启用静态成员特性
    
    # 新增：会话和网络超时配置（基于日志分析优化）
    session_timeout_ms: 90000          # 会话超时增加到90秒（解决频繁rebalance）
    request_timeout_ms: 1000000         # 请求超时增加到100秒（解决40s超时问题）
    heartbeat_interval_ms: 15000       # 心跳间隔增加到15秒
    max_poll_interval_ms: 600000       # 最大poll间隔增加到10分钟（UIE API处理时间长）
    connections_max_idle_ms: 540000    # 连接最大空闲时间9分钟
    retry_backoff_ms: 2000             # 重试间隔增加到2秒
    reconnect_backoff_ms: 1000         # 重连间隔1秒
    reconnect_backoff_max_ms: 10000    # 最大重连间隔10秒

# 向量配置
vector:
  api_url: http://*************:7777/bge-small  # 向量化API地址，请确认端口7777
  # vLLM OpenAI Embeddings 兼容配置（新增）
  openai_base_url: http://************/ca-inference/api/1970407153481326593/embed/v1  # OpenAI兼容API地址，请确认端口8002
  openai_model: bge-small-zh-v1
  dimension: 512
  similarity_threshold: 0.9
  max_results: 5

# 规则匹配配置
rule:
  max_distance: 50  # 从10增加到50
  enable_negation_check: true
  enable_punctuation_check: true

# 重试配置
retry:
  max_retries: 5
  retry_delay: 60
  exponential_backoff: true

# 模型配置 - 多实例负载均衡
# 每个服务实例通过环境变量覆盖API地址，实现真正的负载分散
models:
  llm:
    model_path: ""
    model: qwen3-14b
    model_server: http://************/ca-inference/api/1970402173936508930/llm/v1
    api_url: http://************/ca-inference/api/1970402173936508930/llm/v1/chat/completions  # 默认LLM-1，可通过LLM_API_URL环境变量覆盖
    api_key: ""
    timeout: 30
    max_retries: 3
    batch_size: 10
  
  uie:
    model_path: ""
    api_url: http://************:5000/ppuie  # 默认UIE-1，可通过UIE_API_URL环境变量覆盖
    api_key: ""
    timeout: 30
    max_retries: 3
    batch_size: 32
    batch_timeout: 5.0
    enable_batch_processing: true
  
  intent:
    model_path: ""
    api_url: http://************:6000/analyze  # 默认Intent-1，可通过INTENT_API_URL环境变量覆盖
    api_key: ""
    timeout: 15
    max_retries: 2
    batch_size: 32
    batch_timeout: 3.0
    enable_batch_processing: true
    # 注意：此API同时返回意图和情感分析结果


# 服务配置
services:
  text-processor:
    host: 0.0.0.0
    port: 8200
    debug: false
    log_level: INFO
    version: 1.0.0
  
  rule-matcher:
    host: 0.0.0.0
    port: 8110
    debug: false
    log_level: INFO
    version: 1.0.0
  
  vector-matcher:
    host: 0.0.0.0
    port: 8120
    debug: false
    log_level: INFO
    version: 1.0.0
  
  uie:
    host: 0.0.0.0
    port: 8140
    debug: false
    log_level: INFO
    version: 1.0.0
  
  llm:
    host: 0.0.0.0
    port: 8150
    debug: false
    log_level: INFO
    version: 1.0.0
  
  intent:
    host: 0.0.0.0
    port: 8160
    debug: false
    log_level: INFO
    version: 1.0.0
    # 合并了sentiment服务功能，统一提供意图和情感分析
  
  result-assembler:
    host: 0.0.0.0
    port: 8170
    debug: false
    log_level: INFO
    version: 1.0.0
  
  error-handler:
    host: 0.0.0.0
    port: 8130
    debug: false
    log_level: INFO
    version: 1.0.0
  
  gateway:
    host: 0.0.0.0
    port: 8100
    debug: false
    log_level: INFO
    version: 1.0.0
  
  brand-attribution:
    host: 0.0.0.0
    port: 8170
    debug: false
    log_level: INFO
    version: 1.0.0
  
  post-processor:
    host: 0.0.0.0
    port: 8180
    debug: false
    log_level: INFO
    version: 1.0.0

# 日志配置
logging:
  level: INFO
  dir: logs
  max_file_size: 10485760  # 10MB
  backup_count: 5
  console_output: true
  file_output: true
