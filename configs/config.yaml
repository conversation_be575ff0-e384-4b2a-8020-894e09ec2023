# VOC2.0智能打标系统配置文件

# 数据库配置
database:
  # StarRocks配置
  starrocks:
    host: *************
    port: 9030
    username: root
    password: ""
    database: voc_db
  
  # MySQL配置
  mysql:
    host: ************
    port: 30799
    username: root
    password: L7bzd1gmm+db
    database: voc_model
    database1: voc_ms_be
    charset: utf8mb4

# Elasticsearch配置
elasticsearch:
  hosts:
    - *************:9200
  username: ""
  password: ""
  timeout: 30

# Kafka配置
kafka:
  bootstrap_servers:
    - *************:5084
  client_id: voc-client
  group_id: voc-group
  auto_offset_reset: latest

# 向量配置
vector:
  api_url: http://*************:7777/bge-small
  # vLLM OpenAI Embeddings 兼容配置（新增）
  openai_base_url: http://*************:8002/v1
  openai_model: bge-small-zh-v1
  dimension: 512
  similarity_threshold: 0.7
  max_results: 5

# 规则匹配配置
rule:
  max_distance: 50  # 从10增加到50
  enable_negation_check: true
  enable_punctuation_check: true

# 重试配置
retry:
  max_retries: 5
  retry_delay: 60
  exponential_backoff: true

# 模型配置
models:
  llm:
    model: qwen3-14b
    model_path: ""
    api_url: http://localhost:8001/v1/chat/completions
    api_key: ""
    model_server: http://localhost:8001/v1
    timeout: 30
    max_retries: 3
    batch_size: 10
  
  uie:
    model_path: ""
    api_url: http://*************:5000/ppuie
    api_key: ""
    timeout: 30
    max_retries: 3
    batch_size: 32
    batch_timeout: 5.0
    enable_batch_processing: true
  
  intent:
    model_path: ""
    api_url: http://*************:5001/analyze
    api_key: ""
    timeout: 15
    max_retries: 2
    batch_size: 32
    batch_timeout: 3.0
    enable_batch_processing: true
    # 注意：此API同时返回意图和情感分析结果

# 服务配置
services:
  text-processor:
    host: 0.0.0.0
    port: 8200
    debug: false
    log_level: INFO
    version: 1.0.0
  
  rule-matcher:
    host: 0.0.0.0
    port: 8110
    debug: false
    log_level: INFO
    version: 1.0.0
  
  vector-matcher:
    host: 0.0.0.0
    port: 8120
    debug: false
    log_level: INFO
    version: 1.0.0
  
  uie:
    host: 0.0.0.0
    port: 8140
    debug: false
    log_level: INFO
    version: 1.0.0
  
  llm:
    host: 0.0.0.0
    port: 8150
    debug: false
    log_level: INFO
    version: 1.0.0
  
  intent:
    host: 0.0.0.0
    port: 8160
    debug: false
    log_level: INFO
    version: 1.0.0
    # 合并了sentiment服务功能，统一提供意图和情感分析
  
  result-assembler:
    host: 0.0.0.0
    port: 8170
    debug: false
    log_level: INFO
    version: 1.0.0
  
  error-handler:
    host: 0.0.0.0
    port: 8130
    debug: false
    log_level: INFO
    version: 1.0.0
  
  gateway:
    host: 0.0.0.0
    port: 8100
    debug: false
    log_level: INFO
    version: 1.0.0
  
  brand-attribution:
    host: 0.0.0.0
    port: 8170
    debug: false
    log_level: INFO
    version: 1.0.0
  
  post-processor:
    host: 0.0.0.0
    port: 8180
    debug: false
    log_level: INFO
    version: 1.0.0

# 日志配置
logging:
  level: INFO
  dir: logs
  max_file_size: 10485760  # 10MB
  backup_count: 5
  console_output: true
  file_output: true