# VOC2.0 部署指南

## 🚀 快速部署

### 运行环境要求
- Docker 和 Docker Compose
- Python 3.x (用于智能检测功能)
- Bash shell (推荐) 或兼容的shell

### 🎯 一键部署命令

#### 方法1：推荐使用 bash (最佳兼容性)
```bash
# 开发环境
bash scripts/deploy-env.sh dev

# 生产环境 (智能检测)
bash scripts/deploy-env.sh prod

# 扩展部署
bash scripts/deploy-env.sh scaled
```

#### 方法2：使用 sh (基础兼容)
```bash
# 如果系统不支持bash，可以使用sh
sh scripts/deploy-env.sh dev
sh scripts/deploy-env.sh prod
sh scripts/deploy-env.sh scaled
```

#### 方法3：直接执行 (需要执行权限)
```bash
# 给脚本添加执行权限
chmod +x scripts/deploy-env.sh

# 直接执行
./scripts/deploy-env.sh dev
./scripts/deploy-env.sh prod
./scripts/deploy-env.sh scaled
```

### 🔧 故障排除

#### 语法错误问题
如果遇到类似错误：
```
scripts/deploy-env.sh: 39: [[: not found
scripts/deploy-env.sh: 44: Syntax error: "(" unexpected
```

**解决方案：**
1. **使用bash代替sh**：`bash scripts/deploy-env.sh dev`
2. **检查shell版本**：某些系统的`sh`指向dash而非bash
3. **安装bash**：如果系统没有bash，需要先安装

#### 权限问题
```bash
# Linux/Mac 添加执行权限
chmod +x scripts/*.sh

# Windows 使用Git Bash或WSL
```

#### Docker问题
```bash
# 检查Docker是否运行
docker version

# 检查Docker Compose
docker compose version
```

### 📋 部署选项详解

#### 🔧 开发环境 (dev)
- **特点**：代码挂载，修改立即生效
- **用途**：日常开发、调试
- **基础设施**：连接外部服务
- **镜像**：voc-base:latest (基础环境)

#### 🏭 生产环境 (prod)
- **特点**：智能检测基础设施
- **自动选择**：
  - 检测到外部服务 → 连接外部
  - 无外部服务 → 启动Docker基础设施
- **镜像**：voc-services:latest (完整应用)

#### ⚡ 扩展部署 (scaled)
- **特点**：多实例高并发
- **服务实例**：
  - UIE: 3个实例
  - LLM: 2个实例  
  - Intent: 2个实例
- **要求**：外部API支持多端口

### 🎭 智能检测功能 (生产环境)

生产环境脚本会自动检测：

#### 外部服务检测
- Kafka: *************:5084
- Elasticsearch: *************:9200
- StarRocks: *************:9030

#### 决策逻辑
1. **外部服务全部可用** → 询问是否使用外部服务
2. **本地服务冲突** → 警告并询问是否继续
3. **无冲突** → 正常启动完整基础设施

### 📊 常用管理命令

#### 查看状态
```bash
# 开发环境
docker compose -f docker-compose.dev.yml ps

# 生产环境
docker compose -f docker-compose.yml ps
# 或 (如果使用外部服务)
docker compose -f docker-compose.external.yml ps

# 扩展部署
docker compose -f docker-compose.scaled.yml ps
```

#### 查看日志
```bash
# 查看所有服务日志
docker compose -f [compose-file] logs -f

# 查看特定服务日志
docker compose -f [compose-file] logs -f uie
```

#### 重启服务
```bash
# 重启特定服务
docker compose -f [compose-file] restart uie

# 重启所有服务
docker compose -f [compose-file] restart
```

#### 停止环境
```bash
# 停止服务
docker compose -f [compose-file] down

# 停止并删除数据(谨慎!)
docker compose -f [compose-file] down -v
```

### 🔍 部署验证

#### 检查服务健康状态
```bash
# API网关
curl http://localhost:8100/health

# 具体服务 (开发环境端口示例)
curl http://localhost:8140/health  # UIE
curl http://localhost:8150/health  # LLM
curl http://localhost:8160/health  # Intent
```

#### 检查服务访问
生产环境服务端口：
- API网关: 8100
- 文本处理: 8200  
- 规则匹配: 8300
- 向量匹配: 8400
- UIE: 8140
- LLM: 8500
- Intent: 8600
- 品牌归因: 8700
- 错误处理: 8800

### ⚠️ 注意事项

1. **端口冲突**：确保所需端口未被占用
2. **外部API**：确保外部API服务可访问
3. **资源要求**：扩展部署需要更多CPU和内存
4. **数据持久化**：生产环境数据会持久化保存
5. **代码修改**：
   - 开发环境：直接修改文件即可
   - 生产环境：需要重新构建镜像

### 🆘 获取帮助

```bash
# 查看帮助信息
bash scripts/deploy-env.sh --help

# 或
bash scripts/deploy-env.sh -h
```

如有问题，请检查：
1. 脚本执行权限
2. Docker服务状态
3. 网络连接
4. 外部API可用性
