#!/usr/bin/env python3
"""
简化的向量搜索测试脚本
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from shared.elasticsearch.es_client import ElasticsearchClient
from shared.config.config_manager import Config<PERSON>anager
from shared.utils.embedding_client import get_embedding_client

async def test_vector_search():
    """测试向量搜索"""
    
    print("开始测试向量搜索...")
    
    # 初始化配置
    config_manager = ConfigManager()
    es_config = config_manager.get_elasticsearch_config()
    
    # 初始化ES客户端
    es_client = ElasticsearchClient(es_config)
    await es_client.initialize()
    
    # 初始化向量化客户端
    embedding_client = get_embedding_client()
    
    try:
        # 1. 测试向量化
        test_text = "车辆外观"
        print(f"测试文本: {test_text}")
        
        vector = await embedding_client.embed_single(test_text)
        print(f"向量维度: {len(vector)}")
        print(f"向量前5个值: {vector[:5]}")
        
        # 2. 测试不同的查询方式
        
        # 方式1: 使用script_score查询
        print("\n=== 测试方式1: script_score查询 ===")
        query1 = {
            "query": {
                "script_score": {
                    "query": {"match_all": {}},
                    "script": {
                        "source": "cosineSimilarity(params.query_vector, 'vector') + 1.0",
                        "params": {"query_vector": vector}
                    }
                }
            },
            "size": 5,
            "_source": ["description_id", "sim_description", "vector"]
        }
        
        try:
            response1 = await es_client.client.search(
                index=es_client.DESCRIPTION_SYNONYMS_INDEX, 
                body=query1
            )
            print(f"方式1结果数量: {len(response1['hits']['hits'])}")
            for i, hit in enumerate(response1['hits']['hits']):
                print(f"  结果{i+1}: 分数={hit['_score']:.3f}, 描述={hit['_source'].get('sim_description', 'N/A')}")
        except Exception as e:
            print(f"方式1失败: {e}")
        
        # 方式2: 使用knn查询（如果ES版本支持）
        print("\n=== 测试方式2: knn查询 ===")
        query2 = {
            "knn": {
                "field": "vector",
                "query_vector": vector,
                "k": 5,
                "num_candidates": 10
            },
            "_source": ["description_id", "sim_description", "vector"]
        }
        
        try:
            response2 = await es_client.client.search(
                index=es_client.DESCRIPTION_SYNONYMS_INDEX, 
                body=query2
            )
            print(f"方式2结果数量: {len(response2['hits']['hits'])}")
            for i, hit in enumerate(response2['hits']['hits']):
                print(f"  结果{i+1}: 分数={hit['_score']:.3f}, 描述={hit['_source'].get('sim_description', 'N/A')}")
        except Exception as e:
            print(f"方式2失败: {e}")
        
        # 方式3: 使用function_score查询
        print("\n=== 测试方式3: function_score查询 ===")
        query3 = {
            "query": {
                "function_score": {
                    "query": {"match_all": {}},
                    "functions": [
                        {
                            "script_score": {
                                "script": {
                                    "source": "cosineSimilarity(params.query_vector, 'vector')",
                                    "params": {"query_vector": vector}
                                }
                            }
                        }
                    ]
                }
            },
            "size": 5,
            "_source": ["description_id", "sim_description", "vector"]
        }
        
        try:
            response3 = await es_client.client.search(
                index=es_client.DESCRIPTION_SYNONYMS_INDEX, 
                body=query3
            )
            print(f"方式3结果数量: {len(response3['hits']['hits'])}")
            for i, hit in enumerate(response3['hits']['hits']):
                print(f"  结果{i+1}: 分数={hit['_score']:.3f}, 描述={hit['_source'].get('sim_description', 'N/A')}")
        except Exception as e:
            print(f"方式3失败: {e}")
        
        # 3. 检查索引映射
        print("\n=== 检查索引映射 ===")
        try:
            mapping = await es_client.client.indices.get_mapping(
                index=es_client.DESCRIPTION_SYNONYMS_INDEX
            )
            vector_mapping = mapping[es_client.DESCRIPTION_SYNONYMS_INDEX]['mappings']['properties'].get('vector', {})
            print(f"向量字段映射: {vector_mapping}")
        except Exception as e:
            print(f"获取映射失败: {e}")
        
        # 4. 检查ES版本
        print("\n=== 检查ES版本 ===")
        try:
            info = await es_client.client.info()
            version = info.get('version', {}).get('number', 'unknown')
            print(f"ES版本: {version}")
        except Exception as e:
            print(f"获取版本失败: {e}")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await es_client.close()

if __name__ == "__main__":
    asyncio.run(test_vector_search()) 