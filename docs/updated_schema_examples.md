# VOC2.0智能打标系统 - 更新后的Schema示例

基于新的架构方案，以下是各个服务的输入输出schema示例：

## 1. 初始消息输入（voc_toModel_topic）

```json
{
  "topic_id": "0000001",
  "source_data": {
    "dataSource": "汽车之家",
    "domainListJson": ["汽车","客服","售后"],
    "productName": "长安CS75 PLUS",
    "populationGroup": "25-35岁男性",
    "businessType": "产品评价",
    "businessScene": "购车前调研",
    "textType": 1,
    "title": "CS75 PLUS使用一个月感受",
    "topic_text": "这个车的大灯真的很亮，晚上开车很安全，但是内饰的塑料感有点强，奇瑞的要好点",
    "optionScore": {"overall":8,"appearance":9,"interior":6},
    "position": 1,
    "forumSeries": "CS75系列",
    "brand": "长安",
    "series": "CS75 PLUS",
    "create_time": "2025-08-06 12:00:00"
  },
  "retry_count": 0,
  "error_type": "",
  "error_msg": "",
  "last_ts": ""
}
```

## 2. 分流后消息（llm_topic/uie_topic）

```json
{
  "topic_id": "0000001",
  "source_data": {
    "dataSource": "汽车之家",
    "domainListJson": ["汽车","客服","售后"],
    "productName": "长安CS75 PLUS",
    "populationGroup": "25-35岁男性",
    "businessType": "产品评价",
    "businessScene": "购车前调研",
    "textType": 1,
    "title": "CS75 PLUS使用一个月感受",
    "topic_text": "这个车的大灯真的很亮，晚上开车很安全，但是内饰的塑料感有点强，奇瑞的要好点",
    "optionScore": {"overall":8,"appearance":9,"interior":6},
    "position": 1,
    "forumSeries": "CS75系列",
    "brand": "长安",
    "series": "CS75 PLUS",
    "create_time": "2025-08-06 12:00:00"
  },
  "raw_split_text": [
    "这个车的大灯真的很亮，晚上开车很安全",
    "但是内饰的塑料感有点强，奇瑞的要好点"
  ],
  "cat_type": "uie",
  "retry_count": 0,
  "error_type": "",
  "error_msg": "",
  "last_ts": ""
}
```

## 3. UIE处理后输出（intent_topic）

```json
{
  "topic_id": "0000001",
  "source_data": {
    "dataSource": "汽车之家",
    "domainListJson": ["汽车","客服","售后"],
    "productName": "长安CS75 PLUS",
    "populationGroup": "25-35岁男性",
    "businessType": "产品评价",
    "businessScene": "购车前调研",
    "textType": 1,
    "title": "CS75 PLUS使用一个月感受",
    "topic_text": "这个车的大灯真的很亮，晚上开车很安全，但是内饰的塑料感有点强，奇瑞的要好点",
    "optionScore": {"overall":8,"appearance":9,"interior":6},
    "position": 1,
    "forumSeries": "CS75系列",
    "brand": "长安",
    "series": "CS75 PLUS",
    "create_time": "2025-08-06 12:00:00"
  },
  "cat_type": "uie",
  "retry_count": 0,
  "error_type": "",
  "error_msg": "",
  "last_ts": "",
  "result": [
    {
      "评价对象": [
        {
          "text": "大灯",
          "start": 4,
          "end": 6,
          "relations": {
            "segment": [{"text": ""}],
            "品牌车系": [{"text": ""}],
            "情感倾向": [{"text": ""}],
            "意图": [{"text": ""}],
            "观点": [{
              "text": "很亮",
              "start": 8,
              "end": 10
            }],
            "normalized_subject": {
              "id": "SUBJ_000123",
              "text": "大灯",
              "score": 0.92
            },
            "normalized_description": {
              "id": "DESC_100045",
              "text": "好看",
              "score": 0.90
            },
            "normalized_opinion": {
              "id": "OP_00000027",
              "text": "大灯-好看"
            },
            "std_viewpoint": {
              "id": "CA_STD_011234",
              "text": "大灯好看",
              "confidence": 0.87
            }
          }
        }
      ]
    }
  ]
}
```

## 4. 意图情感分析后输出（brand_attribution_topic）

```json
{
  "topic_id": "0000001",
  "source_data": {
    "dataSource": "汽车之家",
    "domainListJson": ["汽车","客服","售后"],
    "productName": "长安CS75 PLUS",
    "populationGroup": "25-35岁男性",
    "businessType": "产品评价",
    "businessScene": "购车前调研",
    "textType": 1,
    "title": "CS75 PLUS使用一个月感受",
    "topic_text": "这个车的大灯真的很亮，晚上开车很安全，但是内饰的塑料感有点强，奇瑞的要好点",
    "optionScore": {"overall":8,"appearance":9,"interior":6},
    "position": 1,
    "forumSeries": "CS75系列",
    "brand": "长安",
    "series": "CS75 PLUS",
    "create_time": "2025-08-06 12:00:00"
  },
  "cat_type": "uie",
  "retry_count": 0,
  "error_type": "",
  "error_msg": "",
  "last_ts": "",
  "result": [
    {
      "评价对象": [
        {
          "text": "大灯",
          "start": 4,
          "end": 6,
          "relations": {
            "segment": [{"text": "这个车的大灯真的很亮"}],
            "品牌车系": [{"text": ""}],
            "情感倾向": [{"text": "正面"}],
            "意图": [{"text": "表扬"}],
            "观点": [{
              "text": "很亮",
              "start": 8,
              "end": 10
            }],
            "normalized_subject": {
              "id": "SUBJ_000123",
              "text": "大灯",
              "score": 0.92
            },
            "normalized_description": {
              "id": "DESC_100045",
              "text": "好看",
              "score": 0.90
            },
            "normalized_opinion": {
              "id": "OP_00000027",
              "text": "大灯-好看"
            },
            "std_viewpoint": {
              "id": "CA_STD_011234",
              "text": "大灯好看",
              "confidence": 0.87
            }
          }
        }
      ]
    }
  ]
}
```

## 5. 最终输出（result_topic）

```json
{
  "topic_id": "0000001",
  "source_data": {
    "dataSource": "汽车之家",
    "domainListJson": ["汽车","客服","售后"],
    "productName": "长安CS75 PLUS",
    "populationGroup": "25-35岁男性",
    "businessType": "产品评价",
    "businessScene": "购车前调研",
    "textType": 1,
    "title": "CS75 PLUS使用一个月感受",
    "topic_text": "这个车的大灯真的很亮，晚上开车很安全，但是内饰的塑料感有点强，奇瑞的要好点",
    "optionScore": {"overall":8,"appearance":9,"interior":6},
    "position": 1,
    "forumSeries": "CS75系列",
    "brand": "长安",
    "series": "CS75 PLUS",
    "create_time": "2025-08-06 12:00:00"
  },
  "cat_type": "uie",
  "retry_count": 0,
  "error_type": "",
  "error_msg": "",
  "last_ts": "",
  "result": [
    {
      "评价对象": [
        {
          "text": "大灯",
          "start": 4,
          "end": 6,
          "relations": {
            "segment": [{"text": "这个车的大灯真的很亮"}],
            "品牌车系": [{"text": "长安无车系"}],
            "情感倾向": [{"text": "正面"}],
            "意图": [{"text": "表扬"}],
            "观点": [{
              "text": "很亮",
              "start": 8,
              "end": 10
            }],
            "normalized_subject": {
              "id": "SUBJ_000123",
              "text": "大灯",
              "score": 0.92
            },
            "normalized_description": {
              "id": "DESC_100045",
              "text": "好看",
              "score": 0.90
            },
            "normalized_opinion": {
              "id": "OP_00000027",
              "text": "大灯-好看"
            },
            "std_viewpoint": {
              "id": "CA_STD_011234",
              "text": "大灯好看",
              "confidence": 0.87
            }
          }
        },
        {
          "text": "内饰",
          "start": 12,
          "end": 14,
          "relations": {
            "segment": [{"text": "内饰的塑料感有点强"}],
            "品牌车系": [{"text": "长安无车系"}],
            "情感倾向": [{"text": "负面"}],
            "意图": [{"text": "抱怨"}],
            "观点": [{
              "text": "塑料感强",
              "start": 15,
              "end": 19
            }],
            "normalized_subject": {
              "id": "SUBJ_000210",
              "text": "内饰",
              "score": 0.94
            },
            "normalized_description": {
              "id": "DESC_200012",
              "text": "不好看",
              "score": 0.89
            },
            "normalized_opinion": {
              "id": "OP_00009001",
              "text": "内饰-不好看"
            },
            "std_viewpoint": {
              "id": "CA_STD_022678",
              "text": "内饰不好看",
              "confidence": 0.83
            }
          }
        }
      ]
    }
  ]
}
```

## 6. 错误消息格式

```json
{
  "topic_id": "0000001",
  "source_data": {
    "dataSource": "汽车之家",
    "domainListJson": ["汽车","客服","售后"],
    "productName": "长安CS75 PLUS",
    "populationGroup": "25-35岁男性",
    "businessType": "产品评价",
    "businessScene": "购车前调研",
    "textType": 1,
    "title": "CS75 PLUS使用一个月感受",
    "topic_text": "这个车的大灯真的很亮，晚上开车很安全，但是内饰的塑料感有点强，奇瑞的要好点",
    "optionScore": {"overall":8,"appearance":9,"interior":6},
    "position": 1,
    "forumSeries": "CS75系列",
    "brand": "长安",
    "series": "CS75 PLUS",
    "create_time": "2025-08-06 12:00:00"
  },
  "retry_count": 1,
  "error_type": "model_timeout",
  "error_msg": "TimeoutError: inference exceeded 10s",
  "last_ts": "2025-07-31T17:20:00"
}
```

## 主要变化总结

1. **输入数据结构**：将原来的`topic_text`扁平字段改为`source_data`对象，包含完整的元数据信息
2. **标准化信息**：在结果中增加了`normalized_subject`、`normalized_description`、`normalized_opinion`、`std_viewpoint`等标准化字段
3. **位置信息**：增加了`start`、`end`字段来标记文本中的位置
4. **品牌车系判断**：增加了专门的brand_attribution_topic流程
5. **数据库适配**：错误记录库等结构按新架构方案调整
6. **向量化支持**：在实体和描述近义词中增加了vector字段支持

这些更新确保了系统完全符合新的VOC2.0智能打标架构方案。

