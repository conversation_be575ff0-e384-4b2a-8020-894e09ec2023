# 数据库设计文档

## StarRocks 表结构设计

### 1. 品牌车系车型数据库 (brand_series_model)
存储汽车品牌、车系和车型的层级关系及近义词信息。

```sql
CREATE TABLE brand_series_model (
    id BIGINT NOT NULL AUTO_INCREMENT,
    brand VARCHAR(100) NOT NULL COMMENT '品牌名称',
    series VARCHAR(200) NOT NULL COMMENT '车系名称',
    model VARCHAR(300) COMMENT '车型名称',
    synonyms JSON COMMENT '近义词列表',
    is_active TINYINT DEFAULT 1 COMMENT '是否激活',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
) ENGINE=OLAP
DUPLICATE KEY(id)
DISTRIBUTED BY HASH(id) BUCKETS 10;
```

### 2. 长安标准观点库 (changan_standard_opinions)
存储长安的标准观点，用于映射到业务部门标签。

```sql
CREATE TABLE changan_standard_opinions (
    standard_opinion_id BIGINT NOT NULL AUTO_INCREMENT,
    standard_entity VARCHAR(200) NOT NULL COMMENT '标准主体',
    standard_description VARCHAR(500) NOT NULL COMMENT '标准描述',
    business_tags JSON COMMENT '业务部门标签映射',
    category VARCHAR(100) COMMENT '观点分类',
    is_active TINYINT DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (standard_opinion_id)
) ENGINE=OLAP
DUPLICATE KEY(standard_opinion_id)
DISTRIBUTED BY HASH(standard_opinion_id) BUCKETS 10;
```

### 3. 观点近义词库 (opinion_synonyms)
存储观点的近义词，包含主体和描述的分离录入。

```sql
CREATE TABLE opinion_synonyms (
    id BIGINT NOT NULL AUTO_INCREMENT,
    add_entity VARCHAR(200) NOT NULL COMMENT '添加的主体',
    add_description VARCHAR(500) NOT NULL COMMENT '添加的描述',
    standard_opinion_id BIGINT NOT NULL COMMENT '对应的标准观点ID',
    is_active TINYINT DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    INDEX idx_standard_opinion_id (standard_opinion_id)
) ENGINE=OLAP
DUPLICATE KEY(id)
DISTRIBUTED BY HASH(id) BUCKETS 10;
```

### 4. 主体近义词库 (entity_synonyms)
存储主体的近义词及其向量表示。

```sql
CREATE TABLE entity_synonyms (
    entity_id BIGINT NOT NULL AUTO_INCREMENT,
    sim_entity VARCHAR(200) NOT NULL COMMENT '近似主体',
    vector_id VARCHAR(100) COMMENT 'ES中向量ID',
    normalized_entity VARCHAR(200) NOT NULL COMMENT '规范化主体',
    standard_opinion_id_list JSON NOT NULL COMMENT '标准观点ID列表',
    is_active TINYINT DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (entity_id),
    INDEX idx_sim_entity (sim_entity),
    INDEX idx_normalized_entity (normalized_entity)
) ENGINE=OLAP
DUPLICATE KEY(entity_id)
DISTRIBUTED BY HASH(entity_id) BUCKETS 10;
```

### 5. 描述近义词库 (description_synonyms)
存储描述的近义词及其向量表示。

```sql
CREATE TABLE description_synonyms (
    description_id BIGINT NOT NULL AUTO_INCREMENT,
    sim_description VARCHAR(500) NOT NULL COMMENT '近似描述',
    vector_id VARCHAR(100) COMMENT 'ES中向量ID',
    normalized_description VARCHAR(500) NOT NULL COMMENT '规范化描述',
    standard_opinion_id BIGINT NOT NULL COMMENT '标准观点ID',
    is_active TINYINT DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (description_id),
    INDEX idx_sim_description (sim_description),
    INDEX idx_standard_opinion_id (standard_opinion_id)
) ENGINE=OLAP
DUPLICATE KEY(description_id)
DISTRIBUTED BY HASH(description_id) BUCKETS 10;
```

### 6. 新词库 (new_words)
存储模糊匹配未成功的主体+描述。

```sql
CREATE TABLE new_words (
    id BIGINT NOT NULL AUTO_INCREMENT,
    new_entity VARCHAR(200) NOT NULL COMMENT '新主体',
    new_description VARCHAR(500) NOT NULL COMMENT '新描述',
    frequency INT DEFAULT 1 COMMENT '出现频次',
    status ENUM('pending', 'processing', 'approved', 'rejected') DEFAULT 'pending',
    processed_by VARCHAR(100) COMMENT '处理人',
    processed_at DATETIME COMMENT '处理时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    INDEX idx_status (status),
    INDEX idx_entity_desc (new_entity, new_description)
) ENGINE=OLAP
DUPLICATE KEY(id)
DISTRIBUTED BY HASH(id) BUCKETS 10;
```

### 7. 错误库 (error_records)
存储处理失败的数据记录。

```sql
CREATE TABLE error_records (
    id BIGINT NOT NULL AUTO_INCREMENT,
    topic_id VARCHAR(50) NOT NULL COMMENT 'Topic ID',
    topic_text TEXT NOT NULL COMMENT '原始文本',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    error_type VARCHAR(100) COMMENT '错误类型',
    error_msg TEXT COMMENT '错误信息',
    last_ts DATETIME COMMENT '最后处理时间',
    final_status ENUM('pending', 'fixed', 'skipped', 'manual_required') DEFAULT 'pending',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    INDEX idx_topic_id (topic_id),
    INDEX idx_error_type (error_type),
    INDEX idx_final_status (final_status)
) ENGINE=OLAP
DUPLICATE KEY(id)
DISTRIBUTED BY HASH(id) BUCKETS 10;
```

### 8. 否定词表 (negation_words)
存储否定词汇，用于规则匹配。

```sql
CREATE TABLE negation_words (
    id BIGINT NOT NULL AUTO_INCREMENT,
    word VARCHAR(50) NOT NULL COMMENT '否定词',
    word_type ENUM('prefix', 'suffix', 'standalone') NOT NULL COMMENT '词类型',
    weight FLOAT DEFAULT 1.0 COMMENT '权重',
    is_active TINYINT DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY uk_word (word)
) ENGINE=OLAP
DUPLICATE KEY(id)
DISTRIBUTED BY HASH(id) BUCKETS 5;
```

## 索引策略
- 主键索引：所有表都有自增主键
- 业务索引：根据查询频率创建相应索引
- 分布键：使用主键进行HASH分布
- 分桶数：根据表大小设置合适的分桶数量