# Elasticsearch 设计文档 (符合VOC2.0架构)

## 索引设计 - 按架构方案重新设计

### 1. 长安标准观点库 (changan_standard_opinions)
**目标：1.1w条标准观点，用于映射到业务部门标签**

```json
{
  "mappings": {
    "properties": {
      "standard_opinion_id": {
        "type": "keyword"
      },
      "standard_entity": {
        "type": "text",
        "analyzer": "ik_max_word",
        "search_analyzer": "ik_smart",
        "fields": {"keyword": {"type": "keyword"}}
      },
      "standard_description": {
        "type": "text", 
        "analyzer": "ik_max_word",
        "search_analyzer": "ik_smart",
        "fields": {"keyword": {"type": "keyword"}}
      },
      "standard_opinion": {
        "type": "text",
        "analyzer": "ik_max_word",
        "search_analyzer": "ik_smart"
      },
      "business_tags": {
        "type": "keyword"
      },
      "category": {
        "type": "keyword"
      },
      "vector": {
        "type": "dense_vector",
        "dims": 768,
        "index": true,
        "similarity": "cosine"
      },
      "is_active": {
        "type": "boolean"
      },
      "created_at": {
        "type": "date"
      }
    }
  },
  "settings": {
    "number_of_shards": 3,
    "number_of_replicas": 1,
    "analysis": {
      "analyzer": {
        "chinese_analyzer": {
          "type": "custom",
          "tokenizer": "ik_max_word",
          "filter": ["lowercase"]
        }
      }
    }
  }
}
```

### 2. 观点近义词库 (opinion_synonyms)
**原始库：主体+描述组合录入，映射到标准观点**
**架构字段：Add_entity | Add_description | Standard_opinion | Standard_opinion_id**

```json
{
  "mappings": {
    "properties": {
      "id": {
        "type": "keyword"
      },
      "add_entity": {
        "type": "text",
        "analyzer": "ik_max_word",
        "search_analyzer": "ik_smart",
        "fields": {"keyword": {"type": "keyword"}}
      },
      "add_description": {
        "type": "text",
        "analyzer": "ik_max_word", 
        "search_analyzer": "ik_smart",
        "fields": {"keyword": {"type": "keyword"}}
      },
      "standard_opinion": {
        "type": "text",
        "analyzer": "ik_max_word",
        "search_analyzer": "ik_smart"
      },
      "standard_opinion_id": {
        "type": "keyword"
      },
      "is_active": {
        "type": "boolean"
      },
      "created_at": {
        "type": "date"
      }
    }
  },
  "settings": {
    "number_of_shards": 2,
    "number_of_replicas": 1,
    "analysis": {
      "analyzer": {
        "chinese_analyzer": {
          "type": "custom",
          "tokenizer": "ik_max_word",
          "filter": ["lowercase"]
        }
      }
    }
  }
}
```

### 3. 主体近义词库 (entity_synonyms) 
**从观点近义词库拆分，含向量，一对多关系**
**架构字段：Entity_id | Sim_entity | vector | Normoalized_entity | Standard_opinion_id_list**

```json
{
  "mappings": {
    "properties": {
      "entity_id": {
        "type": "keyword"
      },
      "sim_entity": {
        "type": "text",
        "analyzer": "ik_max_word",
        "search_analyzer": "ik_smart",
        "fields": {"keyword": {"type": "keyword"}}
      },
      "normalized_entity": {
        "type": "keyword"
      },
      "vector": {
        "type": "dense_vector",
        "dims": 768,
        "index": true,
        "similarity": "cosine"
      },
      "standard_opinion_id_list": {
        "type": "keyword"
      },
      "is_active": {
        "type": "boolean"
      },
      "created_at": {
        "type": "date"
      }
    }
  },
  "settings": {
    "number_of_shards": 3,
    "number_of_replicas": 1,
    "analysis": {
      "analyzer": {
        "chinese_analyzer": {
          "type": "custom",
          "tokenizer": "ik_max_word",
          "filter": ["lowercase"]
        }
      }
    }
  }
}
```

### 4. 描述近义词库 (description_synonyms)
**从观点近义词库拆分，含向量，一对一关系**
**架构字段：Description_id | Sim_description | vector | Normoalized_description | Standard_opinion_id**

```json
{
  "mappings": {
    "properties": {
      "description_id": {
        "type": "keyword"
      },
      "sim_description": {
        "type": "text",
        "analyzer": "ik_max_word",
        "search_analyzer": "ik_smart",
        "fields": {"keyword": {"type": "keyword"}}
      },
      "normalized_description": {
        "type": "keyword"
      },
      "vector": {
        "type": "dense_vector",
        "dims": 768,
        "index": true,
        "similarity": "cosine"
      },
      "standard_opinion_id": {
        "type": "keyword"
      },
      "is_active": {
        "type": "boolean"
      },
      "created_at": {
        "type": "date"
      }
    }
  },
  "settings": {
    "number_of_shards": 3,
    "number_of_replicas": 1,
    "analysis": {
      "analyzer": {
        "chinese_analyzer": {
          "type": "custom",
          "tokenizer": "ik_max_word",
          "filter": ["lowercase"]
        }
      }
    }
  }
}
```

### 5. 品牌车系库 (brand_series)
**品牌车系及近义词，用于分流判断**

```json
{
  "mappings": {
    "properties": {
      "id": {
        "type": "keyword"
      },
      "brand": {
        "type": "text",
        "analyzer": "ik_max_word",
        "search_analyzer": "ik_smart",
        "fields": {"keyword": {"type": "keyword"}}
      },
      "series": {
        "type": "text",
        "analyzer": "ik_max_word",
        "search_analyzer": "ik_smart",
        "fields": {"keyword": {"type": "keyword"}}
      },
      "model": {
        "type": "text",
        "analyzer": "ik_max_word",
        "search_analyzer": "ik_smart"
      },
      "synonyms": {
        "type": "text",
        "analyzer": "ik_max_word"
      },
      "full_text": {
        "type": "text",
        "analyzer": "ik_max_word"
      },
      "is_active": {
        "type": "boolean"
      },
      "created_at": {
        "type": "date"
      }
    }
  },
  "settings": {
    "number_of_shards": 2,
    "number_of_replicas": 1,
    "analysis": {
      "analyzer": {
        "chinese_analyzer": {
          "type": "custom",
          "tokenizer": "ik_max_word",
          "filter": ["lowercase"]
        }
      }
    }
  }
}
```

### 6. 新词库 (new_words)
**模糊匹配失败的主体+描述**
**架构字段：New_Entity | New_description**

```json
{
  "mappings": {
    "properties": {
      "id": {
        "type": "keyword"
      },
      "new_entity": {
        "type": "text",
        "analyzer": "ik_max_word",
        "search_analyzer": "ik_smart",
        "fields": {"keyword": {"type": "keyword"}}
      },
      "new_description": {
        "type": "text",
        "analyzer": "ik_max_word",
        "search_analyzer": "ik_smart",
        "fields": {"keyword": {"type": "keyword"}}
      },
      "original_text": {
        "type": "text",
        "analyzer": "ik_max_word"
      },
      "topic_id": {
        "type": "keyword"
      },
      "failure_reason": {
        "type": "text"
      },
      "created_at": {
        "type": "date"
      }
    }
  },
  "settings": {
    "number_of_shards": 2,
    "number_of_replicas": 1,
    "analysis": {
      "analyzer": {
        "chinese_analyzer": {
          "type": "custom",
          "tokenizer": "ik_max_word",
          "filter": ["lowercase"]
        }
      }
    }
  }
}
```

### 7. 错误库 (error_records)
**超过5次重试失败的数据**
**架构字段：Topic_id | Topic_text | Retry_count | Error_type | Last_ts | Final_status**

```json
{
  "mappings": {
    "properties": {
      "topic_id": {
        "type": "keyword"
      },
      "topic_text": {
        "type": "text",
        "analyzer": "ik_max_word"
      },
      "retry_count": {
        "type": "integer"
      },
      "error_type": {
        "type": "keyword"
      },
      "last_ts": {
        "type": "date"
      },
      "final_status": {
        "type": "keyword"
      },
      "error_details": {
        "type": "text"
      },
      "created_at": {
        "type": "date"
      }
    }
  },
  "settings": {
    "number_of_shards": 1,
    "number_of_replicas": 1,
    "analysis": {
      "analyzer": {
        "chinese_analyzer": {
          "type": "custom",
          "tokenizer": "ik_max_word",
          "filter": ["lowercase"]
        }
      }
    }
  }
}
```

## 📊 **重要变化说明**：

### ✅ **新增核心索引**：
1. **`changan_standard_opinions`** - 1.1w条长安标准观点（含向量）
2. **`opinion_synonyms`** - 观点近义词原始库  
3. **`new_words`** - 新词收集库
4. **`error_records`** - 错误处理库

### 🔄 **重命名索引**：
- `entity_vectors` → `entity_synonyms` （主体近义词库）
- `description_vectors` → `description_synonyms` （描述近义词库）

### 🗑️ **删除索引**：
- `rule_patterns` （架构中未明确提及，暂时移除）

### 🎯 **向量匹配流程**：
1. **规则匹配**：`entity_synonyms` 精确匹配 → `description_synonyms` 距离匹配
2. **模糊匹配**：`entity_synonyms` 向量匹配 → `description_synonyms` 向量匹配
3. **失败收集**：匹配失败 → `new_words`

### 📋 **向量化库清单**（按架构要求）：
1. **长安标准观点库** - 需要向量化 ✅
2. **主体近义词库** - 需要向量化 ✅  
3. **描述近义词库** - 需要向量化 ✅

