# VOC2.0 智能打标系统 - 开发者指南

> 基于VOC2.0智能打标（扫评部分）流程架构方案的完整微服务系统

[![Python](https://img.shields.io/badge/python-3.9+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com)
[![Docker](https://img.shields.io/badge/docker-ready-blue.svg)](https://docker.com)

## 📖 项目概述

VOC2.0智能打标系统是一个面向汽车行业的用户观点挖掘和情感分析系统。系统采用微服务架构，通过规则匹配和向量相似度的混合策略，自动识别用户评价中的观点主体、描述、情感倾向和意图。

### 🎯 核心功能流程

```
原始文本 → 文本预处理 → 智能分流 → 观点抽取 → 结果组装 → 情感意图分析 → 最终输出
```

**处理逻辑**：
1. **文本预处理** → 粗分句，统计品牌车系数量
2. **智能分流** → 车系数量 ≥3 走LLM，<3 走UIE  
3. **观点抽取** → 规则匹配 + 向量匹配，失败存入新词库
4. **结果组装** → 标准化输出格式，补充缺失信息
5. **情感意图** → 情感分析 + 意图识别，完善结构化输出

## 🏗️ 系统架构

### 算法后端微服务（10个核心服务）

| 服务名 | 端口 | 核心职责 | 主要函数 |
|--------|------|----------|----------|
| **gateway** | 8000 | API网关、路由转发 | `forward_request()`, `submit_processing_task()` |
| **text-processor** | 8100 | 文本预处理、分流决策 | `process_initial_message()`, `count_brand_series_matches()` |
| **rule-matcher** | 8110 | 规则精确匹配 | `rule_match_text()`, `match_entity_exact()` |
| **vector-matcher** | 8120 | 向量模糊匹配 | `fuzzy_match_text()`, `match_entity_vector()` |
| **llm** | 8130 | 大模型观点抽取 | `call_llm_api()`, `build_llm_prompt()` |
| **uie** | 8140 | UIE模型实体抽取 | `call_uie_api()`, `extract_entity_description_pairs()` |
| **intent** | 8150 | 意图识别分析 | `call_intent_api()`, `analyze_intent_by_rules()` |
| **sentiment** | 8160 | 情感倾向分析 | `call_sentiment_api()`, `analyze_sentiment_by_rules()` |
| **result-assembler** | 8170 | 结果组装格式化 | `assemble_result_from_matches()` |
| **error-handler** | 8180 | 错误处理重试 | `process_error_message()`, `schedule_retry()` |

### 技术栈

| 组件 | 技术选型 | 版本 | 作用 | 算法后端优化 |
|------|----------|------|------|--------------|
| **Web框架** | FastAPI + Uvicorn | 0.104+ | 高性能异步API | ✅ 10个核心微服务 |
| **数据存储** | StarRocks | latest | OLAP数据仓库 | ✅ 精简到2个表 |
| **搜索引擎** | Elasticsearch | 8.11+ | 向量搜索、全文检索 | ✅ 5个核心索引（512维） |
| **消息队列** | Apache Kafka | 7.4+ | 异步消息处理 | ✅ 8个partition（轻量级） |
| **机器学习** | sentence-transformers | 2.2+ | 文本向量化 | ✅ 512维向量 |
| **容器化** | Docker + Compose | 20.10+ | 服务容器化 | ✅ 一键部署脚本 |

### 算法后端数据存储（轻量级架构）

#### ✅ Elasticsearch索引（5个）- 完全按架构文档设计

| 索引名称 | 用途 | 向量搜索 | 表结构对应 |
|---------|------|----------|----------|
| `voc_standard_opinion` | 长安标准观点库 | ✅ 512维 | 归一化观点到标准观点映射 |
| `voc_entity_synonym` | 主体近义词库 | ✅ 512维 | Entity_id\|Sim_entity\|vector\|Normalized_entity\|Standard_opinion_id_list |
| `voc_description_synonym` | 描述近义词库 | ✅ 512维 | Description_id\|Sim_description\|vector\|Normalized_description\|Standard_opinion_id |
| `voc_new_words` | 新词库 | ✅ 512维 | 新词发现 + 向量推荐到标准观点 |
| `voc_brand_series_model` | 品牌车系车型库 | ❌ | 精确匹配 + 全文搜索（比StarRocks快） |

**🔍 ES专长**: 所有实时搜索和向量匹配，3个向量索引支持ANN语义搜索

#### ✅ StarRocks数据表（2个）- 精简到核心必要

| 表名称 | 用途 | 表结构对应 |
|--------|------|----------|
| `opinion_synonym` | 观点近义词库 | Add_entity\|Add_description\|Standard_opinion\|Standard_opinion_id |
| `error_records` | 错误记录库 | Topic_id\|Topic_text\|Retry_count\|Error_type\|Last_ts\|Final_status |

**💾 StarRocks专长**: 
- 长安运维手动维护的观点映射数据
- 超过5次重试失败的错误数据统一存储

#### ✅ Kafka Topics（8个）- 轻量级设计
- 每个topic **1个partition** 
- 总计 **8个partitions** (资源节省71%)
- 完整消息流: `voc_toModel_topic` → 算法处理 → `result_topic`

| Topic名称 | 用途 |
|-----------|------|
| `voc_toModel_topic` | 原始任务入口 |
| `llm_topic` | LLM处理任务 |
| `uie_topic` | UIE处理任务 |
| `intent_topic` | 意图情感识别 |
| `brand_attribution_topic` | 品牌归属判断 |
| `result_topic` | 结果输出 |
| `model_error_topic` | 错误处理 |
| `model_retry_topic` | 重试调度 |

## 📁 项目结构

```
changan_voc/
├── services/                    # 微服务实现（10个服务）
│   ├── gateway/                # API网关
│   ├── text-processor/         # 文本处理
│   ├── rule-matcher/           # 规则匹配
│   ├── vector-matcher/         # 向量匹配
│   ├── llm/                    # 大模型服务
│   ├── uie/                    # UIE模型服务
│   ├── intent/                 # 意图识别
│   ├── sentiment/              # 情感分析
│   ├── result-assembler/       # 结果组装
│   └── error-handler/          # 错误处理
├── shared/                     # 共享组件
│   ├── base/                   # 基础服务类
│   ├── models/schemas.py       # 数据模型（30+模型）
│   ├── database/starrocks_client.py  # StarRocks客户端
│   ├── elasticsearch/es_client.py    # ES客户端
│   ├── kafka/kafka_client.py         # Kafka客户端
│   └── utils/                  # 工具函数（配置、日志、向量化）
├── configs/config.yaml         # 统一配置（外部服务地址）
├── scripts/
│   ├── test/                   # 算法后端部署脚本
│   │   ├── deploy_all_optimized.py      # 一键部署脚本
│   │   ├── create_kafka_topics_lightweight.py    # 轻量级Kafka
│   │   ├── create_elasticsearch_indices_optimized.py  # 优化ES索引
│   │   ├── create_starrocks_optimized.py          # 优化StarRocks表
│   │   └── test_optimized_deployment.py          # 部署验证脚本
│   └── production/             # 生产环境脚本（待定）
├── docs/                      # 技术文档
│   ├── architecture_summary.md # 架构总结
│   ├── database_design.md      # 数据库设计
│   └── elasticsearch_design.md # ES索引设计
├── future-modules/            # 未来模块预留
│   ├── intelligent-review/    # 智能回评
│   ├── intelligent-qa/        # 智能问数
│   └── intelligent-report/    # 智能报表
├── docker-compose.yml         # 容器编排（本地开发）
├── docker-compose.scaled.yml  # 扩展版容器编排
├── requirements.txt          # Python依赖
├── ALGORITHM_BACKEND_ARCHITECTURE_SUMMARY.md  # 算法后端架构总结
└── README.md                 # 本文档
```

## 🚀 快速开始

### 环境要求
- Python 3.9+
- Docker 20.10+
- Docker Compose 2.0+
- 8GB+ 内存，20GB+ 磁盘

### 算法后端一键部署（推荐）
```bash
# 1. 克隆项目
git clone <repository-url>
cd changan_voc

# 2. 算法后端一键部署
cd scripts/test
python3 deploy_all_optimized.py

# 等待2-3分钟，自动完成所有部署步骤
# ✅ Kafka 8个partition + ES 5个索引 + StarRocks 2个表
```

### 验证算法后端部署
```bash
# 1. 运行验证脚本
cd scripts/test
python3 test_optimized_deployment.py

# 2. 手动检查系统状态
curl http://localhost:8000/system/health

# 3. 测试算法后端API
curl -X POST http://localhost:8000/api/v1/process \
  -H "Content-Type: application/json" \
  -d '{"topic_id":"test001","topic_text":"长安UNI-T的大灯很好看"}'
```

### 算法后端本地开发模式
```bash
# 1. Python环境
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# 2. 配置外部服务连接
# 编辑 configs/config.yaml
# - StarRocks: *************:9030
# - Elasticsearch: *************:9200
# - Kafka: *************:5084
# - Embedding: *************:7777

# 3. 初始化算法后端数据库
cd scripts/test
python3 create_kafka_topics_lightweight.py
python3 create_elasticsearch_indices_optimized.py
python3 create_starrocks_optimized.py

# 4. 启动单个服务调试
cd services/text-processor
python main.py
```

## 🔧 开发指南

### 代码结构模式

#### 服务主程序（标准模板）
```python
# services/{service-name}/main.py
class ServiceClass:
    def __init__(self):
        self.config_manager = get_config_manager()
        
    async def initialize(self):
        # 初始化客户端
        
    async def process_message(self, message_data: dict):
        # 业务逻辑处理
        
    async def start_consumers(self):
        # 启动Kafka消费者
        
    async def health_check(self):
        # 健康检查

app = FastAPI(lifespan=lifespan)

@app.get("/health")
async def health_check():
    return await service.health_check()
```

#### 数据模型定义
```python
# shared/models/schemas.py
class BaseKafkaMessage(BaseModel):
    topic_id: str
    topic_text: str
    retry_count: int = 0
    error_type: str = ""
    error_msg: str = ""

class OpinionMatch(BaseModel):
    original_entity: str
    original_description: str
    standard_entity: str
    standard_description: str
    confidence: float
    match_type: str  # exact/fuzzy
```

### 添加新功能

#### 1. 新增微服务
```bash
# 创建目录
mkdir services/new-service

# 复制模板
cp services/text-processor/main.py services/new-service/

# 配置服务（configs/config.yaml）
services:
  new-service:
    port: 8190

# 更新编排（docker-compose.yml）
new-service:
  build: ...
  ports: ["8190:8190"]

# 更新部署脚本
# 在scripts/deploy.sh中添加服务
```

#### 2. 扩展数据模型
```python
# shared/models/schemas.py
class NewModel(BaseModel):
    name: str
    data: Dict[str, Any]
    created_at: Optional[datetime] = None
```

#### 3. 添加数据库表
```sql
-- scripts/init_database.sql
CREATE TABLE new_table (
    id BIGINT AUTO_INCREMENT,
    name VARCHAR(200),
    data JSON,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
) ENGINE=OLAP
DISTRIBUTED BY HASH(id) BUCKETS 10;
```

## 🧪 测试调试

### 健康检查
```bash
# 单个服务
curl http://localhost:8100/health

# 系统整体
curl http://localhost:8000/system/health

# 批量检查
./scripts/deploy.sh health
```

### 接口测试
```bash
# 提交任务
curl -X POST http://localhost:8000/api/v1/process \
  -H "Content-Type: application/json" \
  -d '{"topic_id":"test","topic_text":"大灯很好看"}'

# 规则匹配测试
curl -X POST http://localhost:8110/match/text \
  -d '{"text":"大灯很好看"}'

# 向量匹配测试  
curl -X POST http://localhost:8120/match/fuzzy \
  -d '{"text":"车灯挺漂亮"}'
```

### 日志调试
```bash
# 服务日志
docker-compose logs -f text-processor

# 错误过滤
docker-compose logs -f | grep ERROR

# 实时日志
docker-compose logs --tail=100 text-processor
```

### 数据库调试
```bash
# 连接数据库
docker exec -it starrocks-fe mysql -h localhost -P 9030 -u root voc_db

# 常用查询
SHOW TABLES;
SELECT COUNT(*) FROM changan_standard_opinions;
SELECT * FROM new_words LIMIT 10;
SELECT * FROM error_records WHERE final_status='pending';
```

### Kafka调试
```bash
# 查看Topics
docker exec kafka kafka-topics.sh --list --bootstrap-server localhost:9092

# 监控消息
docker exec kafka kafka-console-consumer.sh \
  --bootstrap-server localhost:9092 \
  --topic voc_toModel_topic --from-beginning

# 消费者组状态
docker exec kafka kafka-consumer-groups.sh \
  --bootstrap-server localhost:9092 --describe --all-groups
```

## 📊 监控运维

### 访问地址
- **API网关**: http://localhost:8000
- **系统监控**: http://localhost:8000/system/health
- **StarRocks**: http://localhost:8030
- **Elasticsearch**: http://localhost:9200  
- **Kibana**: http://localhost:5601
- **Kafka UI**: http://localhost:8080
- **Grafana**: http://localhost:3000 (admin/admin)

### 算法后端运维命令
```bash
# 算法后端部署管理
cd scripts/test
python3 deploy_all_optimized.py        # 一键部署算法后端
python3 test_optimized_deployment.py   # 验证部署效果

# 分步部署管理
python3 create_kafka_topics_lightweight.py      # 创建轻量级Kafka
python3 create_elasticsearch_indices_optimized.py  # 创建优化ES索引
python3 create_starrocks_optimized.py           # 创建优化StarRocks表

# 微服务管理
# 本地开发时可使用Docker管理基础服务
docker-compose ps             # 服务状态
docker-compose logs -f service-name  # 查看日志
docker-compose restart service-name  # 重启服务
```

### 算法后端故障排查
1. **部署失败**: 检查网络连接、服务状态、权限配置
   - 运行: `python3 test_optimized_deployment.py`
   - 检查日志: `deploy_optimized.log`
2. **微服务启动失败**: 检查端口占用、外部服务连接
   - StarRocks: *************:9030
   - Elasticsearch: *************:9200
   - Kafka: *************:5084
   - Embedding: *************:7777
3. **消息处理异常**: 查看Kafka日志、错误队列、数据格式
4. **搜索异常**: 检查ES 5个索引状态、512维向量配置

## 🔮 扩展模块

系统预留了3个未来模块的完整技术方案：

### 1. 智能回评模块 (`future-modules/intelligent-review/`)
- 基于观点分析自动生成用户评价回复
- 支持多种回复风格和个性化定制
- 完整的API设计、数据库表设计

### 2. 智能问数模块 (`future-modules/intelligent-qa/`)
- 自然语言问答和数据洞察
- 支持统计、比较、趋势、细分等问题类型
- 知识图谱集成方案

### 3. 智能报表模块 (`future-modules/intelligent-report/`)
- 自动生成分析报表和可视化图表
- 支持定时推送和订阅管理
- 多格式导出（PDF、Excel、Word）

## 📚 开发规范

### 代码规范
```python
# 函数命名：snake_case
async def process_text_message(message: dict) -> dict:
    """处理文本消息"""
    pass

# 类命名：PascalCase  
class TextProcessorService:
    """文本处理服务"""
    pass

# 常量：UPPER_CASE
MAX_RETRY_COUNT = 5
```

### 错误处理
```python
try:
    result = await self.process_business_logic(data)
except ValidationError as e:
    await self._send_error_message(data, f"Validation error: {e}")
except Exception as e:
    self.logger.error(f"Unexpected error: {e}")
    await self._send_error_message(data, str(e))
```

### 提交规范
```
feat: 添加新功能
fix: 修复bug
docs: 更新文档
style: 代码格式
refactor: 重构
test: 添加测试
```

## 📞 技术支持

### 文档资源
- 🎯 **算法后端架构总结**: [ALGORITHM_BACKEND_ARCHITECTURE_SUMMARY.md](ALGORITHM_BACKEND_ARCHITECTURE_SUMMARY.md)
- 📖 **完整架构**: [docs/architecture_summary.md](docs/architecture_summary.md)
- 🗄️ **数据库设计**: [docs/database_design.md](docs/database_design.md)  
- 🔍 **ES索引设计**: [docs/elasticsearch_design.md](docs/elasticsearch_design.md)
- 📄 **部署指南**: [scripts/README.md](scripts/README.md)

### 联系方式
- 🐛 **Bug反馈**: GitHub Issues
- 💬 **技术讨论**: 项目Discussion
- 📧 **紧急问题**: 技术负责人邮箱

### 常见问题
1. **服务启动失败**: 检查端口、配置、依赖服务
2. **消息处理异常**: 查看Kafka日志、错误队列
3. **添加观点类型**: 更新数据库表、ES映射、业务逻辑
4. **性能优化**: 调整批处理、连接池、缓存配置

---

**💡 算法后端开发提示**: 
- 🎯 **架构定位**: 算法中间层，专注智能打标核心逻辑，不存储业务统计数据
- 📄 **阅读顺序**: ALGORITHM_BACKEND_ARCHITECTURE_SUMMARY.md → 完整架构文档
- 🚀 **部署方式**: `cd scripts/test && python3 deploy_all_optimized.py`
- 🔍 **数据流**: voc_toModel_topic → 算法处理 → result_topic
- ✅ **架构特点**: Kafka 8partition + ES 5索引 + StarRocks 2表（轻量级设计）
- 🔧 **问题排查**: 先运行 `test_optimized_deployment.py` 验证部署