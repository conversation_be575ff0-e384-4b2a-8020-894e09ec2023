# VOC2.0 微服务通用 Dockerfile
FROM python:3.10.18

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 配置国内镜像源（简化版本）

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    wget \
    && rm -rf /var/lib/apt/lists/*

# 复制 requirements 文件
COPY requirements.txt .

# 安装 Python 依赖 - 使用国内镜像源
RUN pip install --no-cache-dir -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple

# 复制项目文件
COPY shared/ shared/
COPY services/ services/
COPY configs/ configs/

# 创建日志目录
RUN mkdir -p logs

# 默认端口（可以通过环境变量覆盖）
EXPOSE 8000

# 设置健康检查（端口可配置）
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:${SERVICE_PORT:-8000}/health || exit 1
# 启动命令（服务名通过环境变量传入）
CMD python services/${SERVICE_NAME}/main.py

