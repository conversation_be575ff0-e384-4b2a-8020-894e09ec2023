# VOC2.0 开发环境配置 - 代码挂载版本
version: '3.8'

# 公共配置
x-common-env: &common-env
  CONFIG_FILE: /app/configs/config.yaml
  OPENBLAS_NUM_THREADS: 1
  OMP_NUM_THREADS: 1
  MKL_NUM_THREADS: 1
  USE_SIMPLE_THREADED_LEVEL3: 1
  KMP_AFFINITY: disabled
  KMP_INIT_AT_FORK: FALSE
  MALLOC_ARENA_MAX: 2
  UVLOOP_NO_EXTENSIONS: 1
  PYTHONFAULTHANDLER: 1
  OMP_PROC_BIND: false
  OMP_WAIT_POLICY: PASSIVE

x-common-ulimits: &common-ulimits
  nproc: 65535
  nofile:
    soft: 65535
    hard: 65535

x-common-security: &common-security
  pids_limit: -1
  security_opt:
    - seccomp=unconfined

x-common-volumes: &common-volumes
  - ./shared:/app/shared
  - ./services:/app/services
  - ./configs:/app/configs
  - ./logs/dev:/app/logs

services:
  # 文本处理服务
  text-processor-dev:
    image: voc-base:latest
    container_name: voc-text-processor-dev
    privileged: true
    ports:
      - "8200:8200"
    environment:
      SERVICE_NAME: text-processor
      SERVICE_PORT: "8200"
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-dev:
        aliases:
          - text-processor
    restart: unless-stopped

  # 规则匹配服务
  rule-matcher-dev:
    image: voc-base:latest
    container_name: voc-rule-matcher-dev
    privileged: true
    ports:
      - "8110:8110"
    environment:
      SERVICE_NAME: rule-matcher
      SERVICE_PORT: "8110"
      RULE_MATCHER_PORT: "8110"
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-dev:
        aliases:
          - rule-matcher
    restart: unless-stopped

  # 向量匹配服务
  vector-matcher-dev:
    image: voc-base:latest
    container_name: voc-vector-matcher-dev
    privileged: true
    ports:
      - "8120:8120"
    environment:
      SERVICE_NAME: vector-matcher
      SERVICE_PORT: "8120"
      VECTOR_MATCHER_PORT: "8120"
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes:
      - ./shared:/app/shared
      - ./services:/app/services
      - ./configs:/app/configs
      - ./logs/dev:/app/logs
      - ./models:/app/models  # 模型文件只读
    networks:
      voc-network-dev:
        aliases:
          - vector-matcher
    restart: unless-stopped

  # UIE模型服务
  uie-dev:
    image: voc-base:latest
    container_name: voc-uie-dev
    privileged: true
    ports:
      - "8140:8140"
    environment:
      SERVICE_NAME: uie
      SERVICE_PORT: "8140"
      # UIE批量处理配置
      UIE_BATCH_SIZE: "32"
      UIE_BATCH_TIMEOUT: "5.0"
      UIE_ENABLE_BATCH_PROCESSING: "true"
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      - voc-network-dev
    restart: unless-stopped

  # 大模型服务
  llm-dev:
    image: voc-base:latest
    container_name: voc-llm-dev
    privileged: true
    ports:
      - "8150:8150"
    environment:
      SERVICE_NAME: llm
      SERVICE_PORT: "8150"
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      - voc-network-dev
    restart: "no"

  # 意图分析服务
  intent-dev:
    image: voc-base:latest
    container_name: voc-intent-dev
    privileged: true
    ports:
      - "8160:8160"
    environment:
      SERVICE_NAME: intent
      SERVICE_PORT: "8160"
      # Intent批量处理配置
      INTENT_BATCH_SIZE: "32"
      INTENT_BATCH_TIMEOUT: "3.0"
      INTENT_ENABLE_BATCH_PROCESSING: "true"
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      - voc-network-dev
    restart: unless-stopped

  # 品牌归属服务
  brand-attribution-dev:
    image: voc-base:latest
    container_name: voc-brand-attribution-dev
    privileged: true
    ports:
      - "8170:8170"
    environment:
      SERVICE_NAME: brand-attribution
      SERVICE_PORT: "8170"
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      - voc-network-dev
    restart: unless-stopped

  # 错误处理服务
  error-handler-dev:
    image: voc-base:latest
    container_name: voc-error-handler-dev
    privileged: true
    ports:
      - "8130:8130"
    environment:
      SERVICE_NAME: error-handler
      SERVICE_PORT: "8130"
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      - voc-network-dev
    restart: unless-stopped

  # 后处理服务
  post-processor-dev:
    image: voc-base:latest
    container_name: voc-post-processor-dev
    privileged: true
    ports:
      - "8180:8180"
    environment:
      SERVICE_NAME: post-processor
      SERVICE_PORT: "8180"
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      - voc-network-dev
    restart: unless-stopped

  # API网关
  gateway-dev:
    image: voc-base:latest
    container_name: voc-gateway-dev
    privileged: true
    ports:
      - "8100:8100"
    environment:
      SERVICE_NAME: gateway
      SERVICE_PORT: "8100"
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      - voc-network-dev
    depends_on:
      - text-processor-dev
      - rule-matcher-dev
      - vector-matcher-dev
      - uie-dev
      - llm-dev
      - intent-dev
      - brand-attribution-dev
      - error-handler-dev
      - post-processor-dev
    restart: unless-stopped

networks:
  voc-network-dev:
    driver: bridge
