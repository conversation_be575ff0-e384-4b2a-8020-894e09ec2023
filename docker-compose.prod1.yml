# VOC2.0 生产环境测试配置 - 多实例版本（指向已启动的API）
# 4意图 + 6UIE + 4大模型 + 1规则匹配 + 1向量匹配
# 所有模型实例都指向当前已启动的单套API，通过环境变量覆盖配置
version: '3.8'

# 公共配置
x-common-env: &common-env
  CONFIG_FILE: /app/configs/config_prod.yaml  # 使用生产环境配置
  OPENBLAS_NUM_THREADS: 1
  OMP_NUM_THREADS: 1
  MKL_NUM_THREADS: 1
  USE_SIMPLE_THREADED_LEVEL3: 1
  KMP_AFFINITY: disabled
  KMP_INIT_AT_FORK: FALSE
  MALLOC_ARENA_MAX: 2
  UVLOOP_NO_EXTENSIONS: 1
  PYTHONFAULTHANDLER: 1
  OMP_PROC_BIND: false
  OMP_WAIT_POLICY: PASSIVE
  
  # Kafka优化环境变量 - 解决CommitFailedError和连接超时
  KAFKA_CONSUMER_SESSION_TIMEOUT_MS: 60000      # 会话超时60秒
  KAFKA_CONSUMER_REQUEST_TIMEOUT_MS: 70000      # 请求超时70秒
  KAFKA_CONSUMER_HEARTBEAT_INTERVAL_MS: 10000   # 心跳间隔10秒
  KAFKA_CONSUMER_MAX_POLL_INTERVAL_MS: 300000   # 最大poll间隔5分钟
  KAFKA_CONSUMER_RETRY_BACKOFF_MS: 2000         # 重试间隔2秒
  
  # CPU优化 - 减少进程竞争
  PYTHONUNBUFFERED: 1
  PYTHONDONTWRITEBYTECODE: 1                    # 禁用.pyc文件生成
  
  # 线程优化
  ASYNCIO_DEBUG: 0                              # 禁用asyncio调试
  PYTHONASYNCIODEBUG: 0
  
  # 进程调度优化
  PYTHONHASHSEED: 0                             # 固定hash seed，提高性能
  PYTHONOPTIMIZE: 1                             # 启用Python优化
  
  # Kafka静态成员配置
  KAFKA_ENABLE_STATIC_MEMBERSHIP: "true"        # 启用静态成员
  
  # 网络优化
  PYTHONUNBUFFERED: 1                           # 禁用Python输出缓冲
  PYTHONIOENCODING: utf-8                       # 设置编码
  
  # JVM优化（如果有Java组件）
  JAVA_OPTS: "-XX:+UseG1GC -XX:MaxGCPauseMillis=200 -Xmx512m"

x-common-ulimits: &common-ulimits
  nproc: 65535
  nofile:
    soft: 65535
    hard: 65535

# 资源限制配置 - 32C64G CPU服务器优化
x-uie-resources: &uie-resources
  cpus: '1.5'          # UIE实例增加到1.5核
  memory: 2G           # 内存增加到2G
  
x-llm-resources: &llm-resources  
  cpus: '2.0'          # LLM实例增加到2核
  memory: 3G           # 内存增加到3G
  
x-intent-resources: &intent-resources
  cpus: '1.0'          # Intent实例增加到1核
  memory: 1G           # 内存增加到1G

x-text-processor-resources: &text-processor-resources
  cpus: '1.0'          # 文本处理实例
  memory: 1G
  
x-rule-matcher-resources: &rule-matcher-resources
  cpus: '0.8'          # 规则匹配实例
  memory: 1G
  
x-vector-matcher-resources: &vector-matcher-resources
  cpus: '1.0'          # 向量匹配实例
  memory: 1.5G
  
x-brand-attribution-resources: &brand-attribution-resources
  cpus: '0.5'          # 品牌归属实例
  memory: 512M
  
x-gateway-resources: &gateway-resources
  cpus: '1.0'          # 网关实例
  memory: 1G

x-common-security: &common-security
  pids_limit: -1
  security_opt:
    - seccomp=unconfined

x-common-volumes: &common-volumes
  - ./shared:/app/shared
  - ./services:/app/services
  - ./configs:/app/configs
  - ./logs/prod1:/app/logs

services:
  # 文本处理服务 - 3个实例（增加处理能力）
  text-processor-prod1-1:
    image: voc-base:latest
    container_name: voc-text-processor-prod1-1
    privileged: true
    ports:
      - "8200:8200"
    environment:
      SERVICE_NAME: text-processor
      SERVICE_PORT: "8200"
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod1:
        aliases:
          - text-processor-1
    restart: unless-stopped
    deploy:
      resources:
        limits:
          <<: *text-processor-resources

  text-processor-prod1-2:
    image: voc-base:latest
    container_name: voc-text-processor-prod1-2
    privileged: true
    ports:
      - "8201:8200"
    environment:
      SERVICE_NAME: text-processor
      SERVICE_PORT: "8200"
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod1:
        aliases:
          - text-processor-2
    restart: unless-stopped
    deploy:
      resources:
        limits:
          <<: *text-processor-resources

  text-processor-prod1-3:
    image: voc-base:latest
    container_name: voc-text-processor-prod1-3
    privileged: true
    ports:
      - "8202:8200"
    environment:
      SERVICE_NAME: text-processor
      SERVICE_PORT: "8200"
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod1:
        aliases:
          - text-processor-3
    restart: unless-stopped
    deploy:
      resources:
        limits:
          <<: *text-processor-resources

  # 规则匹配服务 - 2个实例（增加处理能力）
  rule-matcher-prod1-1:
    image: voc-base:latest
    container_name: voc-rule-matcher-prod1-1
    privileged: true
    ports:
      - "8110:8110"
    environment:
      SERVICE_NAME: rule-matcher
      SERVICE_PORT: "8110"
      RULE_MATCHER_PORT: "8110"
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod1:
        aliases:
          - rule-matcher-1
    restart: unless-stopped
    deploy:
      resources:
        limits:
          <<: *rule-matcher-resources

  rule-matcher-prod1-2:
    image: voc-base:latest
    container_name: voc-rule-matcher-prod1-2
    privileged: true
    ports:
      - "8111:8110"
    environment:
      SERVICE_NAME: rule-matcher
      SERVICE_PORT: "8110"
      RULE_MATCHER_PORT: "8110"
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod1:
        aliases:
          - rule-matcher-2
    restart: unless-stopped
    deploy:
      resources:
        limits:
          <<: *rule-matcher-resources

  # 向量匹配服务 - 2个实例（增加处理能力）
  vector-matcher-prod1-1:
    image: voc-base:latest
    container_name: voc-vector-matcher-prod1-1
    privileged: true
    ports:
      - "8120:8120"
    environment:
      SERVICE_NAME: vector-matcher
      SERVICE_PORT: "8120"
      VECTOR_MATCHER_PORT: "8120"
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes:
      - ./shared:/app/shared
      - ./services:/app/services
      - ./configs:/app/configs
      - ./logs/prod1:/app/logs
      - ./models:/app/models  # 模型文件只读
    networks:
      voc-network-prod1:
        aliases:
          - vector-matcher-1
    restart: unless-stopped
    deploy:
      resources:
        limits:
          <<: *vector-matcher-resources

  vector-matcher-prod1-2:
    image: voc-base:latest
    container_name: voc-vector-matcher-prod1-2
    privileged: true
    ports:
      - "8121:8120"
    environment:
      SERVICE_NAME: vector-matcher
      SERVICE_PORT: "8120"
      VECTOR_MATCHER_PORT: "8120"
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes:
      - ./shared:/app/shared
      - ./services:/app/services
      - ./configs:/app/configs
      - ./logs/prod1:/app/logs
      - ./models:/app/models  # 模型文件只读
    networks:
      voc-network-prod1:
        aliases:
          - vector-matcher-2
    restart: unless-stopped
    deploy:
      resources:
        limits:
          <<: *vector-matcher-resources

  # UIE模型服务 - 6个实例（都指向已启动的API）
  uie-prod1-1:
    image: voc-base:latest
    container_name: voc-uie-prod1-1
    privileged: true
    ports:
      - "8140:8140"
    environment:
      SERVICE_NAME: uie
      SERVICE_PORT: "8140"
      UIE_API_URL: "http://10.75.248.22/ca-inference/api/1970404145888534530/topic/ppuie"  # 使用已验证的UIE API
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod1:
        aliases:
          - uie-1
    restart: unless-stopped
    deploy:
      resources:
        limits:
          <<: *uie-resources

  # uie-prod1-2:
  #   image: voc-base:latest
  #   container_name: voc-uie-prod1-2
  #   privileged: true
  #   ports:
  #     - "8141:8140"
  #   environment:
  #     SERVICE_NAME: uie
  #     SERVICE_PORT: "8140"
  #     UIE_API_URL: "http://10.75.248.22/ca-inference/api/1970371420213256194/topic/ppuie"  # 相同API
  #     <<: *common-env
  #   ulimits: *common-ulimits
  #   <<: *common-security
  #   volumes: *common-volumes
  #   networks:
  #     voc-network-prod1:
  #       aliases:
  #         - uie-2
  #   restart: unless-stopped

  uie-prod1-3:
    image: voc-base:latest
    container_name: voc-uie-prod1-3
    privileged: true
    ports:
      - "8142:8140"
    environment:
      SERVICE_NAME: uie
      SERVICE_PORT: "8140"
      UIE_API_URL: "http://10.75.248.22/ca-inference/api/1970400241310277634/topic/ppuie"  # 相同API
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod1:
        aliases:
          - uie-3
    restart: unless-stopped

  uie-prod1-4:
    image: voc-base:latest
    container_name: voc-uie-prod1-4
    privileged: true
    ports:
      - "8143:8140"
    environment:
      SERVICE_NAME: uie
      SERVICE_PORT: "8140"
      UIE_API_URL: "http://10.62.133.18:5000/ppuie"  # 相同API
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod1:
        aliases:
          - uie-4
    restart: unless-stopped

  uie-prod1-5:
    image: voc-base:latest
    container_name: voc-uie-prod1-5
    privileged: true
    ports:
      - "8144:8140"
    environment:
      SERVICE_NAME: uie
      SERVICE_PORT: "8140"
      UIE_API_URL: "http://10.62.133.18:5001/ppuie"  # 相同API
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod1:
        aliases:
          - uie-5
    restart: unless-stopped

  uie-prod1-6:
    image: voc-base:latest
    container_name: voc-uie-prod1-6
    privileged: true
    ports:
      - "8145:8140"
    environment:
      SERVICE_NAME: uie
      SERVICE_PORT: "8140"
      UIE_API_URL: "http://10.62.133.18:5002/ppuie"  # 相同API
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod1:
        aliases:
          - uie-6
    restart: unless-stopped

  # 大模型服务 - 4个实例（都指向已启动的API）
  llm-prod1-1:
    image: voc-base:latest
    container_name: voc-llm-prod1-1
    privileged: true
    ports:
      - "8150:8150"
    environment:
      SERVICE_NAME: llm
      SERVICE_PORT: "8150"
      LLM_API_URL: "http://10.75.248.22/ca-inference/api/1970402173936508930/llm/v1/chat/completions"  # 使用已验证的LLM API
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod1:
        aliases:
          - llm-1
    restart: unless-stopped

  llm-prod1-2:
    image: voc-base:latest
    container_name: voc-llm-prod1-2
    privileged: true
    ports:
      - "8151:8150"
    environment:
      SERVICE_NAME: llm
      SERVICE_PORT: "8150"
      LLM_API_URL: "http://10.75.248.22/ca-inference/api/1970402173936508930/llm/v1/chat/completions"  # 相同API
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod1:
        aliases:
          - llm-2
    restart: unless-stopped

  llm-prod1-3:
    image: voc-base:latest
    container_name: voc-llm-prod1-3
    privileged: true
    ports:
      - "8152:8150"
    environment:
      SERVICE_NAME: llm
      SERVICE_PORT: "8150"
      LLM_API_URL: "http://10.75.248.22/ca-inference/api/1970402173936508930/llm/v1/chat/completions"  # 相同API
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod1:
        aliases:
          - llm-3
    restart: unless-stopped

  llm-prod1-4:
    image: voc-base:latest
    container_name: voc-llm-prod1-4
    privileged: true
    ports:
      - "8153:8150"
    environment:
      SERVICE_NAME: llm
      SERVICE_PORT: "8150"
      LLM_API_URL: "http://10.75.248.22/ca-inference/api/1970402173936508930/llm/v1/chat/completions"  # 相同API
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod1:
        aliases:
          - llm-4
    restart: unless-stopped

  # 意图分析服务 - 4个实例（都指向已启动的API）
  intent-prod1-1:
    image: voc-base:latest
    container_name: voc-intent-prod1-1
    privileged: true
    ports:
      - "8160:8160"
    environment:
      SERVICE_NAME: intent
      SERVICE_PORT: "8160"
      INTENT_API_URL: "http://10.62.133.18:5010/analyze"  # 使用已启动的Intent API
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod1:
        aliases:
          - intent-1
    restart: unless-stopped

  intent-prod1-2:
    image: voc-base:latest
    container_name: voc-intent-prod1-2
    privileged: true
    ports:
      - "8161:8160"
    environment:
      SERVICE_NAME: intent
      SERVICE_PORT: "8160"
      INTENT_API_URL: "http://10.62.133.18:5011/analyze"  # 相同API
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod1:
        aliases:
          - intent-2
    restart: unless-stopped

  intent-prod1-3:
    image: voc-base:latest
    container_name: voc-intent-prod1-3
    privileged: true
    ports:
      - "8162:8160"
    environment:
      SERVICE_NAME: intent
      SERVICE_PORT: "8160"
      INTENT_API_URL: "http://10.62.133.18:5012/analyze"  # 相同API
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod1:
        aliases:
          - intent-3
    restart: unless-stopped

  intent-prod1-4:
    image: voc-base:latest
    container_name: voc-intent-prod1-4
    privileged: true
    ports:
      - "8163:8160"
    environment:
      SERVICE_NAME: intent
      SERVICE_PORT: "8160"
      INTENT_API_URL: "http://10.62.133.18:5013/analyze"  # 相同API
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod1:
        aliases:
          - intent-4
    restart: unless-stopped

  # 品牌归属服务 - 2个实例（增加处理能力）
  brand-attribution-prod1-1:
    image: voc-base:latest
    container_name: voc-brand-attribution-prod1-1
    privileged: true
    ports:
      - "8170:8170"
    environment:
      SERVICE_NAME: brand-attribution
      SERVICE_PORT: "8170"
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod1:
        aliases:
          - brand-attribution-1
    restart: unless-stopped
    deploy:
      resources:
        limits:
          <<: *brand-attribution-resources

  brand-attribution-prod1-2:
    image: voc-base:latest
    container_name: voc-brand-attribution-prod1-2
    privileged: true
    ports:
      - "8171:8170"
    environment:
      SERVICE_NAME: brand-attribution
      SERVICE_PORT: "8170"
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod1:
        aliases:
          - brand-attribution-2
    restart: unless-stopped
    deploy:
      resources:
        limits:
          <<: *brand-attribution-resources

  # 错误处理服务
  error-handler-prod1:
    image: voc-base:latest
    container_name: voc-error-handler-prod1
    privileged: true
    ports:
      - "8130:8130"
    environment:
      SERVICE_NAME: error-handler
      SERVICE_PORT: "8130"
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod1:
        aliases:
          - error-handler
    restart: unless-stopped

  # 后处理服务 - 2个实例（增加处理能力）
  post-processor-prod1-1:
    image: voc-base:latest
    container_name: voc-post-processor-prod1-1
    privileged: true
    ports:
      - "8180:8180"
    environment:
      SERVICE_NAME: post-processor
      SERVICE_PORT: "8180"
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod1:
        aliases:
          - post-processor-1
    restart: unless-stopped
    deploy:
      resources:
        limits:
          <<: *text-processor-resources

  post-processor-prod1-2:
    image: voc-base:latest
    container_name: voc-post-processor-prod1-2
    privileged: true
    ports:
      - "8181:8180"
    environment:
      SERVICE_NAME: post-processor
      SERVICE_PORT: "8180"
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod1:
        aliases:
          - post-processor-2
    restart: unless-stopped
    deploy:
      resources:
        limits:
          <<: *text-processor-resources

  # API网关
  gateway-prod1:
    image: voc-base:latest
    container_name: voc-gateway-prod1
    privileged: true
    ports:
      - "8100:8100"
    environment:
      SERVICE_NAME: gateway
      SERVICE_PORT: "8100"
      # 环境配置
      ENV_SUFFIX: "prod1"
      # 多实例配置（更新实例数量）
      LLM_INSTANCE_COUNT: "4"
      UIE_INSTANCE_COUNT: "6"
      INTENT_INSTANCE_COUNT: "4"
      TEXT_PROCESSOR_INSTANCE_COUNT: "3"
      RULE_MATCHER_INSTANCE_COUNT: "2"
      VECTOR_MATCHER_INSTANCE_COUNT: "2"
      BRAND_ATTRIBUTION_INSTANCE_COUNT: "2"
      POST_PROCESSOR_INSTANCE_COUNT: "2"
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod1:
        aliases:
          - gateway
    depends_on:
      - text-processor-prod1-1
      - text-processor-prod1-2
      - text-processor-prod1-3
      - rule-matcher-prod1-1
      - rule-matcher-prod1-2
      - vector-matcher-prod1-1
      - vector-matcher-prod1-2
      - uie-prod1-1
      # - uie-prod1-2
      - uie-prod1-3
      - uie-prod1-4
      - uie-prod1-5
      - uie-prod1-6
      - llm-prod1-1
      - llm-prod1-2
      - llm-prod1-3
      - llm-prod1-4
      - intent-prod1-1
      - intent-prod1-2
      - intent-prod1-3
      - intent-prod1-4
      - brand-attribution-prod1-1
      - brand-attribution-prod1-2
      - error-handler-prod1
      - post-processor-prod1-1
      - post-processor-prod1-2
    restart: unless-stopped
    deploy:
      resources:
        limits:
          <<: *gateway-resources

networks:
  voc-network-prod1:
    driver: bridge
