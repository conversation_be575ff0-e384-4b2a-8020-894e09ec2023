# VOC2.0智能打标系统 - 算法后端架构总结

## 🎯 **算法后端定位**

**算法中间层**: 专注智能打标核心逻辑，不存储业务统计数据，只负责推送到 `result_topic`

## 📊 **最终架构配置**

### ✅ **Kafka Topics: 8个** (轻量级设计)
- 每个topic **1个partition** 
- 总计 **8个partitions** (资源节省71%)
- 完整消息流: `voc_toModel_topic` → 算法处理 → `result_topic`

### ✅ **Elasticsearch索引: 5个** (完全按架构文档设计)

| 索引名称 | 用途 | 向量搜索 | 表结构对应 |
|---------|------|----------|----------|
| `voc_standard_opinion` | 长安标准观点库 | ✅ 512维 | 归一化观点到标准观点映射 |
| `voc_entity_synonym` | 主体近义词库 | ✅ 512维 | Entity_id\|Sim_entity\|vector\|Normalized_entity\|Standard_opinion_id_list |
| `voc_description_synonym` | 描述近义词库 | ✅ 512维 | Description_id\|Sim_description\|vector\|Normalized_description\|Standard_opinion_id |
| `voc_new_words` | 新词库 | ✅ 512维 | 新词发现 + 向量推荐到标准观点 |
| `voc_brand_series_model` | 品牌车系车型库 | ❌ | 精确匹配 + 全文搜索（比StarRocks快） |

**🔍 ES专长**: 所有实时搜索和向量匹配，3个向量索引支持ANN语义搜索

### ✅ **StarRocks数据表: 2个** (精简到核心必要)

| 表名称 | 用途 | 表结构对应 |
|--------|------|----------|
| `opinion_synonym` | 观点近义词库 | Add_entity\|Add_description\|Standard_opinion\|Standard_opinion_id |
| `error_records` | 错误记录库 | Topic_id\|Topic_text\|Retry_count\|Error_type\|Last_ts\|Final_status |

**💾 StarRocks专长**: 
- 长安运维手动维护的观点映射数据
- 超过5次重试失败的错误数据统一存储

## 🔄 **数据流架构**

### **核心流程**:
```
voc_toModel_topic → [粗分句+分流] → llm_topic / uie_topic
                                      ↓
[LLM全流程] ────────────────────→ result_topic ────→ pengfei_topic
                                      ↑
[UIE流程] → intent_topic → brand_attribution_topic ─┘
```

### **数据读取流程**:
1. **读取StarRocks**: `opinion_synonym` → 转化到ES的标准观点库和近义词库
2. **实时搜索ES**: 规则匹配 + 模糊匹配 + 向量搜索
3. **错误处理**: 超过5次重试 → 存入StarRocks `error_records`

## 🚀 **部署使用**

### **一键部署**:
```bash
cd scripts/test
python3 deploy_all_optimized.py
```

### **验证部署**:
```bash
cd scripts/test
python3 test_optimized_deployment.py
```

### **分步部署**:
```bash
cd scripts/test
python3 create_kafka_topics_lightweight.py          # 8个partition
python3 create_elasticsearch_indices_optimized.py   # 5个索引
python3 create_starrocks_optimized.py              # 2个表
```

## 📈 **架构优势**

### 🎯 **专注核心**
- ✅ 算法中间层定位清晰
- ✅ 不存储业务统计数据 (statistics_summary, processing_logs, business_results)
- ✅ 专注智能打标，只推送到result_topic

### 📋 **标准对接**
- ✅ 表结构完全按照架构文档设计
- ✅ 与长安运维对接无缝
- ✅ 支持观点近义词更新和错误数据管理

### ⚡ **性能优化**
- ✅ ES专长发挥: 品牌匹配比StarRocks更快
- ✅ 3个向量索引支持毫秒级ANN语义搜索
- ✅ 消除重复存储，每份数据只存储在最适合的地方

### 🔧 **维护简化**
- ✅ 架构清晰，职责分离
- ✅ 轻量级设计，资源占用最小
- ✅ 完整的部署和验证脚本

## 🎉 **总结**

**算法后端架构完成！** 
- 🎯 专注智能打标核心逻辑
- 📋 完全按照架构文档设计  
- 🔍 ES负责所有搜索，StarRocks负责运维数据
- ⚡ 算法中间层定位清晰，只推送到result_topic

**从混乱到清晰，从重复到精简，算法后端架构完美实现！** 🚀