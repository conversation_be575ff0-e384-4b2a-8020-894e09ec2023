# 智能报表生成模块

## 模块概述
智能报表生成模块是VOC2.0系统的高级输出功能，基于智能打标和分析结果，自动生成各种业务报表、洞察报告和可视化图表。支持定制化报表模板，定时生成和推送，满足不同业务部门的需求。

## 功能特性

### 核心功能
1. **自动报表生成**
   - 基于观点数据自动生成分析报表
   - 支持多种报表格式（PDF、Word、Excel、HTML）
   - 可配置的报表模板和样式

2. **智能洞察分析**
   - 自动发现数据中的关键趋势和异常
   - 生成洞察性分析文本
   - 提供改进建议和行动建议

3. **可视化图表**
   - 自动选择最适合的图表类型
   - 支持交互式图表和仪表板
   - 移动端适配和响应式设计

4. **定时推送**
   - 支持定时自动生成报表
   - 邮件、短信、企业微信推送
   - 订阅管理和个性化设置

### 报表类型

#### 综合分析报表
- VOC整体趋势分析
- 品牌/车系口碑对比
- 用户满意度综合评估
- 竞品对比分析

#### 专项分析报表
- 单一主体深度分析（如内饰专项报告）
- 情感趋势分析报告
- 用户画像分析报告
- 问题热点分析报告

#### 业务决策报表
- 产品改进建议报告
- 营销策略优化报告
- 客户服务改进报告
- 市场机会分析报告

#### 监控预警报表
- 负面舆情预警报告
- 异常数据监控报告
- KPI达成情况报告
- 实时数据监控看板

## 技术架构预期

### 核心组件
1. **报表引擎**
   - 模板渲染引擎
   - 数据聚合计算
   - 图表生成引擎
   - 格式转换组件

2. **洞察分析引擎**
   - 统计分析算法
   - 趋势检测算法
   - 异常发现算法
   - 文本生成引擎

3. **调度系统**
   - 定时任务调度
   - 依赖关系管理
   - 失败重试机制
   - 资源优化分配

4. **推送系统**
   - 多渠道推送支持
   - 用户订阅管理
   - 推送状态跟踪
   - 个性化设置

## 数据库设计预留

### 报表模板表 (report_templates)
```sql
CREATE TABLE report_templates (
    template_id BIGINT PRIMARY KEY,
    template_name VARCHAR(200) NOT NULL,
    template_type VARCHAR(50),    -- comprehensive, specialized, business, monitoring
    template_content JSON,       -- 模板结构定义
    data_sources JSON,          -- 数据源配置
    chart_configs JSON,         -- 图表配置
    layout_config JSON,         -- 布局配置
    style_config JSON,          -- 样式配置
    variables JSON,             -- 可配置变量
    is_active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(100),
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

### 报表生成记录表 (report_records)
```sql
CREATE TABLE report_records (
    report_id BIGINT PRIMARY KEY,
    template_id BIGINT,
    report_name VARCHAR(200),
    report_type VARCHAR(50),
    generation_mode VARCHAR(20), -- manual, scheduled, triggered
    parameters JSON,            -- 生成参数
    data_range JSON,           -- 数据范围
    file_path VARCHAR(500),    -- 生成文件路径
    file_format VARCHAR(20),   -- pdf, word, excel, html
    file_size BIGINT,         -- 文件大小(bytes)
    generation_time INT,       -- 生成耗时(ms)
    status VARCHAR(20),        -- generating, completed, failed
    error_message TEXT,        -- 错误信息
    generated_by VARCHAR(100), -- 生成人
    generated_at TIMESTAMP,
    downloaded_count INT DEFAULT 0,
    last_downloaded_at TIMESTAMP
);
```

### 订阅管理表 (report_subscriptions)
```sql  
CREATE TABLE report_subscriptions (
    subscription_id BIGINT PRIMARY KEY,
    user_id VARCHAR(100) NOT NULL,
    template_id BIGINT NOT NULL,
    subscription_name VARCHAR(200),
    schedule_config JSON,      -- 调度配置(daily, weekly, monthly)
    parameters JSON,           -- 订阅参数
    delivery_config JSON,      -- 推送配置(email, sms, wechat)
    is_active BOOLEAN DEFAULT TRUE,
    last_generated_at TIMESTAMP,
    next_generation_at TIMESTAMP,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

### 洞察结果表 (insight_results)
```sql
CREATE TABLE insight_results (
    insight_id BIGINT PRIMARY KEY,
    report_id BIGINT,
    insight_type VARCHAR(50),   -- trend, anomaly, correlation, prediction
    insight_title VARCHAR(200),
    insight_content TEXT,       -- 洞察内容描述
    supporting_data JSON,       -- 支撑数据
    confidence_score FLOAT,     -- 置信度
    importance_score FLOAT,     -- 重要性评分
    actionable_suggestions JSON, -- 可执行建议
    created_at TIMESTAMP
);
```

## API接口设计预留

### 生成报表
```
POST /api/v1/intelligent-report/generate
{
    "template_id": "template123",
    "parameters": {
        "date_range": {
            "start": "2024-01-01",
            "end": "2024-01-31"
        },
        "brands": ["长安", "吉利"],
        "components": ["内饰", "外观"]
    },
    "format": "pdf",
    "delivery": {
        "email": "<EMAIL>",
        "immediate": true
    }
}

Response:
{
    "report_id": "report456",
    "status": "generating",
    "estimated_completion": "2024-01-15T10:30:00Z",
    "download_url": null
}
```

### 获取报表状态
```
GET /api/v1/intelligent-report/status/{report_id}

Response:
{
    "report_id": "report456",
    "status": "completed",
    "file_size": 2048000,
    "download_url": "https://example.com/reports/report456.pdf",
    "generation_time": 15000,
    "insights_count": 8
}
```

### 管理订阅
```
POST /api/v1/intelligent-report/subscriptions
{
    "template_id": "template123",
    "name": "月度VOC报告",
    "schedule": {
        "frequency": "monthly",
        "day_of_month": 1,
        "time": "09:00"
    },
    "parameters": {...},
    "delivery": {
        "email": ["<EMAIL>"],
        "format": "pdf"
    }
}
```

### 获取洞察分析
```
GET /api/v1/intelligent-report/insights/{report_id}

Response:
{
    "insights": [
        {
            "type": "trend",
            "title": "内饰满意度持续上升",
            "content": "过去三个月内饰相关正面评价增长25%...",
            "confidence": 0.89,
            "importance": 0.92,
            "suggestions": [
                "继续保持内饰设计优势",
                "可考虑在营销中突出内饰特色"
            ]
        }
    ]
}
```

## 服务架构预留

### 微服务结构
```
future-modules/intelligent-report/
├── services/
│   ├── report-engine/          # 报表生成引擎
│   ├── template-management/    # 模板管理服务
│   ├── data-aggregation/      # 数据聚合服务
│   ├── insight-analysis/      # 洞察分析服务
│   ├── chart-generation/      # 图表生成服务
│   ├── schedule-manager/      # 调度管理服务
│   └── delivery-service/      # 推送服务
├── templates/
│   ├── comprehensive/         # 综合报表模板
│   ├── specialized/          # 专项报表模板
│   ├── business/             # 业务报表模板
│   └── monitoring/           # 监控报表模板
├── assets/
│   ├── styles/              # 样式文件
│   ├── fonts/               # 字体文件
│   └── images/              # 图片资源
└── configs/
    └── report_config.yaml    # 报表配置
```

## 报表模板设计

### 模板结构
```json
{
    "template_id": "voc_monthly_report",
    "name": "VOC月度分析报告",
    "type": "comprehensive",
    "sections": [
        {
            "section_id": "executive_summary",
            "title": "执行摘要",
            "type": "text_with_highlights",
            "data_source": "aggregated_insights"
        },
        {
            "section_id": "trend_analysis", 
            "title": "趋势分析",
            "type": "charts_with_text",
            "charts": [
                {
                    "type": "line_chart",
                    "data_source": "sentiment_trend",
                    "title": "情感趋势变化"
                }
            ]
        },
        {
            "section_id": "component_analysis",
            "title": "部件分析",
            "type": "grid_layout",
            "components": [...]
        }
    ],
    "style": {
        "theme": "professional",
        "color_scheme": "changan_brand",
        "font_family": "Microsoft YaHei"
    }
}
```

## 数据处理流程

### 数据聚合
1. **原始数据获取**：从观点数据库获取结构化数据
2. **数据清洗**：去除无效和重复数据
3. **维度聚合**：按时间、品牌、组件等维度聚合
4. **指标计算**：计算各种业务指标和KPI

### 洞察分析
1. **趋势分析**：识别数据中的上升、下降、周期性趋势
2. **异常检测**：发现异常数据点和模式
3. **关联分析**：分析不同维度间的关联关系
4. **预测分析**：基于历史数据进行趋势预测

### 报表生成
1. **模板解析**：解析报表模板结构
2. **数据填充**：将聚合数据填充到模板中  
3. **图表生成**：生成各种可视化图表
4. **格式转换**：转换为目标格式(PDF/Word/Excel)

## 与现有系统集成

### 数据集成
- 从智能打标系统获取观点数据
- 集成业务数据仓库
- 连接用户画像系统
- 对接外部数据源

### 系统集成
- 集成到企业BI平台
- 对接邮件系统
- 集成企业微信/钉钉
- 连接文件存储系统

## 开发计划预留

### Phase 1: 基础功能
- [ ] 报表模板引擎
- [ ] 基础图表生成
- [ ] PDF/Excel导出
- [ ] 基础API接口

### Phase 2: 智能化
- [ ] 自动洞察分析
- [ ] 智能图表选择
- [ ] 自然语言生成
- [ ] 调度系统

### Phase 3: 高级功能
- [ ] 交互式报表
- [ ] 实时数据支持
- [ ] 多语言支持
- [ ] 高级可视化

## 技术选型建议
- **报表引擎**：JasperReports, BIRT
- **图表库**：ECharts, Highcharts
- **PDF生成**：wkhtmltopdf, Puppeteer
- **调度系统**：Apache Airflow, Quartz
- **文件存储**：MinIO, AWS S3
- **邮件服务**：SendGrid, 阿里云邮件服务

## 性能指标预期
- 报表生成速度：<30秒（标准报表）
- 并发生成能力：50个/分钟
- 文件存储容量：支持TB级存储
- 系统可用性：≥99.9%
- 推送成功率：≥98%