# 智能问数模块

## 模块概述
智能问数模块是VOC2.0系统的高级分析功能，用于基于结构化的观点数据进行智能问答和数据洞察。用户可以用自然语言提问，系统自动理解问题意图并从观点数据中检索答案。

## 功能特性

### 核心功能
1. **自然语言问答**
   - 支持自然语言问题理解
   - 自动识别问题类型（统计、比较、趋势等）
   - 智能匹配相关观点数据

2. **数据洞察生成**
   - 基于观点数据生成洞察报告
   - 自动发现异常模式和趋势
   - 生成可视化图表建议

3. **智能推荐**
   - 推荐相关问题
   - 智能补全问题
   - 基于上下文的深度挖掘建议

### 支持的问题类型

#### 统计类问题
- "大灯相关的正面评价有多少条？"
- "长安UNI-Z的满意度如何？"
- "内饰相关的抱怨主要集中在哪些方面？"

#### 比较类问题  
- "长安和吉利的外观评价对比如何？"
- "UNI-T和UNI-Z哪个口碑更好？"
- "今年和去年的动力评价有什么变化？"

#### 趋势类问题
- "内饰评价的趋势如何？"
- "最近一个月油耗相关的讨论热度？"
- "各品牌车系的情感倾向变化？"

#### 细分类问题
- "90后用户对空间的评价如何？"
- "女性用户最关注哪些方面？"
- "一线城市用户的购买意向分析？"

## 技术架构预期

### 核心组件
1. **问题理解引擎**
   - 自然语言处理
   - 意图识别
   - 实体抽取
   - 查询语句生成

2. **数据检索引擎**
   - 高效的数据查询
   - 多维度数据聚合
   - 实时计算缓存

3. **答案生成引擎** 
   - 结果格式化
   - 自然语言生成
   - 图表建议生成

4. **知识图谱**
   - 汽车领域知识图谱
   - 观点关系图谱
   - 用户画像图谱

## 数据库设计预留

### 问答记录表 (qa_records)
```sql
CREATE TABLE qa_records (
    qa_id BIGINT PRIMARY KEY,
    user_id VARCHAR(100),
    question TEXT NOT NULL,
    question_type VARCHAR(50), -- statistical, comparison, trend, segmentation
    parsed_intent JSON,        -- 解析后的问题意图
    sql_query TEXT,           -- 生成的SQL查询
    answer TEXT,              -- 生成的答案
    answer_type VARCHAR(50),  -- text, chart, table
    confidence_score FLOAT,   -- 答案置信度
    execution_time INT,       -- 查询执行时间(ms)
    user_feedback INT,        -- 用户反馈评分
    session_id VARCHAR(100),  -- 会话ID
    created_at TIMESTAMP
);
```

### 知识图谱表 (knowledge_graph)
```sql
CREATE TABLE knowledge_graph (
    kg_id BIGINT PRIMARY KEY,
    entity_type VARCHAR(50),  -- brand, series, component, attribute
    entity_name VARCHAR(200),
    entity_properties JSON,   -- 实体属性
    relations JSON,          -- 关系信息
    synonyms JSON,           -- 同义词
    embedding VECTOR(768),   -- 实体向量表示
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

### 问题模板表 (question_templates)
```sql
CREATE TABLE question_templates (
    template_id BIGINT PRIMARY KEY,
    template_name VARCHAR(200),
    template_pattern TEXT,    -- 问题模式
    intent_type VARCHAR(50),  -- 意图类型
    sql_template TEXT,        -- SQL模板
    answer_template TEXT,     -- 答案模板
    variables JSON,          -- 模板变量
    usage_count INT DEFAULT 0,
    success_rate FLOAT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP
);
```

## API接口设计预留

### 智能问答
```
POST /api/v1/intelligent-qa/ask
{
    "question": "大灯相关的正面评价有多少条？",
    "user_id": "user123",
    "session_id": "session456",
    "context": {
        "previous_questions": [...],
        "filters": {
            "time_range": "last_month",
            "brand": "长安"
        }
    }
}

Response:
{
    "answer": {
        "text": "根据数据分析，大灯相关的正面评价共有1,247条...",
        "data": {
            "total_count": 1247,
            "breakdown": {...}
        },
        "charts": [
            {
                "type": "bar_chart",
                "data": {...},
                "title": "大灯正面评价分布"
            }
        ]
    },
    "confidence": 0.92,
    "related_questions": [
        "大灯负面评价有哪些？",
        "大灯评价的时间趋势如何？"
    ],
    "execution_time": 245
}
```

### 获取推荐问题
```
GET /api/v1/intelligent-qa/recommendations
Query: user_id, context, category

Response:
{
    "recommended_questions": [
        {
            "question": "内饰评价趋势如何？",
            "category": "trend_analysis",
            "popularity_score": 0.85
        }
    ]
}
```

### 数据洞察
```
POST /api/v1/intelligent-qa/insights
{
    "dimensions": ["brand", "component", "sentiment"],
    "time_range": "last_quarter",
    "filters": {...}
}

Response:
{
    "insights": [
        {
            "type": "trend_insight",
            "title": "内饰满意度持续上升",
            "description": "...",
            "confidence": 0.88,
            "supporting_data": {...}
        }
    ]
}
```

## 服务架构预留

### 微服务结构
```
future-modules/intelligent-qa/
├── services/
│   ├── question-understanding/  # 问题理解服务
│   ├── data-retrieval/         # 数据检索服务
│   ├── answer-generation/      # 答案生成服务
│   ├── knowledge-graph/        # 知识图谱服务
│   └── insight-mining/         # 洞察挖掘服务
├── models/
│   ├── nlp_models/            # NLP模型
│   ├── intent_models/         # 意图识别模型
│   └── generation_models/     # 答案生成模型
├── knowledge/
│   ├── domain_ontology/       # 领域本体
│   ├── entity_relations/      # 实体关系
│   └── question_patterns/     # 问题模式
└── configs/
    └── qa_config.yaml         # 问答配置
```

## 数据流设计

### 输入数据源
1. **观点数据库**：结构化的观点分析结果
2. **用户画像**：用户特征和偏好数据  
3. **业务数据**：销量、市场数据等
4. **外部数据**：行业报告、竞品分析等

### 处理流程
1. **问题预处理**：分词、实体识别、意图分析
2. **查询生成**：将自然语言转换为结构化查询
3. **数据检索**：执行查询并获取相关数据
4. **答案生成**：格式化结果并生成自然语言回答
5. **结果优化**：添加图表建议和相关问题

## 与现有系统集成

### 数据集成
- 读取智能打标结果数据
- 集成用户画像系统
- 连接业务数据仓库

### 服务集成
- 通过API提供问答服务
- 集成到BI系统和报表平台
- 支持第三方应用接入

## 开发计划预留

### Phase 1: 基础问答
- [ ] 问题理解引擎
- [ ] 基础数据查询
- [ ] 简单答案生成
- [ ] 基础API接口

### Phase 2: 智能化增强
- [ ] 知识图谱构建
- [ ] 复杂问题处理
- [ ] 多轮对话支持
- [ ] 个性化推荐

### Phase 3: 高级分析
- [ ] 自动洞察挖掘
- [ ] 预测性分析
- [ ] 实时数据支持
- [ ] 可视化集成

## 技术选型建议
- **NLP框架**：spaCy, Transformers
- **知识图谱**：Neo4j, ArangoDB
- **搜索引擎**：Elasticsearch
- **缓存系统**：Redis
- **图表库**：ECharts, D3.js

## 性能指标预期
- 问题理解准确率：≥90%
- 答案相关性：≥85%
- 平均响应时间：<2秒
- 系统可用性：≥99.5%