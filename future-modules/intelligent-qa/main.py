"""
智能问答服务 - JSON数据总结接口（简化版）
基于Qwen-Agent框架实现JSON数据的智能分析和总结
支持流式输出
"""

import os
import json
import yaml
import logging
import asyncio
from typing import Dict, List, Any, AsyncGenerator
from datetime import datetime

# FastAPI相关导入
from fastapi import FastAPI, HTTPException, Header
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
import uvicorn
import httpx

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)



class JSONSummaryRequest(BaseModel):
	"""JSON数据总结请求模型"""
	data: List[Dict[str, Any]] = Field(..., description="数据列表")
	fieldDefinitions: List[Dict[str, str]] = Field(..., description="字段定义")
	chartType: str = Field(..., description="图表类型")


class JSONDataAnalyzer:
	"""JSON数据分析器（简化版）"""
	
	def __init__(self, config_path: str = "config.yaml"):
		"""初始化分析器"""
		self.config_path = self._resolve_config_path(config_path)
		self.config = self._load_config(self.config_path)
		logger.info(f"已加载配置文件: {os.path.abspath(self.config_path)}")
		# 读取系统提示词
		self.system_prompt = self.config['json_summary_prompts']['system_prompt']

		self.model = (
			self.config.get('qwen_agent', {}).get('model')
			
			
		)
		self.api_url = (
			self.config.get('qwen_agent', {}).get('model_server')
			
			
		)
		self.api_key = (
			self.config.get('qwen_agent', {}).get('api_key')
			
		)
		
	def _resolve_config_path(self, config_path: str) -> str:
		"""解析配置文件路径，支持环境变量与多路径回退"""
		# 环境变量优先
		env_path = os.getenv('INTELLIGENT_QA_CONFIG')
		if env_path and os.path.isfile(env_path):
			return env_path
		# 当前工作目录
		if os.path.isfile(config_path):
			return config_path
		# 脚本所在目录
		script_dir_path = os.path.join(os.path.dirname(__file__), config_path)
		if os.path.isfile(script_dir_path):
			return script_dir_path
		# 未找到，返回原始以便统一报错
		return config_path
		
	def _load_config(self, config_path: str) -> Dict[str, Any]:
		"""加载配置文件"""
		if not os.path.isfile(config_path):
			raise FileNotFoundError(
				f"未找到配置文件: {config_path}。可通过设置环境变量 INTELLIGENT_QA_CONFIG 指定绝对路径。")
		with open(config_path, 'r', encoding='utf-8') as f:
			cfg = yaml.safe_load(f) or {}
		if 'json_summary_prompts' not in cfg or 'system_prompt' not in cfg['json_summary_prompts'] or 'data_summary_prompt' not in cfg['json_summary_prompts']:
			raise ValueError(
				f"配置文件 {os.path.abspath(config_path)} 缺少 json_summary_prompts.system_prompt 或 data_summary_prompt")
		return cfg
	
	def _format_field_definitions(self, field_definitions: List[Dict[str, str]]) -> str:
		"""格式化字段定义"""
		return "\n".join([f"- **{field['fieldName']}**: {field['definition']}" for field in field_definitions])
	
	async def analyze_json_data(self, request: JSONSummaryRequest) -> AsyncGenerator[str, None]:
		"""分析JSON数据（流式），直连LLM接口输出纯文本流"""
		try:
			# 格式化输入数据
			json_data_str = json.dumps(request.data, ensure_ascii=False, indent=2)
			field_definitions_str = self._format_field_definitions(request.fieldDefinitions)

			# 构建提示词（统一使用 data_summary_prompt）
			prompt = self.config['json_summary_prompts']['data_summary_prompt'].format(
				json_data=json_data_str,
				field_definitions=field_definitions_str,
				chart_type=request.chartType
			)

			if not self.api_key:
				raise HTTPException(status_code=500, detail="未配置API密钥，请在config.yaml的qwen_agent.api_key或环境变量OPENAI_API_KEY中设置")

			headers = {
				"Authorization": f"{self.api_key}",
				"Content-Type": "application/json"
			}
			payload = {
				"model": self.model,
				"messages": [
					{"role": "system", "content": self.system_prompt},
					{"role": "user", "content": prompt}
				],
				"stream": True
			}

			async with httpx.AsyncClient(timeout=None) as client:
				async with client.stream("POST", self.api_url, headers=headers, json=payload) as response:
					if response.status_code != 200:
						raise HTTPException(status_code=response.status_code, detail=await response.text())
					async for line in response.aiter_lines():
						if not line:
							continue
						if line.startswith("data: "):
							content = line[len("data: "):].strip()
							if content == "[DONE]":
								break
							try:
								json_data = json.loads(content)
								delta = json_data.get('choices', [{}])[0].get('delta', {})
								chunk = delta.get('content')
								if chunk:
									yield chunk
							except json.JSONDecodeError:
								# 不是JSON则按原文输出
								yield content
						await asyncio.sleep(0.01)

		except Exception as e:
			logger.error(f"JSON数据分析失败: {e}")
			yield f"错误: {str(e)}"


# 创建FastAPI应用
app = FastAPI(
	title="JSON数据总结服务",
	description="基于HTTP流式接口的JSON数据智能分析和总结",
	version="1.0.0"
)


# 全局分析器实例
analyzer = None

@app.on_event("startup")
async def startup_event():
	"""应用启动事件"""
	global analyzer
	analyzer = JSONDataAnalyzer()
	logger.info("JSON数据分析服务启动成功")

@app.get("/")
async def root():
	"""根路径"""
	return {"message": "JSON数据总结服务运行中", "version": "1.0.0"}

@app.get("/health")
async def health_check():
	"""健康检查"""
	return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.post("/analyze")
async def analyze_json(request: JSONSummaryRequest):
	"""分析JSON数据（流式），输出纯文本流"""
	if not analyzer:
		raise HTTPException(status_code=500, detail="服务未初始化")
	try:
		return StreamingResponse(
			analyzer.analyze_json_data(request),
			media_type="text/plain; charset=utf-8",
			headers={
				"Cache-Control": "no-cache",
				"Connection": "keep-alive"
			}
		)
	except Exception as e:
		logger.error(f"JSON分析异常: {e}")
		raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
	# 创建日志目录
	os.makedirs("logs", exist_ok=True)
	
	# 读取配置中的server.host与server.port
	try:
		_tmp_loader = JSONDataAnalyzer()
		cfg = _tmp_loader.config
		host = cfg.get('server', {}).get('host', '0.0.0.0')
		port = int(cfg.get('server', {}).get('port', 8190))
	except Exception as _e:
		logger.warning(f"读取server配置失败，使用默认值。原因: {_e}")
		host = '0.0.0.0'
		port = 8190
	
	# 启动服务
	uvicorn.run(
		"main:app",
		host=host,
		port=port,
		reload=True,
		log_level="info"
	)
