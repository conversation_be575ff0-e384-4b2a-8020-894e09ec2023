"""
智能问答服务 - JSON数据总结接口（OpenAI SSE格式）
基于Qwen-Agent框架实现JSON数据的智能分析和总结
支持OpenAI兼容的流式输出格式
"""

import os
import json
import yaml
import logging
import asyncio
from typing import Dict, List, Any, AsyncGenerator
from datetime import datetime
import uuid
from contextlib import asynccontextmanager

# FastAPI相关导入
from fastapi import FastAPI, HTTPException, Header
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
import uvicorn
import httpx

# 配置日志 - 只在有错误或重要事件时输出
logging.basicConfig(
	level=logging.WARNING,  # 只显示警告和错误
	format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)



class JSONSummaryRequest(BaseModel):
	"""JSON数据总结请求模型"""
	data: List[Dict[str, Any]] = Field(..., description="数据列表")
	fieldDefinitions: List[Dict[str, str]] = Field(..., description="字段定义")
	chartType: str = Field(..., description="图表类型")


class JSONDataAnalyzer:
	"""JSON数据分析器（OpenAI SSE格式）"""
	
	def __init__(self, config_path: str = "config.yaml"):
		"""初始化分析器"""
		self.config_path = self._resolve_config_path(config_path)
		self.config = self._load_config(self.config_path)
		# 启动时只输出一次配置信息（使用 print 而不是 logger）
		print(f"[启动] 已加载配置文件: {os.path.abspath(self.config_path)}")
		# 读取系统提示词
		self.system_prompt = self.config['json_summary_prompts']['system_prompt']

		self.model = (
			self.config.get('qwen_agent', {}).get('model')
			
			
		)
		self.api_url = (
			self.config.get('qwen_agent', {}).get('model_server')
			
			
		)
		self.api_key = (
			self.config.get('qwen_agent', {}).get('api_key')
			
		)
		
	def _resolve_config_path(self, config_path: str) -> str:
		"""解析配置文件路径，支持环境变量与多路径回退"""
		# 环境变量优先
		env_path = os.getenv('INTELLIGENT_QA_CONFIG')
		if env_path and os.path.isfile(env_path):
			return env_path
		# 当前工作目录
		if os.path.isfile(config_path):
			return config_path
		# 脚本所在目录
		script_dir_path = os.path.join(os.path.dirname(__file__), config_path)
		if os.path.isfile(script_dir_path):
			return script_dir_path
		# 未找到，返回原始以便统一报错
		return config_path
		
	def _load_config(self, config_path: str) -> Dict[str, Any]:
		"""加载配置文件"""
		if not os.path.isfile(config_path):
			raise FileNotFoundError(
				f"未找到配置文件: {config_path}。可通过设置环境变量 INTELLIGENT_QA_CONFIG 指定绝对路径。")
		with open(config_path, 'r', encoding='utf-8') as f:
			cfg = yaml.safe_load(f) or {}
		if 'json_summary_prompts' not in cfg or 'system_prompt' not in cfg['json_summary_prompts'] or 'data_summary_prompt' not in cfg['json_summary_prompts']:
			raise ValueError(
				f"配置文件 {os.path.abspath(config_path)} 缺少 json_summary_prompts.system_prompt 或 data_summary_prompt")
		return cfg
	
	def _format_field_definitions(self, field_definitions: List[Dict[str, str]]) -> str:
		"""格式化字段定义"""
		return "\n".join([f"- **{field['fieldName']}**: {field['definition']}" for field in field_definitions])
	
	def _create_sse_chunk(self, content: str, chunk_id: str = None) -> str:
		"""创建OpenAI兼容的SSE数据块"""
		if chunk_id is None:
			chunk_id = f"chatcmpl-{uuid.uuid4().hex[:8]}"
		
		chunk_data = {
			"id": chunk_id,
			"object": "chat.completion.chunk",
			"created": int(datetime.now().timestamp()),
			"model": self.model,
			"choices": [{
				"index": 0,
				"delta": {
					"content": content
				},
				"finish_reason": None
			}]
		}
		return f"data: {json.dumps(chunk_data, ensure_ascii=False)}\n\n"
	
	def _create_sse_done(self, chunk_id: str = None) -> str:
		"""创建OpenAI兼容的结束SSE数据块"""
		if chunk_id is None:
			chunk_id = f"chatcmpl-{uuid.uuid4().hex[:8]}"
		
		done_data = {
			"id": chunk_id,
			"object": "chat.completion.chunk",
			"created": int(datetime.now().timestamp()),
			"model": self.model,
			"choices": [{
				"index": 0,
				"delta": {},
				"finish_reason": "stop"
			}]
		}
		return f"data: {json.dumps(done_data, ensure_ascii=False)}\n\ndata: [DONE]\n\n"
	
	async def analyze_json_data(self, request: JSONSummaryRequest) -> AsyncGenerator[str, None]:
		"""分析JSON数据（OpenAI SSE格式流式），输出标准SSE格式"""
		chunk_id = f"chatcmpl-{uuid.uuid4().hex[:8]}"
		
		try:
			# 格式化输入数据
			json_data_str = json.dumps(request.data, ensure_ascii=False, indent=2)
			field_definitions_str = self._format_field_definitions(request.fieldDefinitions)

			# 构建提示词（统一使用 data_summary_prompt）
			prompt = self.config['json_summary_prompts']['data_summary_prompt'].format(
				json_data=json_data_str,
				field_definitions=field_definitions_str,
				chart_type=request.chartType
			)

			if not self.api_key:
				raise HTTPException(status_code=500, detail="未配置API密钥，请在config.yaml的qwen_agent.api_key或环境变量OPENAI_API_KEY中设置")

			headers = {
				"Authorization": f"{self.api_key}",
				"Content-Type": "application/json"
			}
			payload = {
				"model": self.model,
				"messages": [
					{"role": "system", "content": self.system_prompt},
					{"role": "user", "content": prompt}
				],
				"stream": True
			}

			async with httpx.AsyncClient(timeout=None) as client:
				async with client.stream("POST", self.api_url, headers=headers, json=payload) as response:
					if response.status_code != 200:
						raise HTTPException(status_code=response.status_code, detail=await response.text())
					
					async for line in response.aiter_lines():
						if not line:
							continue
						if line.startswith("data: "):
							content = line[len("data: "):].strip()
							if content == "[DONE]":
								break
							try:
								json_data = json.loads(content)
								delta = json_data.get('choices', [{}])[0].get('delta', {})
								chunk = delta.get('content')
								if chunk:
									# 输出OpenAI兼容的SSE格式
									yield self._create_sse_chunk(chunk, chunk_id)
							except json.JSONDecodeError:
								# 不是JSON则按原文输出，但包装成SSE格式
								if content.strip():
									yield self._create_sse_chunk(content, chunk_id)
						await asyncio.sleep(0.01)
					
					# 发送结束标记
					yield self._create_sse_done(chunk_id)

		except Exception as e:
			print(f"[错误] JSON数据分析失败: {e}")
			# 错误也用SSE格式包装
			error_msg = f"错误: {str(e)}"
			yield self._create_sse_chunk(error_msg, chunk_id)
			yield self._create_sse_done(chunk_id)


# 全局分析器实例
analyzer = None

@asynccontextmanager
async def lifespan(app: FastAPI):
	"""应用生命周期管理"""
	# 启动时初始化
	global analyzer
	analyzer = JSONDataAnalyzer()
	print("[启动] JSON数据分析服务（OpenAI SSE格式）启动成功")
	yield
	# 关闭时清理（如果需要）
	print("[关闭] 服务正在关闭...")

# 创建FastAPI应用
app = FastAPI(
	title="JSON数据总结服务（OpenAI SSE格式）",
	description="基于OpenAI兼容SSE格式的JSON数据智能分析和总结",
	version="1.0.0",
	lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
	CORSMiddleware,
	allow_origins=["*"],
	allow_credentials=True,
	allow_methods=["*"],
	allow_headers=["*"],
)

@app.get("/")
async def root():
	"""根路径"""
	return {"message": "JSON数据总结服务运行中（OpenAI SSE格式）", "version": "1.0.0"}

@app.get("/health")
async def health_check():
	"""健康检查"""
	return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.post("/analyze")
async def analyze_json(request: JSONSummaryRequest):
	"""分析JSON数据（OpenAI SSE格式流式），输出标准SSE格式"""
	if not analyzer:
		raise HTTPException(status_code=500, detail="服务未初始化")
	try:
		return StreamingResponse(
			analyzer.analyze_json_data(request),
			media_type="text/event-stream",  # 标准SSE媒体类型
			headers={
				"Cache-Control": "no-cache",
				"Connection": "keep-alive",
				"X-Accel-Buffering": "no"  # 禁用nginx缓冲
			}
		)
	except Exception as e:
		print(f"[错误] JSON分析异常: {e}")
		raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
	# 创建日志目录
	os.makedirs("logs", exist_ok=True)
	
	# 读取配置中的server.host与server.port
	try:
		# 直接加载配置文件，避免重复初始化
		config_path = "config.yaml"
		if not os.path.isfile(config_path):
			config_path = os.path.join(os.path.dirname(__file__), config_path)
		
		with open(config_path, 'r', encoding='utf-8') as f:
			cfg = yaml.safe_load(f) or {}
		
		host = cfg.get('server', {}).get('host', '0.0.0.0')
		port = int(cfg.get('server', {}).get('port', 8191))  # 使用不同端口避免冲突
	except Exception as _e:
		print(f"[警告] 读取server配置失败，使用默认值。原因: {_e}")
		host = '0.0.0.0'
		port = 8191
	
	# 启动服务
	print(f"[启动] 服务启动在 http://{host}:{port}")
	print(f"[提示] 访问 http://{host}:{port}/health 检查服务状态")
	print(f"[提示] 调用 POST http://{host}:{port}/analyze 进行分析")
	print("[提示] 按 Ctrl+C 停止服务\n")
	
	uvicorn.run(
		"openaisse:app",
		host=host,
		port=port,
		reload=False,  # 关闭自动重载减少日志
		log_level="error"  # 只显示错误日志
	)
