# 智能问答模块配置文件（精简）

# Qwen-Agent配置（仅保留必要字段）
qwen_agent:
  model_server: "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
  api_key: "Bearer 236c2620-61bf-486e-842d-c4ec10110899"  # 建议通过环境变量注入
  model: "doubao-seed-1-6-250615"

# qwen_agent:
#   model_server: "http://************:8001/v1/chat/completions"
#   api_key: "EMPTY"  # 建议通过环境变量注入
#   model: "qwen3-14b"

# 服务器配置
server:
  host: "0.0.0.0"
  port: 8190

# JSON数据总结提示词模板
json_summary_prompts:
  # 系统提示词
  system_prompt: |
    你是一个专业的数据分析师，专门处理汽车行业VOC（Voice of Customer）数据。
    
    你的任务是：
    1. 分析输入的JSON数据，理解数据结构和含义
    2. 基于字段定义解释数据内容
    3. 提供专业的数据洞察和总结
    4. 识别数据中的关键趋势和模式
    5. 给出基于数据的建议和结论
    
    请始终以专业、准确、易懂的方式分析数据。

  # JSON数据总结提示词
  data_summary_prompt: |
    请分析以下JSON数据并生成一份运维汇报总结，参考以下示例风格：
    
    示例风格：
    "该业务截至2025年12月的报告显示，整体负荷率高达93.18%，总览量32910，用户数18509，数据趋势变化显示负荷率和提及量波动，尤其1月负荷率高但提及量下降，聚焦场景中，"爬坡动力性能"和"销售网点分布"负荷率均为53.27%且提及量较高，而"熔岩动力性能"和"成交价格用途"负荷率高达93.18%，整体运营负荷率较高，未来需关注提及量与负荷率的平衡，并深入分析高负荷场景。"
    
    JSON数据：
    {json_data}
    
    字段定义：
    {field_definitions}
    
    图表类型：{chart_type}
    
    要求：
    1. 以运维人员向领导汇报的口吻，模仿示例的表达方式和数据呈现风格
    2. 开头使用"该业务截至[时间]的报告显示"的格式
    3. 直接列举关键数据指标，用顿号、逗号自然分隔
    4. 突出数据趋势变化和异常情况，使用"尤其"、"其中"等连接词
    5. 对重要场景或分类加引号标注，并给出具体数值
    6. 结尾提出运营建议，使用"未来需关注"、"建议"等表述
    7. 字数控制在150-200字，一个自然段落，不使用标点符号之外的格式
    
    请严格按照示例风格生成汇报总结：

