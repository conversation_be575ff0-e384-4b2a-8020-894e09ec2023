# 智能回评模块

## 模块概述
智能回评模块是VOC2.0系统的扩展功能，用于自动生成用户评价的回复内容。基于智能打标模块识别出的观点和情感，生成个性化、针对性的回复。

## 功能特性

### 核心功能
1. **自动回复生成**
   - 基于观点分析结果生成回复
   - 支持多种回复语调（正式、亲和、专业等）
   - 针对不同情感倾向采用不同回复策略

2. **回复模板管理**
   - 可配置的回复模板库
   - 支持动态变量替换
   - 模板分类管理（表扬回复、抱怨处理、询问回答等）

3. **个性化定制**
   - 基于用户画像调整回复风格
   - 考虑车型、品牌等上下文信息
   - 支持A/B测试不同回复策略

### 技术架构预期

#### 输入数据
- 智能打标结果（观点、情感、意图）
- 用户基础信息
- 历史交互记录

#### 输出结果
- 结构化回复内容
- 回复质量评分
- 推荐的人工审核级别

#### 核心算法
- 自然语言生成（NLG）
- 模板匹配引擎
- 情感适应算法
- 个性化推荐

## 数据库设计预留

### 回复模板表 (reply_templates)
```sql
CREATE TABLE reply_templates (
    template_id BIGINT PRIMARY KEY,
    template_name VARCHAR(200),
    template_content TEXT,
    intent_type VARCHAR(50),  -- 对应意图类型
    sentiment_type VARCHAR(50), -- 对应情感类型
    car_brand VARCHAR(100),   -- 适用品牌
    variables JSON,           -- 模板变量定义
    quality_score FLOAT,      -- 模板质量评分
    usage_count INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

### 回复记录表 (reply_records)
```sql
CREATE TABLE reply_records (
    reply_id BIGINT PRIMARY KEY,
    topic_id VARCHAR(50),     -- 关联原始评价
    template_id BIGINT,       -- 使用的模板ID
    generated_reply TEXT,     -- 生成的回复内容
    manual_edited BOOLEAN DEFAULT FALSE, -- 是否经过人工编辑
    final_reply TEXT,         -- 最终发送的回复
    quality_score FLOAT,      -- 回复质量评分
    user_feedback INT,        -- 用户反馈（1-5分）
    status ENUM('generated', 'reviewed', 'sent', 'rejected'),
    created_at TIMESTAMP,
    sent_at TIMESTAMP
);
```

## API接口设计预留

### 生成回复
```
POST /api/v1/intelligent-review/generate
{
    "topic_id": "string",
    "analysis_result": {
        "entities": [...],
        "sentiment": "positive|negative|neutral",
        "intent": "praise|complaint|inquiry"
    },
    "user_context": {
        "user_id": "string",
        "preferences": {...}
    }
}

Response:
{
    "reply_id": "string", 
    "generated_reply": "string",
    "confidence_score": 0.95,
    "template_used": "template_name",
    "requires_review": false
}
```

### 获取回复模板
```
GET /api/v1/intelligent-review/templates
Query: intent, sentiment, brand

Response:
{
    "templates": [
        {
            "template_id": "string",
            "name": "string", 
            "content": "string",
            "variables": [...]
        }
    ]
}
```

## 服务架构预留

### 微服务结构
```
future-modules/intelligent-review/
├── services/
│   ├── reply-generation/     # 回复生成服务
│   ├── template-management/  # 模板管理服务
│   └── quality-assessment/   # 质量评估服务
├── models/
│   ├── nlg_model/           # 自然语言生成模型
│   └── quality_model/       # 质量评估模型
├── templates/
│   ├── praise_templates/    # 表扬类回复模板
│   ├── complaint_templates/ # 抱怨类回复模板
│   └── inquiry_templates/   # 询问类回复模板
└── configs/
    └── reply_config.yaml    # 回复配置
```

## 与现有系统集成

### 数据流集成
1. 接收智能打标系统的结构化输出
2. 结合用户上下文生成个性化回复
3. 将回复结果存储并推送到客服系统

### 消息队列集成
- 监听 `tagging_completed_topic`
- 发送到 `reply_generated_topic`
- 错误处理通过 `reply_error_topic`

## 开发计划预留

### Phase 1: 基础功能
- [ ] 模板引擎开发
- [ ] 基础回复生成算法
- [ ] 数据库表创建
- [ ] 基础API接口

### Phase 2: 智能化
- [ ] NLG模型集成
- [ ] 个性化算法
- [ ] 质量评估系统
- [ ] A/B测试框架

### Phase 3: 优化增强
- [ ] 多语言支持
- [ ] 高级个性化
- [ ] 性能优化
- [ ] 监控告警

## 依赖说明
- 依赖智能打标模块的输出结果
- 需要用户画像数据
- 需要客服系统集成接口
- 可选依赖大语言模型服务