# VOC2.0 部署脚本说明

## 📦 一键部署脚本

### 🎯 统一入口 (推荐使用)

```bash
./scripts/deploy-env.sh [environment]
```

**环境选项:**
- `dev` - 开发环境 (代码挂载，快速迭代)
- `prod` - 生产环境 (完整基础设施，稳定运行)
- `scaled` - 扩展部署 (多实例，高并发)

**使用示例:**
```bash
# 部署开发环境
./scripts/deploy-env.sh dev

# 部署生产环境  
./scripts/deploy-env.sh prod

# 部署扩展环境
./scripts/deploy-env.sh scaled

# 查看帮助
./scripts/deploy-env.sh --help
```

### 🔧 单独部署脚本

| 脚本 | 用途 | Docker Compose文件 |
|------|------|-------------------|
| `build-dev-env.sh` | 开发环境 | `docker-compose.dev.yml` |
| `build-prod-env.sh` | 生产环境 | `docker-compose.yml` |
| `build-scaled-env.sh` | 扩展部署 | `docker-compose.scaled.yml` |

## 🎭 环境对比

| 特性 | 开发环境 | 生产环境 | 扩展部署 |
|------|----------|----------|----------|
| **代码位置** | 挂载 | 镜像内 | 镜像内 |
| **基础设施** | 外部依赖 | 完整包含 | 完整包含 |
| **服务实例** | 单实例 | 单实例 | 多实例 |
| **修改代码** | 立即生效 | 需重建镜像 | 需重建镜像 |
| **适用场景** | 日常开发 | 测试/生产 | 高并发生产 |

## 🚀 快速开始

### 开发环境 (最常用)
```bash
# 一键部署开发环境
./scripts/deploy-env.sh dev

# 修改代码后自动重启，无需重新部署
# 查看日志
docker compose -f docker-compose.dev.yml logs -f uie-dev
```

### 生产环境
```bash  
# 一键部署生产环境
./scripts/deploy-env.sh prod

# 包含完整基础设施 (Kafka, ES, StarRocks等)
# 查看所有服务状态
docker compose -f docker-compose.yml ps
```

### 扩展部署 (高并发)
```bash
# 一键部署扩展环境  
./scripts/deploy-env.sh scaled

# 多实例服务 (UIE x3, LLM x2, Intent x2)
# 查看多实例状态
docker compose -f docker-compose.scaled.yml ps
```

## 📋 常用操作

### 查看服务状态
```bash
# 开发环境
docker compose -f docker-compose.dev.yml ps

# 生产环境  
docker compose -f docker-compose.yml ps

# 扩展部署
docker compose -f docker-compose.scaled.yml ps
```

### 查看日志
```bash
# 查看特定服务日志
docker compose -f [compose-file] logs -f [service-name]

# 查看所有服务日志
docker compose -f [compose-file] logs -f
```

### 重启服务
```bash
# 重启特定服务
docker compose -f [compose-file] restart [service-name]

# 重启所有服务
docker compose -f [compose-file] restart
```

### 停止环境
```bash
# 停止服务
docker compose -f [compose-file] down

# 停止服务并删除数据卷 (慎用!)
docker compose -f [compose-file] down -v
```

## ⚠️ 注意事项

1. **Windows环境**: 如果无法执行bash脚本，请使用Git Bash或WSL
2. **权限问题**: 如果遇到权限问题，请确保Docker守护进程运行
3. **端口冲突**: 确保所需端口未被其他程序占用
4. **外部API**: 扩展部署需要确保外部API服务可访问
5. **资源要求**: 扩展部署需要更多内存和CPU资源

## 🔧 故障排除

### 镜像构建失败
```bash
# 清理Docker缓存
docker system prune -f

# 重新构建镜像
docker build --no-cache -t voc-services:latest .
```

### 服务启动失败
```bash
# 查看具体错误信息
docker compose -f [compose-file] logs [service-name]

# 重新启动服务
docker compose -f [compose-file] up -d [service-name]
```

### 端口被占用
```bash
# 查看端口占用情况
netstat -tlnp | grep [port]

# 或者使用lsof (Linux/Mac)
lsof -i :[port]
```