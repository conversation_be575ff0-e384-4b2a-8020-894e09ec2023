#!/usr/bin/env python3
"""
生产环境Kafka Topics创建脚本 - 32C64G CPU服务器优化版本
支持多实例负载均衡：
- 3个Text Processor + 2个Rule Matcher + 2个Vector Matcher
- 6个UIE + 4个LLM + 4个Intent + 2个Brand Attribution + 2个Post Processor
包含删除原有topics功能，确保重新创建
"""

import sys
import logging
import os
import argparse
import socket

try:
    from kafka import KafkaClient, KafkaAdminClient
    from kafka.admin import NewTopic
    from kafka.errors import TopicAlreadyExistsError, UnknownTopicOrPartitionError
except ImportError:
    print("❌ 缺少 kafka-python 依赖")
    print("请运行: pip install kafka-python")
    sys.exit(1)

# 配置（支持参数与环境变量覆盖）
def parse_args():
    parser = argparse.ArgumentParser(description="生产环境Kafka Topics创建脚本")
    parser.add_argument(
        "--bootstrap",
        type=str,
        default=os.getenv(
            "KAFKA_BOOTSTRAP_SERVERS",
            "************:29092"
        ),
        help="Kafka bootstrap servers，逗号分隔"
    )
    parser.add_argument(
        "--delete-existing",
        action="store_true",
        default=True,
        help="删除已存在的topics（默认启用）"
    )
    return parser.parse_args()

args = parse_args()
KAFKA_BOOTSTRAP_SERVERS = [s.strip() for s in args.bootstrap.split(",") if s.strip()]

# 临时DNS别名：将 broker 的 advertised 主机名映射到可达IP，避免 DNS 解析失败
# 如无需该映射，可删除或改为环境变量控制
_REAL_GETADDRINFO = socket.getaddrinfo

def _patched_getaddrinfo(host, *args, **kwargs):
    # args: (port, family=0, type=0, proto=0, flags=0)
    if host in ('voc-kafka-service', 'voc-kafka-service.voc-test.svc.cluster.local'):
        new_host = '************'
        if len(args) >= 1:
            port = args[0]
            # 将 broker 广播的 9092 端口重写为 NodePort 29092
            new_port = 29092 if str(port) == '9092' else port
            new_args = (new_port,) + args[1:]
            return _REAL_GETADDRINFO(new_host, *new_args, **kwargs)
        return _REAL_GETADDRINFO(new_host, *args, **kwargs)
    return _REAL_GETADDRINFO(host, *args, **kwargs)

socket.getaddrinfo = _patched_getaddrinfo

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 生产环境多分区topics配置 - 32C64G CPU服务器优化版本
TOPICS_CONFIG = [
    {"name": "voc_toModel_topic", "partitions": 3, "replication_factor": 1, "description": "主要入口，原始任务投递 - 3个分区平衡吞吐和顺序"},
    {"name": "text_processor_topic", "partitions": 3, "replication_factor": 1, "description": "文本处理任务 - 3个实例"},
    {"name": "rule_matcher_topic", "partitions": 2, "replication_factor": 1, "description": "规则匹配任务 - 2个实例"},
    {"name": "vector_matcher_topic", "partitions": 2, "replication_factor": 1, "description": "向量匹配任务 - 2个实例"},
    {"name": "uie_topic", "partitions": 6, "replication_factor": 1, "description": "UIE小模型处理任务 - 6个实例"},
    {"name": "llm_topic", "partitions": 4, "replication_factor": 1, "description": "LLM大模型处理任务 - 4个实例"},
    {"name": "intent_topic", "partitions": 4, "replication_factor": 1, "description": "意图情感识别任务 - 4个实例"},
    {"name": "brand_attribution_topic", "partitions": 2, "replication_factor": 1, "description": "品牌归属判断任务 - 2个实例"},
    {"name": "result_topic", "partitions": 2, "replication_factor": 1, "description": "模型处理结果输出 - 2个分区提高吞吐"},
    {"name": "final_topic", "partitions": 2, "replication_factor": 1, "description": "最终结构化结果输出 - 2个分区"},
    {"name": "model_error_topic", "partitions": 1, "replication_factor": 1, "description": "模型错误统一处理"},
    {"name": "model_retry_topic", "partitions": 1, "replication_factor": 1, "description": "错误重试调度"},
]

def check_kafka_connection():
    """检查Kafka连接"""
    try:
        client = KafkaClient(bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS, api_version=(0, 10, 1))
        client.check_version()
        logger.info(f"✅ Kafka连接正常: {KAFKA_BOOTSTRAP_SERVERS}")
        client.close()
        return True
    except Exception as e:
        logger.error(f"❌ 无法连接到Kafka服务器: {KAFKA_BOOTSTRAP_SERVERS[0]}")
        logger.error(f"错误详情: {e}")
        return False

def delete_existing_topics():
    """删除已存在的VOC相关topics"""
    try:
        admin_client = KafkaAdminClient(
            bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS,
            client_id='voc_admin_prod'
        )
        
        # 获取现有topics列表
        from kafka import KafkaConsumer
        consumer = KafkaConsumer(bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS)
        existing_topic_names = list(consumer.topics())
        consumer.close()
        
        # 找出需要删除的VOC相关topics
        required_topic_names = [config['name'] for config in TOPICS_CONFIG]
        topics_to_delete = [topic for topic in required_topic_names if topic in existing_topic_names]
        
        if not topics_to_delete:
            logger.info("📋 没有需要删除的VOC topics")
            return True
        
        logger.info(f"🗑️ 准备删除 {len(topics_to_delete)} 个已存在的VOC topics...")
        for topic in topics_to_delete:
            logger.info(f"  🗑️ 将删除: {topic}")
        
        # 删除topics
        try:
            fs = admin_client.delete_topics(topics_to_delete, request_timeout_ms=30000)
            deleted_count = 0
            for topic_name, f in fs.items():
                try:
                    f.result()  # 等待删除结果
                    logger.info(f"  ✅ 已删除: {topic_name}")
                    deleted_count += 1
                except UnknownTopicOrPartitionError:
                    logger.info(f"  ⚡ {topic_name} - 不存在，跳过")
                    deleted_count += 1
                except Exception as e:
                    logger.error(f"  ❌ {topic_name} - 删除失败: {e}")
            
            logger.info(f"🎯 成功删除 {deleted_count}/{len(topics_to_delete)} 个topics")
            
            # 等待一下，确保删除完成
            import time
            time.sleep(3)
            
        except Exception as e:
            logger.warning(f"⚠️ 批量删除遇到问题: {e}")
        
        admin_client.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ 删除topics过程异常: {e}")
        return False

def create_topics():
    """创建所有需要的topics"""
    try:
        admin_client = KafkaAdminClient(
            bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS,
            client_id='voc_admin_prod'
        )
        
        logger.info(f"🚀 准备创建 {len(TOPICS_CONFIG)} 个生产环境topics...")
        logger.info("🗜️ 强制配置: compression.type=gzip，确保高效存储")
        
        # 创建所有topics - 强制使用gzip压缩
        topics_to_create = []
        topic_configs = {
            'compression.type': 'gzip',  # 强制使用gzip压缩
            'min.insync.replicas': '1',  # 最小同步副本数
            'cleanup.policy': 'delete',  # 日志清理策略
            'retention.ms': '604800000'  # 7天保留期(毫秒)
        }
        
        for topic_config in TOPICS_CONFIG:
            topic = NewTopic(
                name=topic_config['name'],
                num_partitions=topic_config['partitions'],
                replication_factor=topic_config['replication_factor'],
                topic_configs=topic_configs  # 添加topic配置
            )
            topics_to_create.append(topic)
        
        # 批量创建
        try:
            fs = admin_client.create_topics(topics_to_create, validate_only=False)
            created_count = 0
            for topic_name, f in fs.items():
                try:
                    f.result()  # 等待结果
                    topic_config = next(config for config in TOPICS_CONFIG if config['name'] == topic_name)
                    partitions = topic_config['partitions']
                    desc = topic_config['description']
                    logger.info(f"  ✅ {topic_name} ({partitions}分区,gzip压缩) - {desc}")
                    created_count += 1
                except TopicAlreadyExistsError:
                    logger.info(f"  ⚡ {topic_name} - 已存在")
                    created_count += 1
                except Exception as e:
                    logger.error(f"  ❌ {topic_name} - 创建失败: {e}")
            
            logger.info(f"🎯 成功处理 {created_count}/{len(topics_to_create)} 个topics")
            
        except Exception as e:
            # 即使批量创建失败，也可能是因为topics已存在
            logger.warning(f"⚠️ 批量创建遇到问题: {e}")
            logger.info("🔍 检查topics是否因为已存在而导致的'错误'...")
        
        admin_client.close()
        
        # 最终验证：重新检查所有必需的topics
        try:
            from kafka import KafkaConsumer
            consumer = KafkaConsumer(bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS)
            final_topic_names = list(consumer.topics())
            consumer.close()
            
            required_topics = [config['name'] for config in TOPICS_CONFIG]
            final_required = [t for t in required_topics if t in final_topic_names]
            success_count = len(final_required)
            
            logger.info("\n" + "="*60)
            logger.info("🎯 生产环境Kafka Topics部署验证")
            logger.info("="*60)
            logger.info(f"📊 VOC必需topics: {success_count}/{len(required_topics)} 已就绪")
            logger.info(f"⚡ 多分区设计: 支持多实例负载均衡")
            logger.info(f"🔧 32C64G CPU服务器实例配置:")
            logger.info(f"   - 基础服务: 3个Text Processor + 2个Rule/Vector Matcher")
            logger.info(f"   - 模型服务: 6个UIE + 4个LLM + 4个Intent")  
            logger.info(f"   - 业务服务: 2个Brand Attribution + 2个Post Processor")
            
            for topic_config in TOPICS_CONFIG:
                topic_name = topic_config['name']
                partitions = topic_config['partitions']
                desc = topic_config['description']
                if topic_name in final_topic_names:
                    logger.info(f"  ✅ {topic_name} ({partitions}分区,gzip压缩) - {desc}")
                else:
                    logger.error(f"  ❌ {topic_name} - 缺失")
            
            if success_count == len(required_topics):
                logger.info("🎉 所有VOC topics部署成功!")
                return True
            else:
                logger.error(f"❌ 仍有 {len(required_topics) - success_count} 个topics未就绪")
                return False
                
        except Exception as e:
            logger.error(f"❌ 最终验证失败: {e}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 创建topics过程异常: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始创建生产环境Kafka Topics - 32C64G CPU服务器优化版本")
    logger.info("💡 多分区配置，支持多实例负载均衡")
    logger.info("🔧 总计25个服务实例，充分利用32核CPU资源")
    logger.info("   - 基础服务: 3个Text + 2个Rule + 2个Vector Matcher")
    logger.info("   - 模型服务: 6个UIE + 4个LLM + 4个Intent")
    logger.info("   - 业务服务: 2个Brand Attribution + 2个Post Processor")
    
    # 检查连接
    if not check_kafka_connection():
        logger.error("❌ Kafka连接失败，请检查服务状态")
        return False
    
    # 删除已存在的topics（如果启用）
    if args.delete_existing:
        logger.info("\n🗑️ 步骤1: 删除已存在的VOC topics")
        if not delete_existing_topics():
            logger.error("❌ 删除existing topics失败")
            return False
    
    # 创建topics
    logger.info("\n🚀 步骤2: 创建生产环境topics")
    success = create_topics()
    
    if success:
        logger.info("\n🎉 生产环境Kafka Topics部署成功!")
        logger.info("💾 多分区配置，真正实现负载均衡")
        logger.info("🗜️ gzip压缩配置，优化存储效率")
        logger.info("🚀 现在可以启动多实例服务了")
        logger.info("\n📋 下一步:")
        logger.info("  docker-compose -f docker-compose.prod.yml up -d")
    else:
        logger.error("\n❌ Kafka Topics创建失败")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        exit_code = 0 if success else 1
        sys.exit(exit_code)
    except Exception as e:
        logger.error(f"脚本执行失败: {e}")
        sys.exit(1)
