#!/usr/bin/env python3
"""
优化版MySQL建表脚本
基于StarRocks建表脚本转换，专注分析能力，消除与ES的重复存储
"""

import sys
import os
import logging
import yaml

try:
    import pymysql
    import pymysql.cursors
except ImportError:
    print("❌ 缺少 pymysql 依赖")
    print("请运行: pip install pymysql")
    sys.exit(1)

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_config():
    """加载配置文件"""
    config_path = os.path.join(project_root, 'configs', 'config.yaml')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        logger.error(f"❌ 加载配置文件失败: {e}")
        return None

def get_mysql_config():
    """获取MySQL配置"""
    config = load_config()
    if not config or 'database' not in config or 'mysql' not in config['database']:
        logger.error("❌ 配置文件中缺少MySQL配置")
        return None
    
    mysql_config = config['database']['mysql']
    return {
        'host': mysql_config.get('host', 'localhost'),
        'port': mysql_config.get('port', 3306),
        'user': mysql_config.get('username', 'root'),
        'password': mysql_config.get('password', ''),
        'database': mysql_config.get('database', 'voc_db'),
        'charset': mysql_config.get('charset', 'utf8mb4')
    }

def get_optimized_tables():
    """获取算法后端MySQL表 - 包含核心算法表和后处理服务表"""
    mysql_config = get_mysql_config()
    if not mysql_config:
        return []
    
    database_name = mysql_config['database']
    return [
        f"CREATE DATABASE IF NOT EXISTS {database_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
        f"USE {database_name}",
        
        # 1. 观点近义词库 - 长安运维手动添加的主体+描述，映射到长安标准观点
        # 表结构按架构文档: Add_entity | Add_description | Standard_opinion | Standard_opinion_id
        """CREATE TABLE IF NOT EXISTS opinion_synonym (
            id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
            opinion_id VARCHAR(50) COMMENT '六级非规范观点ID',
            opinion TEXT COMMENT '六级非规范观点',
            entity_id VARCHAR(50) COMMENT '六级非规范观点主体ID',
            entity VARCHAR(200) COMMENT '六级非规范观点主体',
            description_id VARCHAR(50) COMMENT '六级非规范观点描述ID',
            description TEXT COMMENT '六级非规范观点描述',
            standard_opinion_id VARCHAR(50) COMMENT '五级标准观点ID',
            standard_opinion TEXT COMMENT '五级标准观点',
            status TINYINT DEFAULT 1 COMMENT '状态',
            opinion_type TINYINT DEFAULT 1 COMMENT '0-问卷观点-无主体描述不向量嵌入；1-常规观点-需向量嵌入；',
            created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            INDEX idx_opinion_id (opinion_id),
            INDEX idx_entity_id (entity_id),
            INDEX idx_standard_opinion_id (standard_opinion_id),
            INDEX idx_status (status),
            INDEX idx_standard_entity_desc (standard_opinion_id, entity, description(100))
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='观点近义词库'""",

        # 1.1 新词库 - 记录无法匹配的主体/描述，供人工审核
        # 字段映射: 主键ID=id | 原文=original_text | 片段=segment | 新观点主体=new_entity | 新观点描述=new_description | 新观点（主体+描述）=new_opinion | 推荐上级标签-五级标准观点=recommended_label | 处理状态位=status_flag
        """CREATE TABLE IF NOT EXISTS new_words (
            id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
            original_text TEXT COMMENT '原文',
            segment TEXT COMMENT '片段',
            new_entity VARCHAR(200) COMMENT '新观点主体',
            new_description TEXT COMMENT '新观点描述',
            new_opinion TEXT COMMENT '新观点（主体+描述）',
            recommended_label VARCHAR(200) COMMENT '推荐上级标签-五级标准观点',
            status TINYINT DEFAULT 0 COMMENT '处理状态位',
            created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            INDEX idx_status (status),
            INDEX idx_created_time (created_time)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新词库'""",
        
        # 2. 错误记录表 - 超过5次重试失败的数据存储
        # 表结构按架构文档: Topic_id | Topic_text | Retry_count | Error_type | Last_ts | Final_status
        """CREATE TABLE IF NOT EXISTS error_records (
            id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
            topic_id VARCHAR(100) COMMENT '主题ID',
            topic_text TEXT COMMENT '原始文本',
            retry_count INT DEFAULT 0 COMMENT '重试次数',
            error_type VARCHAR(100) COMMENT '错误类型',
            error_msg TEXT COMMENT '错误信息',
            last_ts DATETIME COMMENT '最后处理时间',
            status TINYINT DEFAULT 0 COMMENT '状态',
            created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            INDEX idx_topic_id (topic_id),
            INDEX idx_error_type (error_type),
            INDEX idx_status (status),
            INDEX idx_created_time (created_time)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='错误记录表'""",
        
        # 3. 二级情感规则关键词表 - 后处理服务用
        """CREATE TABLE IF NOT EXISTS sentiment_rule_keywords (
            id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
            primary_sentiment VARCHAR(20) NOT NULL COMMENT '一级情感类型：正面/负面/中性',
            secondary_sentiment VARCHAR(20) NOT NULL COMMENT '二级情感类型：高/中/一般',
            rule_type VARCHAR(50) COMMENT '类型',
            rule_keywords TEXT NOT NULL COMMENT '规则关键词，用于匹配和分类',
            status TINYINT DEFAULT 1 COMMENT '状态',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            INDEX idx_primary_sentiment (primary_sentiment),
            INDEX idx_secondary_sentiment (secondary_sentiment),
            INDEX idx_rule_type (rule_type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='二级情感规则关键词表'""",
        
        # 4. 用车场景规则关键词表 - 后处理服务用
        """CREATE TABLE IF NOT EXISTS scenario_rule_keywords (
            id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
            primary_scenario VARCHAR(50) NOT NULL COMMENT '一级场景：操作情景/路况等',
            secondary_scenario VARCHAR(50) NOT NULL COMMENT '二级场景：低速行驶/湿滑路面等',
            rule_keywords TEXT NOT NULL COMMENT '规则关键词，用于匹配和分类',
            status TINYINT DEFAULT 1 COMMENT '状态',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            INDEX idx_primary_scenario (primary_scenario),
            INDEX idx_secondary_scenario (secondary_scenario)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用车场景规则关键词表'""",

        # 5. 否定词表 - 文本处理与规则匹配通用
        """CREATE TABLE IF NOT EXISTS negation_words (
            id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
            word VARCHAR(100) NOT NULL COMMENT '否定词',
            status TINYINT DEFAULT 1 COMMENT '状态',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='否定词表'""",

        # 6. 品牌车系过滤规则表 - 智能过滤误匹配
        """CREATE TABLE IF NOT EXISTS brand_series_filter_rules (
            id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
            rule_type ENUM('global_blacklist', 'context_rule', 'ambiguous_pattern') NOT NULL COMMENT '规则类型：全局黑名单、上下文规则、歧义模式',
            brand_name VARCHAR(50) COMMENT '品牌名（上下文规则用）',
            series_name VARCHAR(50) COMMENT '车系名（上下文规则用）',
            pattern VARCHAR(200) NOT NULL COMMENT '匹配模式或黑名单词汇',
            required_context VARCHAR(200) COMMENT '必需的上下文词汇（逗号分隔）',
            blacklist_context VARCHAR(500) COMMENT '黑名单上下文词汇（逗号分隔）',
            priority INT DEFAULT 100 COMMENT '优先级（数字越小优先级越高）',
            status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
            description TEXT COMMENT '规则描述',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            INDEX idx_rule_type (rule_type),
            INDEX idx_brand_series (brand_name, series_name),
            INDEX idx_pattern (pattern),
            INDEX idx_priority (priority),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='品牌车系过滤规则表'""",
    ]

def get_initial_data():
    """保留占位：初始数据插入已移至专门的脚本push_mysql_brand_series_filter_rules.py"""
    # 不再在建表阶段插入数据，避免重复插入问题
    # 使用专门的脚本来管理过滤规则的插入和更新
    return []

def connect_mysql():
    """连接MySQL"""
    mysql_config = get_mysql_config()
    if not mysql_config:
        return None
    
    try:
        connection = pymysql.connect(
            host=mysql_config['host'],
            port=mysql_config['port'],
            user=mysql_config['user'],
            password=mysql_config['password'],
            charset=mysql_config['charset'],
            cursorclass=pymysql.cursors.DictCursor,
            autocommit=True
        )
        logger.info(f"✅ 连接MySQL成功: {mysql_config['host']}:{mysql_config['port']}")
        return connection
    except Exception as e:
        logger.error(f"❌ 连接MySQL失败: {e}")
        return None

def execute_sql_statements(connection, statements, description):
    """执行SQL语句"""
    success_count = 0
    failed_statements = []
    
    logger.info(f"\n🚀 开始执行{description}...")
    
    with connection.cursor() as cursor:
        for i, sql in enumerate(statements, 1):
            sql = sql.strip()
            if not sql or sql == ";":
                continue
                
            try:
                display_sql = sql.replace("\n", " ").replace("  ", " ")[:80] + "..." if len(sql) > 80 else sql
                logger.info(f"[{i:2d}/{len(statements)}] 执行: {display_sql}")
                
                cursor.execute(sql)
                logger.info("  ✅ 成功")
                success_count += 1
                
            except Exception as e:
                error_msg = str(e)[:100] + "..." if len(str(e)) > 100 else str(e)
                logger.error(f"  ❌ 失败: {error_msg}")
                failed_statements.append((i, sql[:100], str(e)))
    
    logger.info(f"📊 {description}完成: {success_count}/{len(statements)} 成功")
    
    if failed_statements:
        logger.warning(f"⚠️  失败的语句数量: {len(failed_statements)}")
        for idx, sql_preview, error in failed_statements[:2]:
            logger.warning(f"  第{idx}条: {sql_preview}... -> {error[:50]}...")
    
    return success_count, failed_statements

def show_tables(connection):
    """显示创建的表"""
    try:
        mysql_config = get_mysql_config()
        if not mysql_config:
            return 0
            
        database_name = mysql_config['database']
        with connection.cursor() as cursor:
            cursor.execute(f"USE {database_name}")
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            
            if tables:
                logger.info(f"\n📋 成功创建的MySQL表 ({len(tables)}个):")
                for table in tables:
                    table_name = list(table.values())[0]
                    if table_name == 'opinion_synonym':
                        logger.info(f"  📝 {table_name} - 长安运维手动维护的观点近义词")
                    elif table_name == 'new_words':
                        logger.info(f"  📚 {table_name} - 新词库，记录无法匹配的主体/描述")
                    elif table_name == 'error_records':
                        logger.info(f"  ❌ {table_name} - 超过5次重试失败的错误数据")
                    elif table_name == 'sentiment_rule_keywords':
                        logger.info(f"  😊 {table_name} - 二级情感规则关键词表")
                    elif table_name == 'scenario_rule_keywords':
                        logger.info(f"  🚗 {table_name} - 用车场景规则关键词表")
                    elif table_name == 'brand_series_filter_rules':
                        logger.info(f"  🔍 {table_name} - 品牌车系过滤规则表")
                    else:
                        logger.info(f"  📊 {table_name}")
            else:
                logger.warning("📋 没有创建任何表")
            
            return len(tables)
    except Exception as e:
        logger.error(f"获取表信息失败: {e}")
        return 0

def show_table_structure(connection):
    """显示表结构信息"""
    try:
        mysql_config = get_mysql_config()
        if not mysql_config:
            return
            
        database_name = mysql_config['database']
        with connection.cursor() as cursor:
            cursor.execute(f"USE {database_name}")
            
            tables = ['opinion_synonym', 'new_words', 'error_records', 'sentiment_rule_keywords', 'scenario_rule_keywords', 'brand_series_filter_rules']
            
            logger.info(f"\n🔍 表结构详情:")
            for table_name in tables:
                try:
                    cursor.execute(f"SHOW CREATE TABLE {table_name}")
                    result = cursor.fetchone()
                    if result:
                        logger.info(f"\n📋 {table_name} 表结构:")
                        create_sql = result['Create Table']
                        # 格式化显示，每行不超过100字符
                        lines = create_sql.split('\n')
                        for line in lines:
                            if len(line) > 100:
                                logger.info(f"  {line[:100]}...")
                            else:
                                logger.info(f"  {line}")
                except Exception as e:
                    logger.warning(f"  ⚠️  无法获取 {table_name} 表结构: {e}")
                    
    except Exception as e:
        logger.error(f"获取表结构失败: {e}")

def main():
    """主函数"""
    logger.info("🚀 创建算法后端和后处理服务MySQL数据表")
    logger.info("💡 完整设计: 包含算法后端核心表和后处理服务表")
    logger.info("📋 表结构: 完全按照架构文档要求设计，适配MySQL语法")
    
    connection = connect_mysql()
    if not connection:
        return False
    
    try:
        # 创建表
        table_statements = get_optimized_tables()
        table_success, table_failures = execute_sql_statements(connection, table_statements, "分析表创建")
        
        # 插入初始数据
        if table_success >= 5:  # 如果主要表创建成功（包括数据库和USE语句）
            data_statements = get_initial_data()
            data_success, data_failures = execute_sql_statements(connection, data_statements, "初始数据插入")
        else:
            data_success = 0
        
        # 显示结果
        table_count = show_tables(connection)
        
        # 显示表结构（可选）
        if table_count >= 5:
            show_table_structure(connection)
        
        # 总结
        total_success = table_success + data_success
        total_statements = len(table_statements) + (len(get_initial_data()) if table_success >= 5 else 0)
        
        logger.info("\n" + "="*60)
        logger.info("🎯 算法后端和后处理服务MySQL数据库部署总结")
        logger.info("="*60)
        logger.info(f"📊 成功创建表: {table_count} 个")
        logger.info(f"📝 观点近义词库: 长安运维手动维护的主体+描述映射")
        logger.info(f"📚 新词库: 记录无法匹配的主体/描述，供人工审核")
        logger.info(f"❌ 错误记录库: 超过5次重试失败的数据存储")
        logger.info(f"😊 二级情感规则关键词表: 一级情感类型、二级情感类型、规则关键词")
        logger.info(f"🚗 用车场景规则关键词表: 一级场景、二级场景、规则关键词")
        logger.info(f"💾 精简设计: 包含算法后端和后处理服务核心表")
        logger.info(f"⚡ SQL成功率: {total_success}/{total_statements if total_statements > 0 else len(table_statements)} 条")
        logger.info(f"🔧 MySQL特性: 使用InnoDB引擎，支持事务和索引优化")
        
        if table_count >= 6:
            logger.info("✅ 算法后端和后处理服务表部署成功!")
            logger.info("🎯 表结构完全按照需求设计，适配MySQL语法")
            logger.info("🔄 支持观点近义词更新、错误数据管理和后处理分析")
            logger.info("🔍 包含智能品牌车系过滤规则，解决误匹配问题")
            logger.info("📈 包含完整的索引设计，优化查询性能")
            return True
        else:
            logger.error("❌ 核心表创建失败")
            return False
        
    except Exception as e:
        logger.error(f"❌ 部署过程异常: {e}")
        return False
    finally:
        connection.close()
        logger.info("🔌 数据库连接已关闭")

if __name__ == "__main__":
    try:
        success = main()
        exit_code = 0 if success else 1
        sys.exit(exit_code)
    except Exception as e:
        logger.error(f"脚本执行失败: {e}")
        sys.exit(1)
