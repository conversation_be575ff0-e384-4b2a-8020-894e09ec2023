#!/usr/bin/env python3
"""
轻量级Kafka Topics创建脚本 - 每个topic只用1个partition
适合开发和测试环境，减少资源占用
"""

import sys
import logging
import os
import argparse
import socket

try:
    from kafka import KafkaClient, KafkaAdminClient
    from kafka.admin import NewTopic
    from kafka.errors import TopicAlreadyExistsError
except ImportError:
    print("❌ 缺少 kafka-python 依赖")
    print("请运行: pip install kafka-python")
    sys.exit(1)

# 配置（支持参数与环境变量覆盖）
def parse_args():
    parser = argparse.ArgumentParser(description="轻量级Kafka Topics创建脚本")
    parser.add_argument(
        "--bootstrap",
        type=str,
        default=os.getenv(
            "KAFKA_BOOTSTRAP_SERVERS",
            "************:29092"
        ),
        help="Kafka bootstrap servers，逗号分隔"
    )
    return parser.parse_args()

args = parse_args()
KAFKA_BOOTSTRAP_SERVERS = [s.strip() for s in args.bootstrap.split(",") if s.strip()]

# 临时DNS别名：将 broker 的 advertised 主机名映射到可达IP，避免 DNS 解析失败
# 如无需该映射，可删除或改为环境变量控制
_REAL_GETADDRINFO = socket.getaddrinfo

def _patched_getaddrinfo(host, *args, **kwargs):
    # args: (port, family=0, type=0, proto=0, flags=0)
    if host in ('voc-kafka-service', 'voc-kafka-service.voc-test.svc.cluster.local'):
        new_host = '************'
        if len(args) >= 1:
            port = args[0]
            # 将 broker 广播的 9092 端口重写为 NodePort 29092
            new_port = 29092 if str(port) == '9092' else port
            new_args = (new_port,) + args[1:]
            return _REAL_GETADDRINFO(new_host, *new_args, **kwargs)
        return _REAL_GETADDRINFO(new_host, *args, **kwargs)
    return _REAL_GETADDRINFO(host, *args, **kwargs)

socket.getaddrinfo = _patched_getaddrinfo

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 轻量级topics配置 - 每个只用1个partition
TOPICS_CONFIG = [
    {"name": "voc_toModel_topic", "partitions": 1, "replication_factor": 1, "description": "主要入口，原始任务投递"},
    {"name": "uie_topic", "partitions": 1, "replication_factor": 1, "description": "UIE小模型处理任务"},
    {"name": "llm_topic", "partitions": 1, "replication_factor": 1, "description": "LLM大模型处理任务"},
    {"name": "intent_topic", "partitions": 1, "replication_factor": 1, "description": "意图情感识别任务"},
    {"name": "brand_attribution_topic", "partitions": 1, "replication_factor": 1, "description": "品牌归属判断任务"},
    {"name": "result_topic", "partitions": 1, "replication_factor": 1, "description": "模型处理结果输出"},
    {"name": "final_topic", "partitions": 1, "replication_factor": 1, "description": "最终结构化结果输出"},
    {"name": "model_error_topic", "partitions": 1, "replication_factor": 1, "description": "模型错误统一处理"},
    {"name": "model_retry_topic", "partitions": 1, "replication_factor": 1, "description": "错误重试调度"},
]

def check_kafka_connection():
    """检查Kafka连接"""
    try:
        client = KafkaClient(bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS, api_version=(0, 10, 1))
        client.check_version()
        logger.info(f"✅ Kafka连接正常: {KAFKA_BOOTSTRAP_SERVERS}")
        client.close()
        return True
    except Exception as e:
        logger.error(f"❌ 无法连接到Kafka服务器: {KAFKA_BOOTSTRAP_SERVERS[0]}")
        logger.error(f"错误详情: {e}")
        return False

def list_existing_topics():
    """列出现有的topics"""
    try:
        admin_client = KafkaAdminClient(
            bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS,
            client_id='voc_admin'
        )
        metadata = admin_client.describe_topics([topic['name'] for topic in TOPICS_CONFIG])
        existing_topics = []
        for topic_name, topic_metadata in metadata.items():
            if topic_metadata:
                partitions = len(topic_metadata.partitions)
                existing_topics.append({
                    'name': topic_name,
                    'partitions': partitions
                })
        admin_client.close()
        return existing_topics
    except Exception as e:
        logger.warning(f"获取现有topics失败: {e}")
        return []

def create_topics():
    """创建所有需要的topics"""
    try:
        admin_client = KafkaAdminClient(
            bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS,
            client_id='voc_admin'
        )
        
        # 获取现有topics列表
        try:
            from kafka import KafkaConsumer
            consumer = KafkaConsumer(bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS)
            existing_topic_names = list(consumer.topics())
            consumer.close()
        except Exception as e:
            logger.warning(f"获取现有topics列表失败: {e}")
            existing_topic_names = []
        
        # 显示现有的VOC相关topics
        voc_topics = [t for t in existing_topic_names if any(keyword in t for keyword in ['topic', 'voc', 'cuizong', 'uie', 'llm'])]
        if voc_topics:
            logger.info("📋 现有VOC相关topics:")
            for topic in voc_topics:
                logger.info(f"  📝 {topic}")
        
        # 检查需要的topics状态
        required_topics = [config['name'] for config in TOPICS_CONFIG]
        existing_required = [t for t in required_topics if t in existing_topic_names]
        missing_topics = [t for t in required_topics if t not in existing_topic_names]
        
        logger.info(f"📊 VOC必需topics状态: {len(existing_required)}/{len(required_topics)} 已存在")
        
        if len(existing_required) == len(required_topics):
            logger.info("✅ 所有VOC topics已存在，部署完成！")
            for topic in existing_required:
                desc = next(config['description'] for config in TOPICS_CONFIG if config['name'] == topic)
                logger.info(f"  ✅ {topic} - {desc}")
            return True
        
        if missing_topics:
            logger.info(f"🚀 准备创建 {len(missing_topics)} 个缺失的topics...")
            
            # 创建缺失的topics
            topics_to_create = []
            for topic_config in TOPICS_CONFIG:
                if topic_config['name'] in missing_topics:
                    topic = NewTopic(
                        name=topic_config['name'],
                        num_partitions=topic_config['partitions'],
                        replication_factor=topic_config['replication_factor']
                    )
                    topics_to_create.append(topic)
            
            # 批量创建
            try:
                fs = admin_client.create_topics(topics_to_create, validate_only=False)
                created_count = 0
                for topic_name, f in fs.items():
                    try:
                        f.result()  # 等待结果
                        desc = next(config['description'] for config in TOPICS_CONFIG if config['name'] == topic_name)
                        logger.info(f"  ✅ {topic_name} - {desc}")
                        created_count += 1
                    except TopicAlreadyExistsError:
                        logger.info(f"  ⚡ {topic_name} - 已存在")
                        created_count += 1
                    except Exception as e:
                        logger.error(f"  ❌ {topic_name} - 创建失败: {e}")
                
                logger.info(f"🎯 成功处理 {created_count}/{len(topics_to_create)} 个topics")
                
            except Exception as e:
                # 即使批量创建失败，也可能是因为topics已存在
                logger.warning(f"⚠️ 批量创建遇到问题: {e}")
                logger.info("🔍 检查topics是否因为已存在而导致的'错误'...")
        
        admin_client.close()
        
        # 最终验证：重新检查所有必需的topics
        try:
            consumer = KafkaConsumer(bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS)
            final_topic_names = list(consumer.topics())
            consumer.close()
            
            final_required = [t for t in required_topics if t in final_topic_names]
            success_count = len(final_required)
            
            logger.info("\n" + "="*50)
            logger.info("🎯 轻量级Kafka Topics部署验证")
            logger.info("="*50)
            logger.info(f"📊 VOC必需topics: {success_count}/{len(required_topics)} 已就绪")
            logger.info(f"⚡ 轻量级设计: 每个topic 1 partition")
            
            for topic in required_topics:
                if topic in final_topic_names:
                    desc = next(config['description'] for config in TOPICS_CONFIG if config['name'] == topic)
                    logger.info(f"  ✅ {topic} - {desc}")
                else:
                    logger.error(f"  ❌ {topic} - 缺失")
            
            if success_count == len(required_topics):
                logger.info("🎉 所有VOC topics部署成功!")
                return True
            else:
                logger.error(f"❌ 仍有 {len(required_topics) - success_count} 个topics未就绪")
                return False
                
        except Exception as e:
            logger.error(f"❌ 最终验证失败: {e}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 创建topics过程异常: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始创建轻量级Kafka Topics")
    logger.info("💡 优化: 每个topic只用1个partition，适合开发环境")
    
    # 检查连接
    if not check_kafka_connection():
        logger.error("❌ Kafka连接失败，请检查服务状态")
        return False
    
    # 创建topics
    success = create_topics()
    
    if success:
        logger.info("\n🎉 轻量级Kafka Topics部署成功!")
        logger.info("💾 资源占用最小化，适合开发测试")
        logger.info("🔧 生产环境时可考虑增加partition数量")
    else:
        logger.error("\n❌ Kafka Topics创建失败")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        exit_code = 0 if success else 1
        sys.exit(exit_code)
    except Exception as e:
        logger.error(f"脚本执行失败: {e}")
        sys.exit(1)