# Elasticsearch测试数据推送脚本

这个脚本用于为VOC2.0智能打标系统的Elasticsearch索引推送完整的测试数据。

## 功能说明

脚本会为以下4个索引推送测试数据：

1. **entity_vectors** - 主体向量索引
   - 包含8个测试主体（大灯、内饰、动力、空间、油耗、外观、座椅、噪音）
   - 每个主体包含512维随机向量和对应的标准观点ID

2. **description_vectors** - 描述向量索引
   - 包含12个测试描述（很亮、好看、塑料感强、做工精细等）
   - 每个描述包含512维随机向量和对应的标准观点ID

3. **brand_series** - 品牌车系索引
   - 包含长安、奇瑞、比亚迪、吉利等品牌的车系数据
   - 支持车型、同义词等完整信息

4. **rule_patterns** - 规则模式索引
   - 包含8条规则模式（大灯-亮、内饰-塑料感等）
   - 支持精确匹配和模糊匹配类型

## 使用方法

### 方法1: 使用Python脚本（推荐）

```bash
# 推送测试数据（默认操作）
python scripts/test/push_es_test_data.py

# 推送测试数据
python scripts/test/push_es_test_data.py push

# 验证现有数据
python scripts/test/push_es_test_data.py verify

# 测试搜索功能
python scripts/test/push_es_test_data.py search

# 显示帮助信息
python scripts/test/push_es_test_data.py help
```

### 方法2: 使用Shell脚本（Linux/Mac）

```bash
# 推送测试数据（默认操作）
./scripts/test/run_es_test.sh

# 推送测试数据
./scripts/test/run_es_test.sh push

# 验证现有数据
./scripts/test/run_es_test.sh verify

# 测试搜索功能
./scripts/test/run_es_test.sh search

# 显示帮助信息
./scripts/test/run_es_test.sh help
```

### 方法3: 快速测试脚本

如果你只想快速测试ES连接和基本功能：

```bash
# 快速测试（推送少量数据）
python scripts/test/quick_test_es.py
```

### 方法4: Windows PowerShell

```powershell
# 进入项目根目录
cd D:\工作\changan_voc-1

# 推送测试数据
python scripts/test/push_es_test_data.py push

# 验证数据
python scripts/test/push_es_test_data.py verify

# 测试搜索
python scripts/test/push_es_test_data.py search
```

## 前置条件

1. **Elasticsearch服务运行中**
   - 确保ES服务在 `http://*************:9200` 运行
   - 可以通过访问该地址验证服务状态

2. **向量化API服务（可选）**
   - 向量API地址: `http://*************:7777/bge-small`
   - 如果API不可用，脚本会自动使用随机向量作为备用方案
   - API输入格式: `{"texts": ["文本内容"]}`

3. **Python依赖已安装**
   ```bash
   pip install elasticsearch pydantic httpx
   ```

4. **配置文件正确**
   - 确保 `configs/config.yaml` 中的ES配置正确

## 验证步骤

运行脚本后，你可以通过以下方式验证数据：

### 1. 使用脚本验证
```bash
python scripts/test/push_es_test_data.py verify
```

### 2. 直接查询ES
```bash
# 查看所有索引
curl -X GET "*************:9200/_cat/indices?v"

# 查看特定索引数据量
curl -X GET "*************:9200/entity_vectors/_count"
curl -X GET "*************:9200/description_vectors/_count"
curl -X GET "*************:9200/brand_series/_count"
curl -X GET "*************:9200/rule_patterns/_count"

# 查看示例数据
curl -X GET "*************:9200/entity_vectors/_search?size=3&pretty"
```

### 3. 测试搜索功能
```bash
python scripts/test/push_es_test_data.py search
```

## 输出示例

成功运行后，你会看到类似输出：

```
2024-01-01 12:00:00 - __main__ - INFO - 开始推送测试数据到Elasticsearch...
2024-01-01 12:00:01 - __main__ - INFO - 推送主体向量数据...
2024-01-01 12:00:01 - __main__ - INFO - 主体向量数据推送成功: entity_001 - 大灯
2024-01-01 12:00:01 - __main__ - INFO - 主体向量数据推送成功: entity_002 - 内饰
...
2024-01-01 12:00:05 - __main__ - INFO - 刷新所有索引...
2024-01-01 12:00:06 - __main__ - INFO - 验证推送的数据...
2024-01-01 12:00:06 - __main__ - INFO - 索引 entity_vectors 数据量: 8
2024-01-01 12:00:07 - __main__ - INFO - 索引 description_vectors 数据量: 12
2024-01-01 12:00:07 - __main__ - INFO - 索引 brand_series 数据量: 7
2024-01-01 12:00-08 - __main__ - INFO - 索引 rule_patterns 数据量: 8
2024-01-01 12:00:08 - __main__ - INFO - 测试数据推送完成!
```

## 故障排除

### 1. 连接失败
- 检查ES服务是否运行
- 检查网络连接
- 确认ES地址和端口正确

### 2. 权限错误
- 检查ES是否需要认证
- 更新 `configs/config.yaml` 中的用户名密码

### 3. 索引创建失败
- 检查ES版本兼容性
- 查看ES日志获取详细错误信息

### 4. 向量维度错误
- 确保向量维度与ES索引配置一致（默认512维）
- 检查embedding服务是否正常

## 数据结构说明

### 主体向量数据示例
```json
{
  "entity_id": "entity_001",
  "sim_entity": "大灯",
  "normalized_entity": "前大灯",
  "vector": [0.1, -0.2, 0.3, ...],
  "standard_opinion_ids": ["opinion_001", "opinion_002"],
  "is_active": true,
  "created_at": "2024-01-01T12:00:00"
}
```

### 描述向量数据示例
```json
{
  "description_id": "desc_001",
  "sim_description": "很亮",
  "normalized_description": "亮度好",
  "vector": [0.1, -0.2, 0.3, ...],
  "standard_opinion_id": "opinion_001",
  "is_active": true,
  "created_at": "2024-01-01T12:00:00"
}
```

### 品牌车系数据示例
```json
{
  "id": "brand_001",
  "brand": "长安",
  "series": "CS75 PLUS",
  "model": "2024款 2.0T 自动豪华型",
  "synonyms": "CS75+ CS75PLUS",
  "full_text": "长安 CS75 PLUS 2024款 2.0T 自动豪华型",
  "is_active": true
}
```

### 规则模式数据示例
```json
{
  "pattern_id": "rule_001",
  "entity_pattern": "大灯",
  "description_pattern": "亮",
  "combined_pattern": "大灯 亮",
  "standard_opinion_id": "opinion_001",
  "match_type": "exact",
  "priority": 10,
  "is_active": true
}
```

## 注意事项

1. **数据覆盖**: 脚本会覆盖已存在的相同ID数据
2. **向量生成**: 脚本使用随机向量，实际使用时应该使用真实的embedding向量
3. **测试用途**: 此数据仅用于测试，生产环境需要使用真实数据
4. **索引刷新**: 脚本会自动刷新索引以确保数据立即可查询
