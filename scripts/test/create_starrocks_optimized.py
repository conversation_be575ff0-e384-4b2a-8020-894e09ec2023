#!/usr/bin/env python3
"""
优化版StarRocks建表脚本
专注分析能力，消除与ES的重复存储
"""

import sys
import logging

try:
    import pymysql
    import pymysql.cursors
except ImportError:
    print("❌ 缺少 pymysql 依赖")
    print("请运行: pip install pymysql")
    sys.exit(1)

# 配置
STARROCKS_HOST = "*************"
STARROCKS_PORT = 9030
STARROCKS_USER = "root"
STARROCKS_PASSWORD = ""

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_optimized_tables():
    """获取算法后端StarRocks表 - 包含核心算法表和后处理服务表"""
    return [
        "CREATE DATABASE IF NOT EXISTS voc_db",
        "USE voc_db",
        
        # 1. 观点近义词库 - 长安运维手动添加的主体+描述，映射到长安标准观点
        # 表结构按架构文档: Add_entity | Add_description | Standard_opinion | Standard_opinion_id
        """CREATE TABLE IF NOT EXISTS opinion_synonym (
            id BIGINT COMMENT '主键ID',
            opinion_id VARCHAR(50) COMMENT '六级非规范观点ID',
            opinion STRING COMMENT '六级非规范观点',
            entity_id VARCHAR(50) COMMENT '六级非规范观点主体ID',
            entity VARCHAR(200) COMMENT '六级非规范观点主体',
            description_id VARCHAR(50) COMMENT '六级非规范观点描述ID',
            description VARCHAR(500) COMMENT '六级非规范观点描述',
            standard_opinion_id VARCHAR(50) COMMENT '五级标准观点ID',
            standard_opinion STRING COMMENT '五级标准观点',
            status TINYINT COMMENT '状态',
            created_time DATETIME COMMENT '创建时间',
            updated_time DATETIME COMMENT '更新时间'
        ) ENGINE=OLAP
        DUPLICATE KEY(id)
        DISTRIBUTED BY HASH(id) BUCKETS 2
        PROPERTIES ("replication_num" = "1")""",

        # 1.1 新词库 - 记录无法匹配的主体/描述，供人工审核
        # 字段映射: 主键ID=id | 原文=original_text | 片段=segment | 新观点主体=new_entity | 新观点描述=new_description | 新观点（主体+描述）=new_opinion | 推荐上级标签-五级标准观点=recommended_label | 处理状态位=status_flag
        """CREATE TABLE IF NOT EXISTS new_words (
            id BIGINT COMMENT '主键ID',
            original_text STRING COMMENT '原文',
            segment STRING COMMENT '片段',
            new_entity STRING COMMENT '新观点主体',
            new_description STRING COMMENT '新观点描述',
            new_opinion STRING COMMENT '新观点（主体+描述）',
            recommended_label STRING COMMENT '推荐上级标签-五级标准观点',
            status_flag TINYINT COMMENT '处理状态位',
            created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
        ) ENGINE=OLAP
        DUPLICATE KEY(id)
        DISTRIBUTED BY HASH(id) BUCKETS 2
        PROPERTIES ("replication_num" = "1")""",
        
        # 2. 错误记录表 - 超过5次重试失败的数据存储
        # 表结构按架构文档: Topic_id | Topic_text | Retry_count | Error_type | Last_ts | Final_status
        """CREATE TABLE IF NOT EXISTS error_records (
            id BIGINT,
            topic_id VARCHAR(100) COMMENT '主题ID',
            topic_text STRING COMMENT '原始文本',
            retry_count INT COMMENT '重试次数',
            error_type VARCHAR(100) COMMENT '错误类型',
            error_msg STRING COMMENT '错误信息',
            last_ts DATETIME COMMENT '最后处理时间',
            final_status VARCHAR(50) COMMENT '最终状态',
            created_time DATETIME COMMENT '创建时间'
        ) ENGINE=OLAP
        DUPLICATE KEY(id)
        DISTRIBUTED BY HASH(id) BUCKETS 2
        PROPERTIES ("replication_num" = "1")""",
        
        # 3. 二级情感规则关键词表 - 后处理服务用
        """CREATE TABLE IF NOT EXISTS sentiment_rule_keywords (
            id BIGINT NOT NULL,
            primary_sentiment VARCHAR(20) NOT NULL COMMENT '一级情感类型：正面/负面/中性',
            secondary_sentiment VARCHAR(20) NOT NULL COMMENT '二级情感类型：高/中/一般',
            rule_type VARCHAR(50) COMMENT '类型',
            rule_keywords TEXT NOT NULL COMMENT '规则关键词，用于匹配和分类',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=OLAP
        DUPLICATE KEY(id, primary_sentiment, secondary_sentiment)
        DISTRIBUTED BY HASH(id) BUCKETS 3
        PROPERTIES ("replication_num" = "1")""",
        
        # 4. 用车场景规则关键词表 - 后处理服务用
        """CREATE TABLE IF NOT EXISTS scenario_rule_keywords (
            id BIGINT NOT NULL,
            primary_scenario VARCHAR(50) NOT NULL COMMENT '一级场景：操作情景/路况等',
            secondary_scenario VARCHAR(50) NOT NULL COMMENT '二级场景：低速行驶/湿滑路面等',
            rule_keywords TEXT NOT NULL COMMENT '规则关键词，用于匹配和分类',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=OLAP
        DUPLICATE KEY(id, primary_scenario, secondary_scenario)
        DISTRIBUTED BY HASH(id) BUCKETS 3
        PROPERTIES ("replication_num" = "1")""",
    ]

def get_initial_data():
    """保留占位：不再在建表阶段插入任何示例数据"""
    return []

def connect_starrocks():
    """连接StarRocks"""
    try:
        connection = pymysql.connect(
            host=STARROCKS_HOST,
            port=STARROCKS_PORT,
            user=STARROCKS_USER,
            password=STARROCKS_PASSWORD,
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor,
            autocommit=True
        )
        logger.info(f"✅ 连接StarRocks成功: {STARROCKS_HOST}:{STARROCKS_PORT}")
        return connection
    except Exception as e:
        logger.error(f"❌ 连接StarRocks失败: {e}")
        return None

def execute_sql_statements(connection, statements, description):
    """执行SQL语句"""
    success_count = 0
    failed_statements = []
    
    logger.info(f"\n🚀 开始执行{description}...")
    
    with connection.cursor() as cursor:
        for i, sql in enumerate(statements, 1):
            sql = sql.strip()
            if not sql or sql == ";":
                continue
                
            try:
                display_sql = sql.replace("\n", " ").replace("  ", " ")[:80] + "..." if len(sql) > 80 else sql
                logger.info(f"[{i:2d}/{len(statements)}] 执行: {display_sql}")
                
                cursor.execute(sql)
                logger.info("  ✅ 成功")
                success_count += 1
                
            except Exception as e:
                error_msg = str(e)[:100] + "..." if len(str(e)) > 100 else str(e)
                logger.error(f"  ❌ 失败: {error_msg}")
                failed_statements.append((i, sql[:100], str(e)))
    
    logger.info(f"📊 {description}完成: {success_count}/{len(statements)} 成功")
    
    if failed_statements:
        logger.warning(f"⚠️  失败的语句数量: {len(failed_statements)}")
        for idx, sql_preview, error in failed_statements[:2]:
            logger.warning(f"  第{idx}条: {sql_preview}... -> {error[:50]}...")
    
    return success_count, failed_statements

def show_tables(connection):
    """显示创建的表"""
    try:
        with connection.cursor() as cursor:
            cursor.execute("USE voc_db")
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            
            if tables:
                logger.info(f"\n📋 成功创建的StarRocks表 ({len(tables)}个):")
                for table in tables:
                    table_name = list(table.values())[0]
                    if table_name == 'opinion_synonym':
                        logger.info(f"  📝 {table_name} - 长安运维手动维护的观点近义词")
                    elif table_name == 'error_records':
                        logger.info(f"  ❌ {table_name} - 超过5次重试失败的错误数据")
                    elif table_name == 'sentiment_rule_keywords':
                        logger.info(f"  😊 {table_name} - 二级情感规则关键词表")
                    elif table_name == 'scenario_rule_keywords':
                        logger.info(f"  🚗 {table_name} - 用车场景规则关键词表")
                    else:
                        logger.info(f"  📊 {table_name}")
            else:
                logger.warning("📋 没有创建任何表")
            
            return len(tables)
    except Exception as e:
        logger.error(f"获取表信息失败: {e}")
        return 0

def main():
    """主函数"""
    logger.info("🚀 创建算法后端和后处理服务StarRocks数据表")
    logger.info("💡 完整设计: 包含算法后端核心表和后处理服务表")
    logger.info("📋 表结构: 完全按照架构文档要求设计")
    
    connection = connect_starrocks()
    if not connection:
        return False
    
    try:
        # 创建表
        table_statements = get_optimized_tables()
        table_success, table_failures = execute_sql_statements(connection, table_statements, "分析表创建")
        
        # 插入初始数据
        if table_success >= 5:  # 如果主要表创建成功（包括数据库和USE语句）
            data_statements = get_initial_data()
            data_success, data_failures = execute_sql_statements(connection, data_statements, "初始数据插入")
        else:
            data_success = 0
        
        # 显示结果
        table_count = show_tables(connection)
        
        # 总结
        total_success = table_success + data_success
        total_statements = len(table_statements) + (len(get_initial_data()) if table_success >= 5 else 0)
        
        logger.info("\n" + "="*60)
        logger.info("🎯 算法后端和后处理服务StarRocks数据库部署总结")
        logger.info("="*60)
        logger.info(f"📊 成功创建表: {table_count} 个")
        logger.info(f"📝 观点近义词库: 长安运维手动维护的主体+描述映射")
        logger.info(f"❌ 错误记录库: 超过5次重试失败的数据存储")
        logger.info(f"😊 二级情感规则关键词表: 一级情感类型、二级情感类型、规则关键词")
        logger.info(f"🚗 用车场景规则关键词表: 一级场景、二级场景、规则关键词")
        logger.info(f"💾 精简设计: 包含算法后端和后处理服务核心表")
        logger.info(f"⚡ SQL成功率: {total_success}/{total_statements if total_statements > 0 else len(table_statements)} 条")
        
        if table_count >= 4:
            logger.info("✅ 算法后端和后处理服务表部署成功!")
            logger.info("🎯 表结构完全按照需求设计")
            logger.info("🔄 支持观点近义词更新、错误数据管理和后处理分析")
            return True
        else:
            logger.error("❌ 核心表创建失败")
            return False
        
    except Exception as e:
        logger.error(f"❌ 部署过程异常: {e}")
        return False
    finally:
        connection.close()
        logger.info("🔌 数据库连接已关闭")

if __name__ == "__main__":
    try:
        success = main()
        exit_code = 0 if success else 1
        sys.exit(exit_code)
    except Exception as e:
        logger.error(f"脚本执行失败: {e}")
        sys.exit(1)