#!/usr/bin/env python3
"""
优化版Elasticsearch索引创建脚本
专注搜索能力，消除与StarRocks的重复存储
"""

import sys
import logging
import requests
from datetime import datetime

# 配置（生产环境集群DNS与认证）
ES_HOST = "*************"
ES_PORT = 9200
ES_USERNAME = ""
ES_PASSWORD = ""
ES_URL = f"http://{ES_HOST}:{ES_PORT}"

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_optimized_indices():
    """获取优化后的ES索引配置 - 算法后端核心搜索索引"""
    return {
        
        # 2. 主体近义词库 - 按照架构文档表结构设计
        "voc_entity_synonym": {
            "description": "主体近义词库（Entity_id|Sim_entity|vector|Normalized_entity|Standard_opinion_id_list）",
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0
            },
            "mappings": {
                "properties": {
                    "entity_id": {"type": "keyword"},
                    "sim_entity": {"type": "keyword"},
                    "vector": {
                        "type": "dense_vector",
                        "dims": 512,
                        "similarity": "cosine"
                    },
                    "standard_opinion_id_list": {
                        "type": "text",
                        "index": False  # 不索引，纯存储，提高性能
                    },
                    "status": {"type": "integer"},
                    "created_time": {"type": "date"},
                    "updated_time": {"type": "date"}
                }
            }
        },
        
        # 3. 描述近义词库 - 按照架构文档表结构设计
        "voc_description_synonym": {
            "description": "描述近义词库（Description_id|Sim_description|vector|Standard_opinion_id）",
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0
            },
            "mappings": {
                "properties": {
                    "description_id": {"type": "keyword"},
                    "sim_description": {"type": "keyword"},
                    "vector": {
                        "type": "dense_vector",
                        "dims": 512,
                        "similarity": "cosine"
                    },
                    "standard_opinion_id": {"type": "keyword"},
                    "status": {"type": "integer"},
                    "created_time": {"type": "date"},
                    "updated_time": {"type": "date"}
                }
            }
        }
    }

def create_optimized_indices():
    """创建优化后的索引"""
    try:
        # 检查ES连接
        response = requests.get(ES_URL, auth=(ES_USERNAME, ES_PASSWORD), timeout=5)
        if response.status_code != 200:
            logger.error(f"❌ 无法连接到Elasticsearch: {ES_URL}")
            return False
        
        es_info = response.json()
        logger.info(f"✅ Elasticsearch连接正常")
        logger.info(f"ES版本: {es_info.get('version', {}).get('number', 'unknown')}")
        
        indices = get_optimized_indices()
        success_count = 0
        
        logger.info(f"\n🚀 开始创建算法后端ES索引 (5个核心索引)...")
        
        for index_name, config in indices.items():
            try:
                # 删除已存在的索引
                delete_url = f"{ES_URL}/{index_name}"
                requests.delete(delete_url, auth=(ES_USERNAME, ES_PASSWORD))
                
                # 创建新索引
                create_url = f"{ES_URL}/{index_name}"
                index_config = {
                    "settings": config["settings"],
                    "mappings": config["mappings"]
                }
                
                response = requests.put(
                    create_url,
                    json=index_config,
                    headers={"Content-Type": "application/json"},
                    auth=(ES_USERNAME, ES_PASSWORD),
                    timeout=30
                )
                
                if response.status_code in [200, 201]:
                    logger.info(f"✅ {index_name} - {config['description']}")
                    success_count += 1
                else:
                    logger.error(f"❌ {index_name} 创建失败: {response.text[:100]}")
                    
            except Exception as e:
                logger.error(f"❌ {index_name} 创建异常: {e}")
        
        # 验证创建结果
        try:
            response = requests.get(
                f"{ES_URL}/_cat/indices/voc_*?v&h=index,docs.count,store.size",
                auth=(ES_USERNAME, ES_PASSWORD),
                timeout=10
            )
            if response.status_code == 200:
                logger.info(f"\n📊 当前VOC索引状态:")
                for line in response.text.strip().split('\n'):
                    if line.strip() and 'index' not in line:
                        logger.info(f"  📝 {line}")
        except Exception as e:
            logger.warning(f"获取索引状态失败: {e}")
        
        logger.info("\n" + "="*60)
        logger.info("🎯 算法后端Elasticsearch索引创建完成!")
        logger.info("="*60)
        logger.info(f"📊 创建成功: {success_count}/2 个索引")
        logger.info(f"🎯 主体近义词库: Entity_id|Sim_entity|vector|Normalized_entity|Standard_opinion_id_list")
        logger.info(f"📝 描述近义词库: Description_id|Sim_description|vector|Normalized_description|Standard_opinion_id")
        
        if success_count >= 2:
            logger.info("✅ 算法后端核心搜索功能已就绪!")
            return True
        else:
            logger.error("❌ 索引创建失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 创建过程异常: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 创建算法后端Elasticsearch索引")
    logger.info("💡 专注核心: 主体描述近义词")
    
    success = create_optimized_indices()
    
    if success:
        logger.info("\n🎉 算法后端ES索引部署成功!")
        logger.info("🔍 搜索能力: 2个核心索引支持完整智能打标流程")
        logger.info("⚡ 向量搜索: 2个索引支持512维ANN语义匹配")
    else:
        logger.error("\n❌ ES索引创建失败")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        exit_code = 0 if success else 1
        sys.exit(exit_code)
    except Exception as e:
        logger.error(f"脚本执行失败: {e}")
        sys.exit(1)