#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本：验证过滤规则推送功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

def test_push_rules():
    """测试推送规则"""
    print("🚀 开始测试过滤规则推送功能")
    print("=" * 60)
    
    # 导入推送脚本
    try:
        from scripts.test.push_mysql_brand_series_filter_rules import main as push_main
        print("✅ 成功导入推送脚本")
    except ImportError as e:
        print(f"❌ 导入推送脚本失败: {e}")
        return False
    
    # 执行推送
    try:
        success = push_main()
        if success:
            print("✅ 规则推送测试成功!")
            return True
        else:
            print("❌ 规则推送测试失败")
            return False
    except Exception as e:
        print(f"❌ 推送过程异常: {e}")
        return False

def test_create_table():
    """测试创建表"""
    print("\n🔧 测试数据库表创建")
    print("=" * 60)
    
    try:
        from scripts.test.create_mysql_optimized import main as create_main
        print("✅ 成功导入建表脚本")
    except ImportError as e:
        print(f"❌ 导入建表脚本失败: {e}")
        return False
    
    try:
        success = create_main()
        if success:
            print("✅ 数据库表创建测试成功!")
            return True
        else:
            print("❌ 数据库表创建测试失败")
            return False
    except Exception as e:
        print(f"❌ 建表过程异常: {e}")
        return False

def main():
    """主函数"""
    print("🧪 智能过滤规则部署测试")
    print("测试目标：验证数据库表创建和规则推送功能")
    print("=" * 80)
    
    # 测试建表
    table_success = test_create_table()
    
    if table_success:
        # 测试推送规则
        push_success = test_push_rules()
        
        if push_success:
            print("\n" + "=" * 80)
            print("🎉 所有测试通过!")
            print("✅ 数据库表创建成功")
            print("✅ 过滤规则推送成功")
            print("🎯 现在可以启动text-processor服务进行功能测试")
            print("=" * 80)
            return True
    
    print("\n" + "=" * 80)
    print("❌ 测试失败")
    print("💡 请检查数据库连接和配置")
    print("=" * 80)
    return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"测试脚本异常: {e}")
        sys.exit(1)
