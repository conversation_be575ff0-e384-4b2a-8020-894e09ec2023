#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MySQL测试数据推送脚本 - 符合VOC2.0架构
为VOC2.0智能打标系统的MySQL数据库推送完整的测试数据
"""

import asyncio
import json
import logging
import sys
import os
from typing import List, Dict, Any
from datetime import datetime
import pymysql
import pymysql.cursors
try:
    import openpyxl  # 读取Excel
except Exception:
    openpyxl = None

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

import yaml

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def load_config():
    """加载配置文件"""
    config_path = os.path.join(project_root, 'configs', 'config.yaml')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        logger.error(f"❌ 加载配置文件失败: {e}")
        return None

def get_mysql_config():
    """获取MySQL配置"""
    config = load_config()
    if not config or 'database' not in config or 'mysql' not in config['database']:
        logger.error("❌ 配置文件中缺少MySQL配置")
        return None
    
    mysql_config = config['database']['mysql']
    return {
        'host': mysql_config.get('host', 'localhost'),
        'port': mysql_config.get('port', 3306),
        'user': mysql_config.get('username', 'root'),
        'password': mysql_config.get('password', ''),
        'database': mysql_config.get('database', 'voc_db'),
        'charset': mysql_config.get('charset', 'utf8mb4')
    }


class MySQLClient:
    """MySQL数据库客户端"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.connection = None
    
    async def connect(self):
        """连接MySQL数据库"""
        try:
            self.connection = pymysql.connect(
                host=self.config['host'],
                port=self.config['port'],
                user=self.config['user'],
                password=self.config['password'],
                database=self.config['database'],
                charset=self.config['charset'],
                cursorclass=pymysql.cursors.DictCursor,
                autocommit=True
            )
            logger.info(f"✅ MySQL连接成功: {self.config['host']}:{self.config['port']}/{self.config['database']}")
            return True
        except Exception as e:
            logger.error(f"❌ MySQL连接失败: {e}")
            return False
    
    async def execute_query(self, sql: str, params: tuple = None) -> List[Dict]:
        """执行查询SQL"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(sql, params)
                return cursor.fetchall()
        except Exception as e:
            logger.error(f"查询执行失败: {sql}, 错误: {e}")
            return []
    
    async def execute_insert(self, sql: str, params: tuple = None) -> bool:
        """执行插入SQL"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(sql, params)
                self.connection.commit()
                return True
        except Exception as e:
            logger.error(f"插入执行失败: {sql}, 错误: {e}")
            return False
    
    async def execute_batch_insert(self, sql: str, params_list: List[tuple]) -> int:
        """批量插入数据"""
        try:
            with self.connection.cursor() as cursor:
                affected_rows = cursor.executemany(sql, params_list)
                self.connection.commit()
                logger.info(f"批量插入成功: {affected_rows} 行")
                return affected_rows
        except Exception as e:
            logger.error(f"批量插入失败: {sql}, 错误: {e}")
            return 0
    
    async def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            logger.info("🔌 MySQL连接已关闭")


def get_opinion_synonym_test_data() -> List[tuple]:
    """获取观点近义词库测试数据 - 匹配opinion_synonym表结构
    
    根据字段映射表：
    - id: 主键ID
    - opinion_id: 六级非规范观点ID
    - opinion: 六级非规范观点
    - entity_id: 六级非规范观点主体ID
    - entity: 六级非规范观点主体
    - description_id: 六级非规范观点描述ID
    - description: 六级非规范观点描述
    - standard_opinion_id: 五级标准观点ID
    - standard_opinion: 五级标准观点
    - status: 状态
    - opinion_type: 观点类型(0-问卷观点-无主体描述不向量嵌入；1-常规观点-需向量嵌入)
    - created_time: 创建时间
    - updated_time: 更新时间
    """
    return [
        (1, 'opinion_001', '大灯好看', 'entity_001', '大灯', 'desc_001', '好看', 'std_opinion_001', '前大灯外观设计好看', 1, 1, datetime.now(), datetime.now()),
        (2, 'opinion_002', '内饰丑', 'entity_002', '内饰', 'desc_002', '丑', 'std_opinion_002', '内饰设计风格丑', 1, 1, datetime.now(), datetime.now()),
        (3, 'opinion_003', '动力强劲', 'entity_003', '动力', 'desc_003', '强劲', 'std_opinion_003', '发动机动力性能强劲', 1, 1, datetime.now(), datetime.now()),
        (4, 'opinion_004', '空间宽敞', 'entity_004', '空间', 'desc_004', '宽敞', 'std_opinion_004', '车内乘坐空间宽敞', 1, 1, datetime.now(), datetime.now()),
        (5, 'opinion_005', '油耗省', 'entity_005', '油耗', 'desc_005', '省', 'std_opinion_005', '燃油经济性省', 1, 1, datetime.now(), datetime.now()),
        (6, 'opinion_006', '座椅舒适', 'entity_006', '座椅', 'desc_006', '舒适', 'std_opinion_006', '座椅舒适性舒适', 1, 1, datetime.now(), datetime.now()),
        (7, 'opinion_007', '操控精准', 'entity_007', '操控', 'desc_007', '精准', 'std_opinion_007', '操控性能精准', 1, 1, datetime.now(), datetime.now()),
        (8, 'opinion_008', '噪音安静', 'entity_008', '噪音', 'desc_008', '安静', 'std_opinion_008', '车内噪音控制安静', 1, 1, datetime.now(), datetime.now()),
        (9, 'opinion_009', '配置丰富', 'entity_009', '配置', 'desc_009', '丰富', 'std_opinion_009', '车辆配置丰富', 1, 1, datetime.now(), datetime.now()),
        (10, 'opinion_010', '价格合理', 'entity_010', '价格', 'desc_010', '合理', 'std_opinion_010', '价格性价比合理', 1, 1, datetime.now(), datetime.now()),
        # 添加一些问卷观点类型的测试数据 (opinion_type=0)
        (11, 'opinion_011', '整体满意度高', 'entity_011', '整体满意度', 'desc_011', '高', 'std_opinion_011', '整体满意度评价高', 1, 0, datetime.now(), datetime.now()),
        (12, 'opinion_012', '推荐指数好', 'entity_012', '推荐指数', 'desc_012', '好', 'std_opinion_012', '推荐指数评价好', 1, 0, datetime.now(), datetime.now())
    ]


def get_new_words_test_data() -> List[tuple]:
    """获取新词库测试数据 - 匹配new_words表结构"""
    return [
        (1, '这辆车的大灯设计很独特，晚上看起来很酷炫', '大灯设计很独特', '大灯', '设计很独特', '大灯设计很独特', '前大灯外观设计好看', 0, datetime.now(), datetime.now()),
        (2, '内饰的材质摸起来很舒服，质感不错', '内饰的材质摸起来很舒服', '内饰', '材质摸起来很舒服', '内饰材质摸起来很舒服', '内饰材质质感好', 0, datetime.now(), datetime.now()),
        (3, '发动机声音听起来很有力量感', '发动机声音听起来很有力量感', '发动机', '声音听起来很有力量感', '发动机声音听起来很有力量感', '发动机动力性能强劲', 0, datetime.now(), datetime.now()),
        (4, '后排空间比想象中要大很多', '后排空间比想象中要大很多', '后排空间', '比想象中要大很多', '后排空间比想象中要大很多', '车内乘坐空间宽敞', 0, datetime.now(), datetime.now()),
        (5, '油耗表现超出预期，很经济实用', '油耗表现超出预期', '油耗', '表现超出预期', '油耗表现超出预期', '燃油经济性省', 0, datetime.now(), datetime.now())
    ]


def get_sentiment_rule_keywords_test_data() -> List[tuple]:
    """从Excel按行读取情感规则关键词（第二列=一级情感，第三列=二级情感，第四列=规则关键词）。

    文件: 项目根目录 `情感层级分类定义.xlsx`
    规则关键词按分隔符拆分为多条（每个关键词一行）
    """
    excel_path = os.path.join(project_root, '情感层级分类定义.xlsx')

    if openpyxl is None:
        logger.error("缺少 openpyxl 依赖，请先安装: pip install openpyxl")
        raise RuntimeError("openpyxl not installed")
    if not os.path.exists(excel_path):
        logger.error(f"未找到Excel文件: {excel_path}")
        raise FileNotFoundError(excel_path)

    workbook = openpyxl.load_workbook(excel_path, data_only=True)

    def split_keywords(text: Any) -> List[str]:
        if text is None:
            return []
        normalized = str(text)
        for sep in ['\n', '、', '，', '；', ';', ',']:
            normalized = normalized.replace(sep, ',')
        normalized = normalized.replace('\u3000', ' ').replace('  ', ' ')
        parts: List[str] = []
        for seg in normalized.split(','):
            seg = seg.strip()
            if not seg:
                continue
            if ' ' in seg:
                for s in seg.split(' '):
                    s = s.strip()
                    if s:
                        parts.append(s)
            else:
                parts.append(seg)
        return parts

    rows: List[tuple] = []
    next_id = 1
    for sheet in workbook.worksheets:
        is_first_row = True
        # 默认列索引（兼容旧格式）：0=一级情感，1=二级情感，3=规则关键词；类型列可选
        idx_primary = 0
        idx_secondary = 1
        idx_keywords = 3
        idx_type = 4
        for row in sheet.iter_rows(values_only=True):
            if not row or len(row) < 4:
                continue
            if is_first_row:
                is_first_row = False
                headers = [str(v).strip() if v is not None else '' for v in row]
                header_set = set(headers)
                # 如果首行看起来是表头，则根据表头名定位列，并跳过该行
                if header_set & {'主项', '子项', '相关描述', '一级情感', '二级情感', '规则关键词', '情感', '级别', '关键词', '类型'}:
                    def find_idx(names: List[str], default: int) -> int:
                        for name in names:
                            if name in headers:
                                return headers.index(name)
                        return default
                    idx_primary = find_idx(['一级情感', '情感'], 0)
                    idx_secondary = find_idx(['二级情感', '级别'], 1)
                    idx_keywords = find_idx(['规则关键词', '关键词', '相关描述'], 3)
                    idx_type = headers.index('类型') if '类型' in headers else None
                    continue

            # 提取各列
            try:
                primary = row[idx_primary]
                secondary = row[idx_secondary]
                desc = row[idx_keywords]
                rule_type = (row[idx_type] if idx_type is not None and idx_type < len(row) else None)
            except Exception:
                continue
            if not primary or not secondary:
                continue

            keywords = split_keywords(desc)
            if not keywords:
                keywords = [str(secondary).strip()]
            for kw in keywords:
                rows.append((
                    next_id,
                    str(primary).strip(),
                    str(secondary).strip(),
                    (str(rule_type).strip() if rule_type is not None and str(rule_type).strip() != '' else None),
                    kw,
                    1,
                    datetime.now(),
                    datetime.now()
                ))
                next_id += 1

    logger.info(f"从Excel读取情感规则关键词: {len(rows)} 条 (按行读取 2/3/4 列)")
    return rows


def get_scenario_rule_keywords_test_data() -> List[tuple]:
    """从Excel按行读取(第二列=一级场景, 第三列=二级场景, 第四列=规则关键词)。

    - 文件: 项目根目录 `汽车场景分析结构体系-0422.xlsx`
    - 规则关键词按 "、，；; , 换行 空格" 拆分，生成"每个关键词一行"
    """
    excel_path = os.path.join(project_root, '汽车场景分析结构体系-0422.xlsx')

    if openpyxl is None:
        logger.error("缺少 openpyxl 依赖，请先安装: pip install openpyxl")
        raise RuntimeError("openpyxl not installed")
    if not os.path.exists(excel_path):
        logger.error(f"未找到Excel文件: {excel_path}")
        raise FileNotFoundError(excel_path)

    workbook = openpyxl.load_workbook(excel_path, data_only=True)

    def split_keywords(text: Any) -> List[str]:
        if text is None:
            return []
        normalized = str(text)
        for sep in ['\n', '、', '，', '；', ';', ',']:
            normalized = normalized.replace(sep, ',')
        normalized = normalized.replace('\u3000', ' ').replace('  ', ' ')
        parts: List[str] = []
        for seg in normalized.split(','):
            seg = seg.strip()
            if not seg:
                continue
            if ' ' in seg:
                for s in seg.split(' '):
                    s = s.strip()
                    if s:
                        parts.append(s)
            else:
                parts.append(seg)
        return parts

    rows: List[tuple] = []
    next_id = 1

    # 仅处理指定工作表：用车场景指标
    try:
        target_sheet = workbook['用车场景指标']
    except KeyError:
        logger.error("未找到名为 '用车场景指标' 的工作表，请检查Excel文件")
        raise

    for sheet in [target_sheet]:
        is_first_row = True
        for row in sheet.iter_rows(values_only=True):
            if not row or len(row) < 4:
                continue
            # 跳过首行表头（第二/三/四列为列名）
            if is_first_row:
                is_first_row = False
                header_vals = [str(v).strip() if v is not None else '' for v in row[:4]]
                expected_headers = {'分类', '主项', '子项', '相关描述'}
                if set(header_vals) >= expected_headers:
                    # 表头正确，跳过
                    continue
                else:
                    logger.error(f"工作表 '{sheet.title}' 首行表头不符合要求，期望包含: {expected_headers}，实际: {header_vals}")
                    raise ValueError("Excel表头不符合要求：必须为 '分类/主项/子项/相关描述'")

            primary = row[1]
            secondary = row[2]
            desc = row[3]
            if not primary or not secondary:
                continue
            keywords = split_keywords(desc)
            if not keywords:
                keywords = [str(secondary).strip()]
            for kw in keywords:
                rows.append((next_id, str(primary).strip(), str(secondary).strip(), kw, 1, datetime.now(), datetime.now()))
                next_id += 1

    logger.info(f"从Excel读取场景规则关键词: {len(rows)} 条 (按行读取 2/3/4 列)")
    return rows


def get_error_records_test_data() -> List[tuple]:
    """获取错误记录测试数据 - 匹配error_records表结构"""
    return [
        (1, 'topic_001', '这是一条测试错误记录文本，用于验证错误处理功能', 5, 'PROCESSING_ERROR', '处理超时，重试5次后失败', datetime.now(), 0, datetime.now(), datetime.now()),
        (2, 'topic_002', '另一条测试错误记录，模拟网络连接问题', 3, 'NETWORK_ERROR', '网络连接超时', datetime.now(), 0, datetime.now(), datetime.now()),
        (3, 'topic_003', '第三条测试错误记录，模拟数据格式错误', 2, 'FORMAT_ERROR', '数据格式不正确', datetime.now(), 0, datetime.now(), datetime.now())
    ]


def get_negation_words_from_excel() -> List[tuple]:
    """从项目根目录的 否定词表.xlsx 读取否定词，生成插入数据。

    表头与列要求（两列）：
    - 第1列：规则关键词（映射到 negation_words.word）
    - 第2列：状态位（映射到 negation_words.status，取 0/1，无法解析则默认 1）

    其他规则：
    - 如果首行包含表头（否定词/关键词/word/规则关键词、状态/状态位/status），自动跳过
    - 去除空白与重复空行
    - id 采用连续自增（仅用于测试数据装载；表本身也为自增）
    """
    excel_path = os.path.join(project_root, '否定词表.xlsx')
    rows: List[tuple] = []
    if openpyxl is None:
        logger.error("缺少 openpyxl 依赖，请先安装: pip install openpyxl")
        raise RuntimeError("openpyxl not installed")
    if not os.path.exists(excel_path):
        logger.error(f"未找到Excel文件: {excel_path}")
        return rows

    wb = openpyxl.load_workbook(excel_path, data_only=True)
    sheet = wb.worksheets[0]

    next_id = 1
    first_row_checked = False
    for row in sheet.iter_rows(values_only=True):
        if not row:
            continue
        word_cell = row[0]
        status_cell = row[1] if len(row) > 1 else None
        if word_cell is None:
            continue
        word = str(word_cell).strip()
        if not word:
            continue
        if not first_row_checked:
            first_row_checked = True
            header1 = word.lower()
            header2 = str(status_cell).strip().lower() if status_cell is not None else ""
            if header1 in ("否定词", "关键词", "规则关键词", "word", "词", "negation", "negation_word") or header2 in ("状态", "状态位", "status"):
                # 跳过表头
                continue

        # 状态位解析
        status_val = 1
        if status_cell is not None:
            try:
                status_val = int(str(status_cell).strip())
            except Exception:
                status_val = 1

        rows.append((next_id, word, status_val, datetime.now(), datetime.now()))
        next_id += 1

    logger.info(f"从否定词表读取: {len(rows)} 条")
    return rows


async def push_test_data():
    """推送测试数据到MySQL"""
    try:
        # 获取MySQL配置
        mysql_config = get_mysql_config()
        if not mysql_config:
            logger.error("无法获取MySQL配置")
            return False
        
        # 创建MySQL客户端
        mysql_client = MySQLClient(mysql_config)
        
        # 连接数据库
        if not await mysql_client.connect():
            logger.error("无法连接到MySQL数据库")
            return False
        
        logger.info("开始推送测试数据到MySQL (VOC2.0架构)...")
        
        # 1. 观点近义词库数据 - 可选插入测试数据（通常由长安运维手动维护）
        logger.info("推送观点近义词库测试数据...")
        opinion_synonym_data = get_opinion_synonym_test_data()
        opinion_synonym_sql = """
            INSERT INTO opinion_synonym 
            (id, opinion_id, opinion, entity_id, entity, description_id, description, standard_opinion_id, standard_opinion, status, opinion_type, created_time, updated_time) 
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        success_count = await mysql_client.execute_batch_insert(opinion_synonym_sql, opinion_synonym_data)
        logger.info(f"观点近义词库测试数据推送完成: {success_count} 条记录")
        
        # 2. 推送新词库数据
        logger.info("推送新词库数据...")
        new_words_data = get_new_words_test_data()
        new_words_sql = """
            INSERT INTO new_words 
            (id, original_text, segment, new_entity, new_description, new_opinion, recommended_label, status, created_time, updated_time) 
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        success_count = await mysql_client.execute_batch_insert(new_words_sql, new_words_data)
        logger.info(f"新词库数据推送完成: {success_count} 条记录")
        
        # 3. 推送二级情感规则关键词数据
        logger.info("推送二级情感规则关键词数据...")
        sentiment_data = get_sentiment_rule_keywords_test_data()
        sentiment_sql = """
            INSERT INTO sentiment_rule_keywords 
            (id, primary_sentiment, secondary_sentiment, rule_type, rule_keywords, status, created_at, updated_at) 
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """
        success_count = await mysql_client.execute_batch_insert(sentiment_sql, sentiment_data)
        logger.info(f"二级情感规则关键词数据推送完成: {success_count} 条记录")
        
        # 4. 推送用车场景规则关键词数据
        logger.info("推送用车场景规则关键词数据...")
        scenario_data = get_scenario_rule_keywords_test_data()
        scenario_sql = """
            INSERT INTO scenario_rule_keywords 
            (id, primary_scenario, secondary_scenario, rule_keywords, status, created_at, updated_at) 
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """
        success_count = await mysql_client.execute_batch_insert(scenario_sql, scenario_data)
        logger.info(f"用车场景规则关键词数据推送完成: {success_count} 条记录")
        
        # 5. 推送否定词
        logger.info("推送否定词数据...")
        negation_rows = get_negation_words_from_excel()
        if negation_rows:
            negation_sql = """
                INSERT INTO negation_words 
                (id, word, status, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s)
            """
            success_count = await mysql_client.execute_batch_insert(negation_sql, negation_rows)
            logger.info(f"否定词数据推送完成: {success_count} 条记录")
        else:
            logger.info("未发现否定词数据或文件缺失，跳过推送")

        # 6. 推送错误记录数据
        logger.info("推送错误记录数据...")
        error_data = get_error_records_test_data()
        error_sql = """
            INSERT INTO error_records 
            (id, topic_id, topic_text, retry_count, error_type, error_msg, last_ts, status, created_time, updated_time) 
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        success_count = await mysql_client.execute_batch_insert(error_sql, error_data)
        logger.info(f"错误记录数据推送完成: {success_count} 条记录")
        
        # 6. 验证数据
        logger.info("验证推送的数据...")
        await verify_test_data(mysql_client)
        
        await mysql_client.close()
        logger.info("测试数据推送完成!")
        return True
        
    except Exception as e:
        logger.error(f"推送测试数据失败: {e}")
        return False


async def verify_test_data(mysql_client: MySQLClient):
    """验证推送的测试数据"""
    tables = [
        ("opinion_synonym", "观点近义词库"),
        ("new_words", "新词库"),
        ("sentiment_rule_keywords", "二级情感规则关键词表"),
        ("scenario_rule_keywords", "用车场景规则关键词表"),
        ("negation_words", "否定词表"),
        ("error_records", "错误记录表")
    ]
    
    logger.info("=" * 60)
    logger.info("数据验证结果 (VOC2.0架构 - MySQL):")
    logger.info("=" * 60)
    
    total_records = 0
    
    for table_name, display_name in tables:
        try:
            count_sql = f"SELECT COUNT(*) as count FROM {table_name}"
            result = await mysql_client.execute_query(count_sql)
            count = result[0]['count'] if result else 0
            total_records += count
            logger.info(f"{display_name:20} ({table_name:30}): {count:3d} 条记录")
            
            # 如果有数据，显示一条示例
            if count > 0:
                sample_sql = f"SELECT * FROM {table_name} LIMIT 1"
                sample_result = await mysql_client.execute_query(sample_sql)
                if sample_result:
                    sample = sample_result[0]
                    logger.info(f"  示例: {json.dumps(sample, ensure_ascii=False, default=str)}")
                
        except Exception as e:
            logger.warning(f"{display_name:20} ({table_name:30}): 验证失败 - {e}")
    
    logger.info("=" * 60)
    logger.info(f"总计: {total_records} 条记录")
    logger.info("=" * 60)


async def search_test():
    """测试搜索功能"""
    try:
        # 获取MySQL配置
        mysql_config = get_mysql_config()
        if not mysql_config:
            logger.error("无法获取MySQL配置")
            return False
        
        # 创建MySQL客户端
        mysql_client = MySQLClient(mysql_config)
        
        # 连接数据库
        if not await mysql_client.connect():
            logger.error("无法连接到MySQL数据库")
            return False
        
        logger.info("开始测试搜索功能...")
        
        # 测试观点近义词搜索
        logger.info("测试观点近义词搜索...")
        search_sql = "SELECT * FROM opinion_synonym WHERE entity LIKE '%大灯%' LIMIT 5"
        results = await mysql_client.execute_query(search_sql)
        logger.info(f"搜索 '大灯' 结果: {len(results)} 条")
        for result in results:
            logger.info(f"  {result}")
        
        # 测试新词库搜索
        logger.info("测试新词库搜索...")
        search_sql = "SELECT * FROM new_words WHERE new_entity LIKE '%大灯%' LIMIT 5"
        results = await mysql_client.execute_query(search_sql)
        logger.info(f"搜索新词库 '大灯' 结果: {len(results)} 条")
        for result in results:
            logger.info(f"  {result}")
        
        # 测试情感规则搜索
        logger.info("测试情感规则搜索...")
        search_sql = "SELECT * FROM sentiment_rule_keywords WHERE primary_sentiment = '正面' LIMIT 5"
        results = await mysql_client.execute_query(search_sql)
        logger.info(f"搜索 '正面' 情感结果: {len(results)} 条")
        for result in results:
            logger.info(f"  {result}")
        
        # 测试场景规则搜索
        logger.info("测试场景规则搜索...")
        search_sql = "SELECT * FROM scenario_rule_keywords WHERE primary_scenario = '路况' LIMIT 5"
        results = await mysql_client.execute_query(search_sql)
        logger.info(f"搜索 '路况' 场景结果: {len(results)} 条")
        for result in results:
            logger.info(f"  {result}")
        
        # 测试联合索引查询
        logger.info("测试联合索引查询...")
        search_sql = """
            SELECT * FROM opinion_synonym 
            WHERE standard_opinion_id = 'std_opinion_001' 
              AND entity = '大灯' 
              AND description LIKE '好看%'
            LIMIT 5
        """
        results = await mysql_client.execute_query(search_sql)
        logger.info(f"联合索引查询结果: {len(results)} 条")
        for result in results:
            logger.info(f"  {result}")
        
        await mysql_client.close()
        logger.info("搜索功能测试完成!")
        return True
        
    except Exception as e:
        logger.error(f"搜索功能测试失败: {e}")
        return False


def print_usage():
    """打印使用说明"""
    print("""
VOC2.0 MySQL测试数据推送脚本 (符合优化架构)

使用方法:
    python push_mysql_test_data.py [command]

命令:
    push     - 推送测试数据到MySQL (默认)
    verify   - 验证现有数据
    search   - 测试搜索功能
    help     - 显示此帮助信息

算法后端和后处理服务表结构 (6个核心表):
    1. opinion_synonym           - 观点近义词库 (包含测试数据)
    2. new_words                 - 新词库 (记录无法匹配的主体/描述)
    3. sentiment_rule_keywords   - 二级情感规则关键词表 (后处理服务)
    4. scenario_rule_keywords    - 用车场景规则关键词表 (后处理服务)
    5. error_records             - 错误记录表 (超过5次重试失败的数据)
    6. negation_words            - 否定词表 (从Excel文件导入)

示例:
    python push_mysql_test_data.py           # 推送测试数据
    python push_mysql_test_data.py push      # 推送测试数据
    python push_mysql_test_data.py verify    # 验证数据
    python push_mysql_test_data.py search    # 测试搜索
    """)


async def main():
    """主函数"""
    command = sys.argv[1] if len(sys.argv) > 1 else "push"
    
    if command == "help":
        print_usage()
        return
    elif command == "push":
        success = await push_test_data()
        if not success:
            sys.exit(1)
    elif command == "verify":
        # 获取MySQL配置
        mysql_config = get_mysql_config()
        if not mysql_config:
            logger.error("无法获取MySQL配置")
            sys.exit(1)
        
        # 创建MySQL客户端
        mysql_client = MySQLClient(mysql_config)
        
        # 连接数据库
        if not await mysql_client.connect():
            logger.error("无法连接到MySQL数据库")
            sys.exit(1)
        
        await verify_test_data(mysql_client)
        await mysql_client.close()
    elif command == "search":
        success = await search_test()
        if not success:
            sys.exit(1)
    else:
        logger.error(f"未知命令: {command}")
        print_usage()
        sys.exit(1)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("用户中断操作")
    except Exception as e:
        logger.error(f"脚本执行失败: {e}")
        sys.exit(1)
