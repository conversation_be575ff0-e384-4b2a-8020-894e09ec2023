#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
品牌车系过滤规则推送脚本
将从Java代码提取的过滤规则推送到MySQL数据库
支持增量更新，避免重复插入
"""

import sys
import os
import logging
import yaml
from typing import List, Dict, Any

try:
    import pymysql
    import pymysql.cursors
except ImportError:
    print("❌ 缺少 pymysql 依赖")
    print("请运行: pip install pymysql")
    sys.exit(1)

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_config():
    """加载配置文件"""
    config_path = os.path.join(project_root, 'configs', 'config.yaml')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        logger.error(f"❌ 加载配置文件失败: {e}")
        return None

def get_mysql_config():
    """获取MySQL配置"""
    config = load_config()
    if not config or 'database' not in config or 'mysql' not in config['database']:
        logger.error("❌ 配置文件中缺少MySQL配置")
        return None
    
    mysql_config = config['database']['mysql']
    return {
        'host': mysql_config.get('host', 'localhost'),
        'port': mysql_config.get('port', 3306),
        'user': mysql_config.get('username', 'root'),
        'password': mysql_config.get('password', ''),
        'database': mysql_config.get('database', 'voc_db'),
        'charset': mysql_config.get('charset', 'utf8mb4')
    }

def connect_mysql():
    """连接MySQL"""
    mysql_config = get_mysql_config()
    if not mysql_config:
        return None
    
    try:
        connection = pymysql.connect(
            host=mysql_config['host'],
            port=mysql_config['port'],
            user=mysql_config['user'],
            password=mysql_config['password'],
            database=mysql_config['database'],
            charset=mysql_config['charset'],
            cursorclass=pymysql.cursors.DictCursor,
            autocommit=False  # 使用事务
        )
        logger.info(f"✅ 连接MySQL成功: {mysql_config['host']}:{mysql_config['port']}/{mysql_config['database']}")
        return connection
    except Exception as e:
        logger.error(f"❌ 连接MySQL失败: {e}")
        return None

def get_java_filter_rules() -> List[Dict[str, Any]]:
    """获取从Java代码提取的过滤规则"""
    rules = []
    
    # 1. 全局黑名单规则 - 从Java代码removeAmbiguousNames方法提取
    global_blacklist_rules = [
        # 基础误匹配词汇
        {'pattern': '奕炫版', 'description': 'Java规则：过滤奕炫相关误匹配', 'priority': 10},
        {'pattern': '奕炫款', 'description': 'Java规则：过滤奕炫相关误匹配', 'priority': 10},
        {'pattern': '奕炫rv', 'description': 'Java规则：过滤奕炫相关误匹配', 'priority': 10},
        {'pattern': '快递哥', 'description': 'Java规则：过滤快递相关误匹配', 'priority': 10},
        {'pattern': '旅美速腾', 'description': 'Java规则：过滤速腾误匹配', 'priority': 10},
        {'pattern': '飞鱼智行', 'description': 'Java规则：过滤智行误匹配', 'priority': 10},
        {'pattern': '缓冲卫士', 'description': 'Java规则：过滤卫士误匹配', 'priority': 10},
        {'pattern': '360智行', 'description': 'Java规则：过滤智行误匹配', 'priority': 10},
        {'pattern': 'mini洗衣机', 'description': 'Java规则：过滤mini误匹配', 'priority': 10},
        {'pattern': '智行领航版', 'description': 'Java规则：过滤智行误匹配', 'priority': 10},
        
        # 地名相关
        {'pattern': '武汉', 'description': 'Java规则：过滤地名误匹配', 'priority': 10},
        {'pattern': '好汉', 'description': 'Java规则：过滤汉字误匹配', 'priority': 10},
        {'pattern': '大汉', 'description': 'Java规则：过滤汉字误匹配', 'priority': 10},
        {'pattern': '汉奸', 'description': 'Java规则：过滤汉字误匹配', 'priority': 10},
        
        # 服装相关
        {'pattern': 'polo衫', 'description': 'Java规则：过滤服装误匹配', 'priority': 10},
        {'pattern': 'dsg', 'description': 'Java规则：过滤服装误匹配', 'priority': 10},
        
        # 宝典类
        {'pattern': '学习宝典', 'description': 'Java规则：过滤宝典类误匹配', 'priority': 10},
        {'pattern': '视频宝典', 'description': 'Java规则：过滤宝典类误匹配', 'priority': 10},
        {'pattern': '音乐宝典', 'description': 'Java规则：过滤宝典类误匹配', 'priority': 10},
        {'pattern': '组合宝典', 'description': 'Java规则：过滤宝典类误匹配', 'priority': 10},
        {'pattern': '恋爱宝典', 'description': 'Java规则：过滤宝典类误匹配', 'priority': 10},
        {'pattern': '必备宝典', 'description': 'Java规则：过滤宝典类误匹配', 'priority': 10},
        {'pattern': '图文宝典', 'description': 'Java规则：过滤宝典类误匹配', 'priority': 10},
        {'pattern': '宝典软件', 'description': 'Java规则：过滤宝典类误匹配', 'priority': 10},
        {'pattern': '驾考宝典', 'description': 'Java规则：过滤宝典类误匹配', 'priority': 10},
        {'pattern': '反腐宝典', 'description': 'Java规则：过滤宝典类误匹配', 'priority': 10},
        {'pattern': '自学宝典', 'description': 'Java规则：过滤宝典类误匹配', 'priority': 10},
        {'pattern': '葵花宝典', 'description': 'Java规则：过滤宝典类误匹配', 'priority': 10},
        {'pattern': '宝典限时', 'description': 'Java规则：过滤宝典类误匹配', 'priority': 10},
        {'pattern': '数学宝典', 'description': 'Java规则：过滤宝典类误匹配', 'priority': 10},
        {'pattern': '抢分宝典', 'description': 'Java规则：过滤宝典类误匹配', 'priority': 10},
        {'pattern': '颜值宝典', 'description': 'Java规则：过滤宝典类误匹配', 'priority': 10},
        {'pattern': '游玩宝典', 'description': 'Java规则：过滤宝典类误匹配', 'priority': 10},
        {'pattern': '育儿宝典', 'description': 'Java规则：过滤宝典类误匹配', 'priority': 10},
        {'pattern': '用车宝典', 'description': 'Java规则：过滤宝典类误匹配', 'priority': 10},
        {'pattern': '上岸宝典', 'description': 'Java规则：过滤宝典类误匹配', 'priority': 10},
        {'pattern': '避坑宝典', 'description': 'Java规则：过滤宝典类误匹配', 'priority': 10},
        
        # 其他误匹配
        {'pattern': '特顺利', 'description': 'Java规则：过滤特顺误匹配', 'priority': 10},
        {'pattern': '特顺心', 'description': 'Java规则：过滤特顺误匹配', 'priority': 10},
        {'pattern': '安全顺利', 'description': 'Java规则：过滤特顺误匹配', 'priority': 10},
        {'pattern': '自毁长城', 'description': 'Java规则：过滤长城误匹配', 'priority': 10},
        {'pattern': '万里长城', 'description': 'Java规则：过滤长城误匹配', 'priority': 10},
        {'pattern': '比亚迪大药房', 'description': 'Java规则：过滤药房误匹配', 'priority': 10},
        {'pattern': '坪山比亚迪二期', 'description': 'Java规则：过滤地产误匹配', 'priority': 10},
        {'pattern': '东风17', 'description': 'Java规则：过滤军事误匹配', 'priority': 10},
        {'pattern': '东风夜放花千树', 'description': 'Java规则：过滤诗词误匹配', 'priority': 10},
        {'pattern': '萨普神山', 'description': 'Java规则：过滤萨普误匹配', 'priority': 10},
        {'pattern': '口吐莲花', 'description': 'Java规则：过滤莲花误匹配', 'priority': 10},
        {'pattern': '御风而行', 'description': 'Java规则：过滤御风误匹配', 'priority': 10},
        {'pattern': '忠诚卫士', 'description': 'Java规则：过滤卫士误匹配', 'priority': 10},
        
        # 海豹相关
        {'pattern': '海豹突击队', 'description': 'Java规则：过滤军事误匹配', 'priority': 10},
        {'pattern': '海豹家族', 'description': 'Java规则：过滤海豹误匹配', 'priority': 10},
        {'pattern': '海豹表情', 'description': 'Java规则：过滤海豹误匹配', 'priority': 10},
        {'pattern': '海豹馆', 'description': 'Java规则：过滤海豹误匹配', 'priority': 10},
        {'pattern': '海豹小白', 'description': 'Java规则：过滤海豹误匹配', 'priority': 10},
        
        # 炮类相关
        {'pattern': '意大利炮', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        {'pattern': '地图炮', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        {'pattern': '炮轰', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        {'pattern': '第3炮', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        {'pattern': '长杆短炮', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        {'pattern': '铁炮', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        {'pattern': '过宫炮', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        {'pattern': '大炮', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        {'pattern': '礼炮', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        {'pattern': '炮口', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        {'pattern': '迫击炮', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        {'pattern': '如法炮制', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        {'pattern': '开炮', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        {'pattern': '火炮', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        {'pattern': '放炮', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        {'pattern': '炮火', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        {'pattern': '坦克炮', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        {'pattern': '马后炮', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        {'pattern': '炮击', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        {'pattern': '炮台', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        {'pattern': '炮弹', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        {'pattern': '高射炮', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        {'pattern': '空炮', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        {'pattern': '炮车', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        {'pattern': '炮兵', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        {'pattern': '炮灰', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        {'pattern': '加农炮', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        {'pattern': '火箭炮', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        {'pattern': '炮塔', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        {'pattern': '炮仗', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        {'pattern': '摔炮', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        {'pattern': '鸟枪换炮', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        {'pattern': '一炮双响', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        {'pattern': '嘴炮', 'description': 'Java规则：过滤炮类误匹配', 'priority': 10},
        
        # 金融相关 - 高优先级
        {'pattern': '亿元', 'description': 'Java规则：过滤金融数字误匹配', 'priority': 5},
        {'pattern': '万元', 'description': 'Java规则：过滤金融数字误匹配', 'priority': 5},
        {'pattern': '美元', 'description': 'Java规则：过滤金融数字误匹配', 'priority': 5},
        {'pattern': '人民币', 'description': 'Java规则：过滤金融数字误匹配', 'priority': 5},
        {'pattern': 'ETF', 'description': 'Java规则：过滤金融产品误匹配', 'priority': 5},
        {'pattern': '基金', 'description': 'Java规则：过滤金融产品误匹配', 'priority': 5},
        {'pattern': '股价', 'description': 'Java规则：过滤股票误匹配', 'priority': 5},
        {'pattern': '涨跌', 'description': 'Java规则：过滤股票误匹配', 'priority': 5},
        {'pattern': '成交额', 'description': 'Java规则：过滤股票误匹配', 'priority': 5},
        {'pattern': '市值', 'description': 'Java规则：过滤股票误匹配', 'priority': 5},
        {'pattern': '换手率', 'description': 'Java规则：过滤股票误匹配', 'priority': 5},
        {'pattern': '流通股', 'description': 'Java规则：过滤股票误匹配', 'priority': 5},
        {'pattern': '持股', 'description': 'Java规则：过滤股票误匹配', 'priority': 5},
        {'pattern': '收益率', 'description': 'Java规则：过滤金融误匹配', 'priority': 5},
    ]
    
    # 添加全局黑名单规则
    for rule in global_blacklist_rules:
        rules.append({
            'rule_type': 'global_blacklist',
            'brand_name': None,
            'series_name': None,
            'pattern': rule['pattern'],
            'required_context': None,
            'blacklist_context': None,
            'priority': rule['priority'],
            'description': rule['description']
        })
    
    # 2. 上下文规则 - 从Java代码removeOrGetAmbiguousSeries方法提取
    context_rules = [
        # 比亚迪车系
        {'brand': '比亚迪', 'series': '汉', 'pattern': '汉', 'required': '比亚迪', 'blacklist': '武汉,好汉,大汉,汉奸,汉朝,汉族', 'desc': 'Java规则：汉系列需要比亚迪上下文'},
        {'brand': '比亚迪', 'series': '秦', 'pattern': '秦', 'required': '比亚迪', 'blacklist': '秦朝,秦代,秦始皇', 'desc': 'Java规则：秦系列需要比亚迪上下文'},
        {'brand': '比亚迪', 'series': '宋', 'pattern': '宋', 'required': '比亚迪', 'blacklist': '宋朝,宋代,宋词', 'desc': 'Java规则：宋系列需要比亚迪上下文'},
        {'brand': '比亚迪', 'series': '唐', 'pattern': '唐', 'required': '比亚迪', 'blacklist': '唐朝,唐代,唐诗', 'desc': 'Java规则：唐系列需要比亚迪上下文'},
        {'brand': '比亚迪', 'series': '元', 'pattern': '元', 'required': '比亚迪', 'blacklist': '亿元,万元,美元,人民币,元朝,元代', 'desc': 'Java规则：元系列需要比亚迪上下文'},
        {'brand': '比亚迪', 'series': '海豚', 'pattern': '海豚', 'required': '比亚迪', 'blacklist': '海豹突击队,海豹家族', 'desc': 'Java规则：海豚系列需要比亚迪上下文'},
        {'brand': '比亚迪', 'series': '海豹', 'pattern': '海豹', 'required': '比亚迪', 'blacklist': '海豹突击队,海豹家族,海豹表情,海豹馆', 'desc': 'Java规则：海豹系列需要比亚迪上下文'},
        {'brand': '比亚迪', 'series': '驱逐舰', 'pattern': '驱逐舰', 'required': '比亚迪', 'blacklist': '海军,军舰,战舰', 'desc': 'Java规则：驱逐舰系列需要比亚迪上下文'},
        
        # 长城/哈弗车系
        {'brand': '长城', 'series': '炮', 'pattern': '炮', 'required': '长城', 'blacklist': '意大利炮,地图炮,马后炮,大炮,火炮,炮火,开炮,炮击', 'desc': 'Java规则：炮系列需要长城上下文'},
        {'brand': '哈弗', 'series': '赤兔', 'pattern': '赤兔', 'required': '哈弗', 'blacklist': '赤兔马,三国', 'desc': 'Java规则：赤兔系列需要哈弗上下文'},
        {'brand': '哈弗', 'series': '大狗', 'pattern': '大狗', 'required': '哈弗', 'blacklist': '小狗,狗狗,宠物狗', 'desc': 'Java规则：大狗系列需要哈弗上下文'},
        {'brand': '哈弗', 'series': '神兽', 'pattern': '神兽', 'required': '哈弗', 'blacklist': '上古神兽,神话,传说', 'desc': 'Java规则：神兽系列需要哈弗上下文'},
        {'brand': '哈弗', 'series': '酷狗', 'pattern': '酷狗', 'required': '哈弗', 'blacklist': '酷狗音乐,音乐软件', 'desc': 'Java规则：酷狗系列需要哈弗上下文'},
        
        # 福田车系
        {'brand': '福田', 'series': '风景', 'pattern': '风景', 'required': '福田', 'blacklist': '风景区,风景线,自然风景', 'desc': 'Java规则：风景系列需要福田上下文'},
        {'brand': '福田', 'series': '萨普', 'pattern': '萨普', 'required': '福田', 'blacklist': '萨普神山', 'desc': 'Java规则：萨普系列需要福田上下文'},
        {'brand': '福田', 'series': '小金刚', 'pattern': '小金刚', 'required': '福田', 'blacklist': '变形金刚,金刚狼', 'desc': 'Java规则：小金刚系列需要福田上下文'},
        
        # 湖北大运车系
        {'brand': '湖北大运', 'series': '风度', 'pattern': '风度', 'required': '湖北大运', 'blacklist': '风度翩翩,个人风度', 'desc': 'Java规则：风度系列需要湖北大运上下文'},
        {'brand': '湖北大运', 'series': '风驰', 'pattern': '风驰', 'required': '湖北大运', 'blacklist': '风驰电掣', 'desc': 'Java规则：风驰系列需要湖北大运上下文'},
        
        # 吉利车系
        {'brand': '吉利', 'series': '金刚', 'pattern': '金刚', 'required': '吉利', 'blacklist': '变形金刚,金刚狼,金刚石', 'desc': 'Java规则：金刚系列需要吉利上下文'},
        {'brand': '吉利', 'series': '远景', 'pattern': '远景', 'required': '吉利', 'blacklist': '远景规划,发展远景,美好远景', 'desc': 'Java规则：远景系列需要吉利上下文'},
        {'brand': '吉利', 'series': 'icon', 'pattern': 'icon', 'required': '吉利', 'blacklist': '图标,图像,应用图标', 'desc': 'Java规则：icon系列需要吉利上下文'},
        {'brand': '吉利', 'series': '帝豪', 'pattern': '帝豪', 'required': '吉利,车', 'blacklist': '帝王,豪华,奢华', 'desc': 'Java规则：帝豪系列需要吉利或汽车上下文'},
        
        # 江铃/福特车系
        {'brand': '江铃', 'series': '宝典', 'pattern': '宝典', 'required': '江铃,福特', 'blacklist': '驾考宝典,学习宝典,葵花宝典', 'desc': 'Java规则：宝典系列需要江铃或福特上下文'},
        {'brand': '江铃', 'series': '全顺', 'pattern': '全顺', 'required': '江铃,福特', 'blacklist': '一切顺利,全都顺利', 'desc': 'Java规则：全顺系列需要江铃或福特上下文'},
        {'brand': '江铃', 'series': '特顺', 'pattern': '特顺', 'required': '江铃,福特', 'blacklist': '特别顺利,特顺心', 'desc': 'Java规则：特顺系列需要江铃或福特上下文'},
        
        # 日产车系
        {'brand': '日产', 'series': '阳光', 'pattern': '阳光', 'required': '日产', 'blacklist': '阳光明媚,阳光灿烂,阳光照射', 'desc': 'Java规则：阳光系列需要日产上下文'},
        {'brand': '日产', 'series': '天籁', 'pattern': '天籁', 'required': '日产', 'blacklist': '天籁之音,自然天籁', 'desc': 'Java规则：天籁系列需要日产上下文'},
        {'brand': '日产', 'series': '楼兰', 'pattern': '楼兰', 'required': '日产', 'blacklist': '楼兰古国,丝绸之路', 'desc': 'Java规则：楼兰系列需要日产上下文'},
        
        # 江淮车系
        {'brand': '江淮', 'series': '擎天柱', 'pattern': '擎天柱', 'required': '江淮', 'blacklist': '变形金刚,机器人', 'desc': 'Java规则：擎天柱系列需要江淮上下文'},
        {'brand': '江淮', 'series': '悦悦', 'pattern': '悦悦', 'required': '江淮', 'blacklist': '开心,愉悦', 'desc': 'Java规则：悦悦系列需要江淮上下文'},
        
        # 东风车系
        {'brand': '东风', 'series': '莲花', 'pattern': '莲花', 'required': '东风', 'blacklist': '荷花,莲花池,口吐莲花', 'desc': 'Java规则：莲花系列需要东风上下文'},
        {'brand': '东风', 'series': '御风', 'pattern': '御风', 'required': '东风', 'blacklist': '御风而行,乘风', 'desc': 'Java规则：御风系列需要东风上下文'},
        
        # 本田车系
        {'brand': '本田', 'series': 'life', 'pattern': 'life', 'required': '本田', 'blacklist': '生活,人生,生命', 'desc': 'Java规则：life系列需要本田上下文'},
        {'brand': '本田', 'series': 'fit', 'pattern': 'fit', 'required': '本田', 'blacklist': '健身,合适,适合', 'desc': 'Java规则：fit系列需要本田上下文'},
        {'brand': '本田', 'series': 'city', 'pattern': 'city', 'required': '本田', 'blacklist': '城市,都市', 'desc': 'Java规则：city系列需要本田上下文'},
        
        # 大众车系
        {'brand': '大众', 'series': '高尔夫', 'pattern': '高尔夫', 'required': '大众', 'blacklist': '高尔夫球,高尔夫球场', 'desc': 'Java规则：高尔夫系列需要大众上下文'},
        {'brand': '大众', 'series': 'golf', 'pattern': 'golf', 'required': '大众', 'blacklist': '高尔夫球,球场', 'desc': 'Java规则：golf系列需要大众上下文'},
        {'brand': '大众', 'series': 'polo', 'pattern': 'polo', 'required': '大众', 'blacklist': 'polo衫,马球', 'desc': 'Java规则：polo系列需要大众上下文'},
        
        # 奇瑞车系
        {'brand': '奇瑞', 'series': '小蚂蚁', 'pattern': '小蚂蚁', 'required': '奇瑞', 'blacklist': '蚂蚁,昆虫', 'desc': 'Java规则：小蚂蚁系列需要奇瑞上下文'},
        {'brand': '奇瑞', 'series': '大蚂蚁', 'pattern': '大蚂蚁', 'required': '奇瑞', 'blacklist': '蚂蚁,昆虫', 'desc': 'Java规则：大蚂蚁系列需要奇瑞上下文'},
        {'brand': '奇瑞', 'series': '风云2', 'pattern': '风云2', 'required': '奇瑞', 'blacklist': '风云变幻,风云人物', 'desc': 'Java规则：风云2系列需要奇瑞上下文'},
        
        # 林肯车系
        {'brand': '林肯', 'series': '航海家', 'pattern': '航海家', 'required': '林肯', 'blacklist': '航海,探险家', 'desc': 'Java规则：航海家系列需要林肯上下文'},
        {'brand': '林肯', 'series': '冒险家', 'pattern': '冒险家', 'required': '林肯', 'blacklist': '冒险,探险', 'desc': 'Java规则：冒险家系列需要林肯上下文'},
        {'brand': '林肯', 'series': '飞行家', 'pattern': '飞行家', 'required': '林肯', 'blacklist': '飞行员,航空', 'desc': 'Java规则：飞行家系列需要林肯上下文'},
        {'brand': '林肯', 'series': '领航员', 'pattern': '领航员', 'required': '林肯', 'blacklist': '导航,领航', 'desc': 'Java规则：领航员系列需要林肯上下文'},
        
        # 魏牌/WEY车系
        {'brand': '魏牌', 'series': '摩卡', 'pattern': '摩卡', 'required': '魏牌,wey,长城', 'blacklist': '咖啡,摩卡咖啡', 'desc': 'Java规则：摩卡系列需要魏牌上下文'},
        {'brand': '魏牌', 'series': '拿铁', 'pattern': '拿铁', 'required': '魏牌,wey,长城', 'blacklist': '咖啡,拿铁咖啡', 'desc': 'Java规则：拿铁系列需要魏牌上下文'},
        {'brand': '魏牌', 'series': '玛奇朵', 'pattern': '玛奇朵', 'required': '魏牌,wey,长城', 'blacklist': '咖啡,玛奇朵咖啡', 'desc': 'Java规则：玛奇朵系列需要魏牌上下文'},
        
        # 欧拉车系
        {'brand': '欧拉', 'series': 'iq', 'pattern': 'iq', 'required': '欧拉', 'blacklist': '智商,智力', 'desc': 'Java规则：iq系列需要欧拉上下文'},
        {'brand': '欧拉', 'series': '芭蕾猫', 'pattern': '芭蕾猫', 'required': '欧拉', 'blacklist': '芭蕾舞,猫咪', 'desc': 'Java规则：芭蕾猫系列需要欧拉上下文'},
        {'brand': '欧拉', 'series': '白猫', 'pattern': '白猫', 'required': '欧拉', 'blacklist': '猫咪,小猫,宠物猫', 'desc': 'Java规则：白猫系列需要欧拉上下文'},
        {'brand': '欧拉', 'series': '好猫', 'pattern': '好猫', 'required': '欧拉', 'blacklist': '猫咪,小猫,宠物猫', 'desc': 'Java规则：好猫系列需要欧拉上下文'},
        {'brand': '欧拉', 'series': '好猫gt', 'pattern': '好猫gt', 'required': '欧拉', 'blacklist': '猫咪,小猫', 'desc': 'Java规则：好猫gt系列需要欧拉上下文'},
        {'brand': '欧拉', 'series': '黑猫', 'pattern': '黑猫', 'required': '欧拉', 'blacklist': '猫咪,小猫,宠物猫', 'desc': 'Java规则：黑猫系列需要欧拉上下文'},
        {'brand': '欧拉', 'series': '闪电猫', 'pattern': '闪电猫', 'required': '欧拉', 'blacklist': '猫咪,闪电', 'desc': 'Java规则：闪电猫系列需要欧拉上下文'},
        
        # 长安车系
        {'brand': '长安', 'series': '奔奔', 'pattern': '奔奔', 'required': '长安', 'blacklist': '奔跑,奔波', 'desc': 'Java规则：奔奔系列需要长安上下文'},
        
        # 长安欧尚车系
        {'brand': '长安欧尚', 'series': '尼欧', 'pattern': '尼欧', 'required': '长安,欧尚', 'blacklist': '尼奥,黑客帝国', 'desc': 'Java规则：尼欧系列需要长安欧尚上下文'},
        
        # 马自达车系
        {'brand': '马自达', 'series': '星骋', 'pattern': '星骋', 'required': '马自达', 'blacklist': '星空,奔驰', 'desc': 'Java规则：星骋系列需要马自达上下文'},
        
        # 福特车系
        {'brand': '福特', 'series': '金牛座', 'pattern': '金牛座', 'required': '福特', 'blacklist': '星座,占星', 'desc': 'Java规则：金牛座系列需要福特上下文'},
        {'brand': '福特', 'series': '探险者', 'pattern': '探险者', 'required': '福特', 'blacklist': '探险,冒险', 'desc': 'Java规则：探险者系列需要福特上下文'},
        
        # 丰田车系
        {'brand': '丰田', 'series': '亚洲狮', 'pattern': '亚洲狮', 'required': '丰田', 'blacklist': '狮子,动物', 'desc': 'Java规则：亚洲狮系列需要丰田上下文'},
        
        # 奥迪车系
        {'brand': '奥迪', 'series': 'Q5', 'pattern': 'Q5', 'required': '奥迪', 'blacklist': 'QQ,Q版', 'desc': 'Java规则：Q5系列需要奥迪上下文'},
        
        # 雪佛兰车系
        {'brand': '雪佛兰', 'series': '开拓者', 'pattern': '开拓者', 'required': '雪佛兰', 'blacklist': '开拓,拓荒', 'desc': 'Java规则：开拓者系列需要雪佛兰上下文'},
        
        # 别克车系
        {'brand': '别克', 'series': 'gl6', 'pattern': 'gl6', 'required': '别克', 'blacklist': 'GL,游戏', 'desc': 'Java规则：gl6系列需要别克上下文'},
        {'brand': '别克', 'series': 'gl8', 'pattern': 'gl8', 'required': '别克', 'blacklist': 'GL,游戏', 'desc': 'Java规则：gl8系列需要别克上下文'},
        
        # 荣威车系
        {'brand': '荣威', 'series': '鲸', 'pattern': '鲸', 'required': '荣威', 'blacklist': '鲸鱼,海洋动物', 'desc': 'Java规则：鲸系列需要荣威上下文'},
        
        # 传祺车系
        {'brand': '传祺', 'series': 'ga3', 'pattern': 'ga3', 'required': '传祺', 'blacklist': 'GA,游戏', 'desc': 'Java规则：ga3系列需要传祺上下文'},
        {'brand': '传祺', 'series': 'ga4', 'pattern': 'ga4', 'required': '传祺', 'blacklist': 'GA,游戏', 'desc': 'Java规则：ga4系列需要传祺上下文'},
        {'brand': '传祺', 'series': 'ga6', 'pattern': 'ga6', 'required': '传祺', 'blacklist': 'GA,游戏', 'desc': 'Java规则：ga6系列需要传祺上下文'},
        {'brand': '传祺', 'series': 'ga8', 'pattern': 'ga8', 'required': '传祺', 'blacklist': 'GA,游戏', 'desc': 'Java规则：ga8系列需要传祺上下文'},
        {'brand': '传祺', 'series': 'gs3', 'pattern': 'gs3', 'required': '传祺', 'blacklist': 'GS,游戏', 'desc': 'Java规则：gs3系列需要传祺上下文'},
        {'brand': '传祺', 'series': 'gs4', 'pattern': 'gs4', 'required': '传祺', 'blacklist': 'GS,游戏', 'desc': 'Java规则：gs4系列需要传祺上下文'},
        {'brand': '传祺', 'series': 'gs5', 'pattern': 'gs5', 'required': '传祺', 'blacklist': 'GS,游戏', 'desc': 'Java规则：gs5系列需要传祺上下文'},
        {'brand': '传祺', 'series': 'gs7', 'pattern': 'gs7', 'required': '传祺', 'blacklist': 'GS,游戏', 'desc': 'Java规则：gs7系列需要传祺上下文'},
        {'brand': '传祺', 'series': 'gs8', 'pattern': 'gs8', 'required': '传祺', 'blacklist': 'GS,游戏', 'desc': 'Java规则：gs8系列需要传祺上下文'},
        
        # 雪铁龙车系
        {'brand': '雪铁龙', 'series': '世嘉', 'pattern': '世嘉', 'required': '雪铁龙', 'blacklist': '世嘉公司,游戏', 'desc': 'Java规则：世嘉系列需要雪铁龙上下文'},
        
        # 北京汽车车系
        {'brand': '北京汽车', 'series': '魔方', 'pattern': '魔方', 'required': '北京汽车,beijing汽车', 'blacklist': '魔方游戏,魔方玩具', 'desc': 'Java规则：魔方系列需要北京汽车上下文'},
        
        # 中兴车系
        {'brand': '中兴', 'series': '小老虎', 'pattern': '小老虎', 'required': '中兴', 'blacklist': '老虎,动物', 'desc': 'Java规则：小老虎系列需要中兴上下文'},
        {'brand': '中兴', 'series': '领主', 'pattern': '领主', 'required': '中兴', 'blacklist': '领导,主人', 'desc': 'Java规则：领主系列需要中兴上下文'},
        
        # 五菱/江淮/湖北大运车系
        {'brand': '五菱', 'series': '征途', 'pattern': '征途', 'required': '五菱,江淮,湖北大运', 'blacklist': '征途游戏,人生征途', 'desc': 'Java规则：征途系列需要五菱等上下文'},
        
        # 凯马车系
        {'brand': '凯马', 'series': 'k8', 'pattern': 'k8', 'required': '凯马', 'blacklist': 'K歌,游戏', 'desc': 'Java规则：k8系列需要凯马上下文'},
        {'brand': '凯马', 'series': 'k6', 'pattern': 'k6', 'required': '凯马', 'blacklist': 'K歌,游戏', 'desc': 'Java规则：k6系列需要凯马上下文'},
        {'brand': '凯马', 'series': 'k3', 'pattern': 'k3', 'required': '凯马', 'blacklist': 'K歌,游戏', 'desc': 'Java规则：k3系列需要凯马上下文'},
        {'brand': '凯马', 'series': 'k23', 'pattern': 'k23', 'required': '凯马', 'blacklist': 'K歌,游戏', 'desc': 'Java规则：k23系列需要凯马上下文'},
        {'brand': '凯马', 'series': 'hk8', 'pattern': 'hk8', 'required': '凯马', 'blacklist': 'HK,香港', 'desc': 'Java规则：hk8系列需要凯马上下文'},
        {'brand': '凯马', 'series': 'hk6', 'pattern': 'hk6', 'required': '凯马', 'blacklist': 'HK,香港', 'desc': 'Java规则：hk6系列需要凯马上下文'},
        {'brand': '凯马', 'series': 'hk3', 'pattern': 'hk3', 'required': '凯马', 'blacklist': 'HK,香港', 'desc': 'Java规则：hk3系列需要凯马上下文'},
        {'brand': '凯马', 'series': 'gk8', 'pattern': 'gk8', 'required': '凯马', 'blacklist': 'GK,游戏', 'desc': 'Java规则：gk8系列需要凯马上下文'},
        {'brand': '凯马', 'series': 'gk6', 'pattern': 'gk6', 'required': '凯马', 'blacklist': 'GK,游戏', 'desc': 'Java规则：gk6系列需要凯马上下文'},
        {'brand': '凯马', 'series': 'gk3', 'pattern': 'gk3', 'required': '凯马', 'blacklist': 'GK,游戏', 'desc': 'Java规则：gk3系列需要凯马上下文'},
        
        # 华泰品牌
        {'brand': '华泰', 'series': '', 'pattern': '华泰', 'required': '汽车,车', 'blacklist': 'ETF,基金,柏瑞,证券,保险', 'desc': 'Java规则：华泰需要汽车相关上下文'},
    ]
    
    # 添加上下文规则
    for rule in context_rules:
        rules.append({
            'rule_type': 'context_rule',
            'brand_name': rule['brand'],
            'series_name': rule['series'] if rule['series'] else None,
            'pattern': rule['pattern'],
            'required_context': rule['required'],
            'blacklist_context': rule['blacklist'],
            'priority': 20,
            'description': rule['desc']
        })
    
    # 3. 歧义模式规则
    ambiguous_rules = [
        {'brand': '阿维塔', 'series': '07', 'pattern': '07', 'blacklist': '7.02%,2007年,07月,07日', 'desc': 'Java规则：数字07容易与日期百分比误匹配'},
        # 可以添加更多歧义模式
    ]
    
    # 添加歧义模式规则
    for rule in ambiguous_rules:
        rules.append({
            'rule_type': 'ambiguous_pattern',
            'brand_name': rule['brand'],
            'series_name': rule['series'],
            'pattern': rule['pattern'],
            'required_context': None,
            'blacklist_context': rule['blacklist'],
            'priority': 30,
            'description': rule['desc']
        })
    
    return rules

def check_table_exists(connection):
    """检查表是否存在"""
    try:
        with connection.cursor() as cursor:
            cursor.execute("SHOW TABLES LIKE 'brand_series_filter_rules'")
            result = cursor.fetchone()
            return result is not None
    except Exception as e:
        logger.error(f"检查表存在性失败: {e}")
        return False

def get_existing_rules(connection) -> List[str]:
    """获取已存在的规则模式"""
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT pattern FROM brand_series_filter_rules")
            results = cursor.fetchall()
            return [row['pattern'] for row in results]
    except Exception as e:
        logger.warning(f"获取已存在规则失败: {e}")
        return []

def insert_rules_batch(connection, rules: List[Dict[str, Any]], batch_size: int = 50):
    """批量插入规则"""
    success_count = 0
    failed_count = 0
    
    # 获取已存在的规则模式
    existing_patterns = set(get_existing_rules(connection))
    logger.info(f"数据库中已存在 {len(existing_patterns)} 条规则")
    
    # 过滤出新规则
    new_rules = []
    for rule in rules:
        if rule['pattern'] not in existing_patterns:
            new_rules.append(rule)
        else:
            logger.debug(f"跳过已存在规则: {rule['pattern']}")
    
    if not new_rules:
        logger.info("没有新规则需要插入")
        return 0, 0
    
    logger.info(f"准备插入 {len(new_rules)} 条新规则")
    
    try:
        with connection.cursor() as cursor:
            # 批量插入
            for i in range(0, len(new_rules), batch_size):
                batch = new_rules[i:i + batch_size]
                
                # 构建批量插入SQL
                sql = """
                INSERT INTO brand_series_filter_rules 
                (rule_type, brand_name, series_name, pattern, required_context, blacklist_context, priority, description, status)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                batch_data = []
                for rule in batch:
                    batch_data.append((
                        rule['rule_type'],
                        rule['brand_name'],
                        rule['series_name'],
                        rule['pattern'],
                        rule['required_context'],
                        rule['blacklist_context'],
                        rule['priority'],
                        rule['description'],
                        1  # status: 启用
                    ))
                
                try:
                    cursor.executemany(sql, batch_data)
                    connection.commit()
                    success_count += len(batch)
                    logger.info(f"成功插入批次 {i//batch_size + 1}: {len(batch)} 条规则")
                except Exception as e:
                    connection.rollback()
                    failed_count += len(batch)
                    logger.error(f"批次 {i//batch_size + 1} 插入失败: {e}")
                    
                    # 尝试单条插入
                    for rule in batch:
                        try:
                            cursor.execute(sql, (
                                rule['rule_type'],
                                rule['brand_name'],
                                rule['series_name'],
                                rule['pattern'],
                                rule['required_context'],
                                rule['blacklist_context'],
                                rule['priority'],
                                rule['description'],
                                1
                            ))
                            connection.commit()
                            success_count += 1
                            failed_count -= 1
                        except Exception as single_error:
                            logger.error(f"单条插入失败 {rule['pattern']}: {single_error}")
    
    except Exception as e:
        logger.error(f"批量插入过程异常: {e}")
        connection.rollback()
    
    return success_count, failed_count

def show_statistics(connection):
    """显示统计信息"""
    try:
        with connection.cursor() as cursor:
            # 按规则类型统计
            cursor.execute("""
                SELECT rule_type, COUNT(*) as count 
                FROM brand_series_filter_rules 
                WHERE status = 1 
                GROUP BY rule_type 
                ORDER BY rule_type
            """)
            type_stats = cursor.fetchall()
            
            # 总数统计
            cursor.execute("SELECT COUNT(*) as total FROM brand_series_filter_rules WHERE status = 1")
            total_result = cursor.fetchone()
            total_count = total_result['total'] if total_result else 0
            
            logger.info(f"\n📊 过滤规则统计:")
            logger.info(f"📋 总规则数: {total_count}")
            for stat in type_stats:
                rule_type = stat['rule_type']
                count = stat['count']
                type_desc = {
                    'global_blacklist': '全局黑名单',
                    'context_rule': '上下文规则',
                    'ambiguous_pattern': '歧义模式'
                }.get(rule_type, rule_type)
                logger.info(f"  🔸 {type_desc}: {count} 条")
            
            # 优先级分布
            cursor.execute("""
                SELECT priority, COUNT(*) as count 
                FROM brand_series_filter_rules 
                WHERE status = 1 
                GROUP BY priority 
                ORDER BY priority
            """)
            priority_stats = cursor.fetchall()
            
            logger.info(f"\n⚡ 优先级分布:")
            for stat in priority_stats:
                priority = stat['priority']
                count = stat['count']
                priority_desc = {
                    5: '高优先级(金融)',
                    10: '中优先级(基础)',
                    20: '低优先级(上下文)',
                    30: '最低优先级(歧义)'
                }.get(priority, f'优先级{priority}')
                logger.info(f"  📌 {priority_desc}: {count} 条")
                
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")

def main():
    """主函数"""
    logger.info("🚀 开始推送品牌车系过滤规则到MySQL数据库")
    logger.info("💡 基于Java代码提取的规则，支持增量更新")
    
    # 连接数据库
    connection = connect_mysql()
    if not connection:
        return False
    
    try:
        # 检查表是否存在
        if not check_table_exists(connection):
            logger.error("❌ 表 brand_series_filter_rules 不存在")
            logger.info("💡 请先运行 create_mysql_optimized.py 创建表")
            return False
        
        logger.info("✅ 表 brand_series_filter_rules 存在")
        
        # 获取Java规则
        java_rules = get_java_filter_rules()
        logger.info(f"📋 从Java代码提取 {len(java_rules)} 条过滤规则")
        
        # 批量插入规则
        success_count, failed_count = insert_rules_batch(connection, java_rules)
        
        # 显示统计信息
        show_statistics(connection)
        
        # 总结
        logger.info("\n" + "="*60)
        logger.info("🎯 品牌车系过滤规则推送完成")
        logger.info("="*60)
        logger.info(f"✅ 成功插入: {success_count} 条")
        logger.info(f"❌ 插入失败: {failed_count} 条")
        logger.info(f"📊 规则来源: Java代码 removeAmbiguousNames + removeOrGetAmbiguousSeries")
        logger.info(f"🔍 规则类型: 全局黑名单 + 上下文规则 + 歧义模式")
        logger.info(f"⚡ 优化策略: 增量更新，避免重复插入")
        
        if success_count > 0:
            logger.info("✅ 过滤规则推送成功!")
            logger.info("🎯 现在可以通过text-processor服务使用智能过滤功能")
            logger.info("🔄 规则支持动态重载，无需重启服务")
            return True
        else:
            logger.warning("⚠️ 没有新规则被插入")
            return True
        
    except Exception as e:
        logger.error(f"❌ 推送过程异常: {e}")
        return False
    finally:
        connection.close()
        logger.info("🔌 数据库连接已关闭")

if __name__ == "__main__":
    try:
        success = main()
        exit_code = 0 if success else 1
        sys.exit(exit_code)
    except Exception as e:
        logger.error(f"脚本执行失败: {e}")
        sys.exit(1)
