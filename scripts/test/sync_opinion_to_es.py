#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
观点近义词库同步到ES脚本
从MySQL的voc_model.opinion_synonym表读取数据，同步到Elasticsearch的voc_entity_synonym和voc_description_synonym索引
"""

import asyncio
import json
import logging
import sys
import os
from typing import List, Dict, Any, Optional
from datetime import datetime
import pymysql
import pymysql.cursors
from elasticsearch import Elasticsearch, helpers
import httpx
import random

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

import yaml

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def get_vector_embedding(text: str, openai_base_url: str, openai_model: str, dimension: int = 512) -> List[float]:
    """调用 vLLM OpenAI Embeddings 接口获取单个文本的向量"""
    try:
        async with httpx.AsyncClient() as client:
            url = f"{openai_base_url.rstrip('/')}/embeddings"
            logger.debug(f"正在获取文本 '{text}' 的向量 (model={openai_model})...")
            response = await client.post(
                url,
                json={"model": openai_model, "input": text},
                timeout=30.0
            )
            response.raise_for_status()
            result = response.json()
            if isinstance(result, dict) and "data" in result and result["data"]:
                embedding = result["data"][0].get("embedding")
                if isinstance(embedding, list) and embedding:
                    logger.debug(f"成功获取向量，维度: {len(embedding)}")
                    return embedding
            logger.warning(f"Embeddings API 非预期响应结构: {str(result)[:200]}...")
    except httpx.ConnectError as e:
        logger.error(f"向量API连接失败 ('{text}'): {e}")
        logger.warning("将使用随机向量作为备用方案")
    except httpx.HTTPStatusError as e:
        logger.error(f"向量API返回错误状态 {e.response.status_code} ('{text}'): {e}")
        logger.warning("将使用随机向量作为备用方案")
    except Exception as e:
        logger.error(f"获取向量时发生未知错误 ('{text}'): {e}")
        logger.warning("将使用随机向量作为备用方案")
    
    # 返回随机向量作为备用
    vector = [random.uniform(-1.0, 1.0) for _ in range(dimension)]
    logger.debug(f"使用随机向量 (维度: {dimension}) 作为 '{text}' 的备用方案")
    return vector


class VectorEmbeddingService:
    """向量嵌入服务，支持多服务器负载均衡和并发处理"""
    
    def __init__(self, base_host: str = None, start_port: int = None, num_services: int = 6, model: str = None, dimension: int = None):
        self.service_urls = []
        
        # 从配置文件中获取配置信息
        config = load_config()
        vector_config = config.get('vector', {}) if config else {}
        
        # 如果没有提供base_host，从配置文件中获取
        if base_host is None:
            if 'openai_base_url' in vector_config:
                openai_base_url = vector_config['openai_base_url']
                # 从 openai_base_url 解析主机和端口
                import re
                match = re.match(r'http://([^:]+):(\d+)', openai_base_url)
                if match:
                    base_host = match.group(1)
                    config_port = int(match.group(2))
                    # 如果没有提供start_port，使用配置中的端口
                    if start_port is None:
                        start_port = config_port
                    logger.info(f"🔍 从配置文件获取base_host: {base_host}")
                    if start_port == config_port:
                        logger.info(f"🔍 从配置文件获取start_port: {start_port}")
                else:
                    base_host = "************"
                    logger.warning(f"⚠️  无法解析配置中的openai_base_url，使用默认base_host: {base_host}")
            else:
                base_host = "************"
                logger.warning(f"⚠️  配置文件中未找到vector.openai_base_url，使用默认base_host: {base_host}")
        
        # 如果仍然没有start_port，使用默认值
        if start_port is None:
            start_port = 8002
            logger.info(f"🔍 使用默认start_port: {start_port}")
        
        # 如果没有提供model，从配置文件中获取
        if model is None:
            self.model = vector_config.get('openai_model', 'bge-small-zh-v1')
            logger.info(f"🔍 从配置文件获取model: {self.model}")
        else:
            self.model = model
        
        # 如果没有提供dimension，从配置文件中获取
        if dimension is None:
            self.dimension = vector_config.get('dimension', 512)
            logger.info(f"🔍 从配置文件获取dimension: {self.dimension}")
        else:
            self.dimension = dimension
        
        # 初始化服务URL列表
        for i in range(num_services):
            port = start_port + i
            url = f"http://{base_host}:{port}/v1"
            self.service_urls.append(url)
        
        self.current_service_index = 0
        logger.info(f"🔗 初始化向量嵌入服务: {len(self.service_urls)} 个服务")
        logger.info(f"🔍 服务地址: {self.service_urls}")
    
    def get_next_service_url(self) -> str:
        """获取下一个服务URL（轮询负载均衡）"""
        url = self.service_urls[self.current_service_index]
        self.current_service_index = (self.current_service_index + 1) % len(self.service_urls)
        return url
    
    async def get_single_embedding(self, text: str, service_url: str = None) -> List[float]:
        """获取单个文本的嵌入向量"""
        if service_url is None:
            service_url = self.get_next_service_url()
        
        try:
            async with httpx.AsyncClient() as client:
                url = f"{service_url.rstrip('/')}/embeddings"
                response = await client.post(
                    url,
                    json={"model": self.model, "input": text},
                    timeout=30.0
                )
                response.raise_for_status()
                result = response.json()
                
                if isinstance(result, dict) and "data" in result and result["data"]:
                    embedding = result["data"][0].get("embedding")
                    if isinstance(embedding, list) and embedding:
                        return embedding
                
                logger.warning(f"API响应格式错误: {str(result)[:100]}...")
                
        except Exception as e:
            logger.warning(f"服务 {service_url} 失败: {e}")
        
        # 返回随机向量作为备用
        return [random.uniform(-1.0, 1.0) for _ in range(self.dimension)]
    
    async def get_batch_embeddings_concurrent(self, texts: List[str], max_concurrent: int = 6) -> List[List[float]]:
        """并发获取多个文本的嵌入向量"""
        if not texts:
            return []
        
        logger.info(f"🚀 并发获取 {len(texts)} 个文本的向量（最大并发数: {max_concurrent}）")
        
        # 使用 asyncio.Semaphore 控制并发数
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def get_embedding_with_semaphore(text: str, index: int) -> tuple:
            async with semaphore:
                service_url = self.service_urls[index % len(self.service_urls)]  # 轮询分配
                embedding = await self.get_single_embedding(text, service_url)
                logger.debug(f"✅ 文本 {index}: '{text[:20]}...' -> 向量维度: {len(embedding)}")
                return index, embedding
        
        # 创建并发任务
        tasks = []
        for i, text in enumerate(texts):
            task = get_embedding_with_semaphore(text, i)
            tasks.append(task)
        
        # 执行并发任务
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 整理结果
        embeddings = [None] * len(texts)
        success_count = 0
        
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"任务执行失败: {result}")
                continue
            
            try:
                index, embedding = result
                embeddings[index] = embedding
                success_count += 1
            except Exception as e:
                logger.error(f"结果解析失败: {e}")
        
        # 填充失败的项目
        for i, embedding in enumerate(embeddings):
            if embedding is None:
                embeddings[i] = [random.uniform(-1.0, 1.0) for _ in range(self.dimension)]
                logger.warning(f"使用随机向量作为文本 {i} 的备用")
        
        logger.info(f"✅ 并发向量生成完成: {success_count}/{len(texts)} 成功")
        return embeddings


async def get_batch_vector_embeddings(texts: List[str], openai_base_url: str = None, openai_model: str = None, dimension: int = None) -> List[List[float]]:
    """兼容性函数，使用新的并发向量服务，所有参数从配置文件获取"""
    # 使用新的并发服务，所有配置都从配置文件自动获取
    vector_service = VectorEmbeddingService(
        base_host=None,      # 从配置文件获取
        start_port=None,     # 从配置文件获取
        num_services=1,      # 唯一不从配置文件获取的参数
        model=None,          # 从配置文件获取
        dimension=None       # 从配置文件获取
    )
    
    return await vector_service.get_batch_embeddings_concurrent(texts)


def load_config():
    """加载配置文件"""
    config_path = os.path.join(project_root, 'configs', 'config.yaml')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        logger.error(f"❌ 加载配置文件失败: {e}")
        return None


def get_mysql_config():
    """获取MySQL配置"""
    config = load_config()
    if not config or 'database' not in config or 'mysql' not in config['database']:
        logger.error("❌ 配置文件中缺少MySQL配置")
        return None
    
    mysql_config = config['database']['mysql']
    return {
        'host': mysql_config.get('host', 'localhost'),
        'port': mysql_config.get('port', 3306),
        'user': mysql_config.get('username', 'root'),
        'password': mysql_config.get('password', ''),
        'database': 'voc_model',  # 固定使用voc_model数据库
        'charset': mysql_config.get('charset', 'utf8mb4')
    }


def get_elasticsearch_config():
    """获取Elasticsearch配置"""
    config = load_config()
    if not config or 'elasticsearch' not in config:
        logger.error("❌ 配置文件中缺少Elasticsearch配置")
        return None
    
    es_config = config['elasticsearch']
    return {
        'hosts': es_config.get('hosts', ['localhost:9200']),
        'username': es_config.get('username'),
        'password': es_config.get('password'),
        'verify_certs': es_config.get('verify_certs', False),
        'ca_certs': es_config.get('ca_certs'),
        'timeout': es_config.get('timeout', 30)
    }


class MySQLClient:
    """MySQL数据库客户端"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.connection = None
    
    async def connect(self):
        """连接MySQL数据库"""
        try:
            self.connection = pymysql.connect(
                host=self.config['host'],
                port=self.config['port'],
                user=self.config['user'],
                password=self.config['password'],
                database=self.config['database'],
                charset=self.config['charset'],
                cursorclass=pymysql.cursors.DictCursor,
                autocommit=True
            )
            logger.info(f"✅ MySQL连接成功: {self.config['host']}:{self.config['port']}/{self.config['database']}")
            return True
        except Exception as e:
            logger.error(f"❌ MySQL连接失败: {e}")
            return False
    
    async def execute_query(self, sql: str, params: tuple = None) -> List[Dict]:
        """执行查询SQL"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(sql, params)
                return cursor.fetchall()
        except Exception as e:
            logger.error(f"查询执行失败: {sql}, 错误: {e}")
            return []
    
    async def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            logger.info("🔌 MySQL连接已关闭")


class ElasticsearchClient:
    """Elasticsearch客户端"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.client = None
    
    async def connect(self):
        """连接Elasticsearch"""
        try:
            # 处理hosts格式，确保包含协议前缀
            hosts = []
            for host in self.config['hosts']:
                if not host.startswith('http://') and not host.startswith('https://'):
                    # 默认使用http协议
                    host = f"http://{host}"
                hosts.append(host)
            
            # 构建连接参数
            connect_params = {
                'hosts': hosts,
                'request_timeout': self.config.get('timeout', 30),  # 使用新的参数名
                'verify_certs': self.config.get('verify_certs', False)
            }
            
            # 添加认证信息（如果有）
            if self.config.get('username') and self.config.get('password'):
                connect_params['http_auth'] = (self.config['username'], self.config['password'])
            
            # 添加CA证书（如果有）
            if self.config.get('ca_certs'):
                connect_params['ca_certs'] = self.config['ca_certs']
            
            self.client = Elasticsearch(**connect_params)
            
            # 测试连接
            if self.client.ping():
                logger.info(f"✅ Elasticsearch连接成功: {self.config['hosts']}")
                return True
            else:
                logger.error("❌ Elasticsearch连接失败: ping测试失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ Elasticsearch连接失败: {e}")
            return False
    
    async def bulk_index(self, index_name: str, documents: List[Dict], max_retries: int = 3, progress_interval: int = 10000) -> bool:
        """批量索引文档（带重试机制和进度日志）"""
        if not documents:
            logger.warning(f"没有文档需要索引到 {index_name}")
            return True
        
        total_docs = len(documents)
        logger.info(f"🚀 开始批量索引: {index_name}, 总文档数: {total_docs:,} 条")
        
        # 准备批量操作
        actions = []
        for doc in documents:
            action = {
                "_index": index_name,
                "_source": doc
            }
            # 如果文档有ID，使用它
            if 'id' in doc:
                action["_id"] = doc['id']
            actions.append(action)
        
        # 分批处理，每批最多10000条，以便记录进度
        batch_size = min(progress_interval, 5000)  # 实际批次大小，不超过5000以保证性能
        total_success = 0
        total_failed = 0
        
        for batch_start in range(0, len(actions), batch_size):
            batch_end = min(batch_start + batch_size, len(actions))
            batch_actions = actions[batch_start:batch_end]
            current_batch_size = len(batch_actions)
            
            # 重试机制
            batch_success = False
            for attempt in range(max_retries):
                try:
                    # 执行批量索引
                    success_count, failed_items = helpers.bulk(
                        self.client,
                        batch_actions,
                        chunk_size=500,  # 减小批次大小提高成功率
                        request_timeout=120,  # 增加超时时间
                        max_retries=2,
                        initial_backoff=2,
                        max_backoff=600
                    )
                    
                    total_success += success_count
                    batch_failed = len(failed_items) if failed_items else 0
                    total_failed += batch_failed
                    
                    # 记录批次结果
                    logger.debug(f"📊 批次 [{batch_start+1:,}-{batch_end:,}]: 成功 {success_count} 条, 失败 {batch_failed} 条")
                    
                    # 每处理完progress_interval条记录，输出进度日志
                    if (batch_end) % progress_interval == 0 or batch_end == total_docs:
                        progress_percent = (batch_end / total_docs) * 100
                        logger.info(f"📈 索引进度: {batch_end:,}/{total_docs:,} ({progress_percent:.1f}%), 累计成功: {total_success:,} 条, 累计失败: {total_failed:,} 条")
                    
                    if failed_items:
                        logger.warning(f"⚠️  批次部分文档索引失败: {len(failed_items)} 条")
                        if len(failed_items) <= 3:  # 只显示前3个失败项
                            for item in failed_items:
                                logger.warning(f"失败项: {item}")
                        
                        # 如果失败项过多，返回False
                        if len(failed_items) > current_batch_size * 0.1:  # 失败率超过10%
                            logger.error(f"❌ 批次索引失败率过高: {len(failed_items)}/{current_batch_size}")
                            return False
                    
                    batch_success = True
                    break  # 批次成功，跳出重试循环
                    
                except Exception as e:
                    logger.warning(f"⚠️  批次 [{batch_start+1:,}-{batch_end:,}] 尝试 {attempt + 1}/{max_retries} 失败: {e}")
                    if attempt == max_retries - 1:  # 最后一次尝试
                        logger.error(f"❌ 批次 [{batch_start+1:,}-{batch_end:,}] 最终失败: {e}")
                        return False
                    
                    # 等待后重试
                    await asyncio.sleep(2 ** attempt)  # 指数退避
            
            if not batch_success:
                return False
        
        # 最终统计
        final_success_rate = (total_success / total_docs) * 100 if total_docs > 0 else 0
        logger.info(f"✅ 批量索引完成: {index_name}, 总成功: {total_success:,}/{total_docs:,} 条 ({final_success_rate:.1f}%), 总失败: {total_failed:,} 条")
        
        # 如果总失败率过高，返回False
        if total_failed > total_docs * 0.1:  # 总失败率超过10%
            logger.error(f"❌ 总体索引失败率过高: {total_failed:,}/{total_docs:,}")
            return False
        
        return True
    
    async def create_index_if_not_exists(self, index_name: str, mapping: Dict = None):
        """创建索引（如果不存在）"""
        try:
            if not self.client.indices.exists(index=index_name):
                body = {}
                if mapping:
                    body["mappings"] = mapping
                
                self.client.indices.create(index=index_name, body=body)
                logger.info(f"✅ 创建索引: {index_name}")
            else:
                logger.info(f"📋 索引已存在: {index_name}")
        except Exception as e:
            logger.error(f"❌ 创建索引失败: {index_name}, 错误: {e}")
            raise


class OpinionSyncService:
    """观点近义词库同步服务"""
    
    def __init__(self):
        self.mysql_client = None
        self.es_client = None
        self.vector_config = None
    
    async def initialize(self):
        """初始化客户端"""
        # 初始化MySQL客户端
        mysql_config = get_mysql_config()
        if not mysql_config:
            raise Exception("无法获取MySQL配置")
        
        self.mysql_client = MySQLClient(mysql_config)
        if not await self.mysql_client.connect():
            raise Exception("无法连接到MySQL数据库")
        
        # 初始化Elasticsearch客户端
        es_config = get_elasticsearch_config()
        if not es_config:
            raise Exception("无法获取Elasticsearch配置")
        
        self.es_client = ElasticsearchClient(es_config)
        if not await self.es_client.connect():
            raise Exception("无法连接到Elasticsearch")
        
        # 初始化向量配置
        config = load_config()
        if config and 'vector' in config:
            self.vector_config = {
                'openai_base_url': config['vector'].get('openai_base_url', 'http://************:8002/v1'),
                'openai_model': config['vector'].get('openai_model', 'bge-small-zh-v1'),
                'dimension': config['vector'].get('dimension', 512)
            }
            logger.info(f"🔍 向量配置: {self.vector_config['openai_base_url']}, 模型: {self.vector_config['openai_model']}, 维度: {self.vector_config['dimension']}")
        else:
            logger.warning("⚠️  未找到向量配置，使用默认值")
            self.vector_config = {
                'openai_base_url': 'http://************:8002/v1',
                'openai_model': 'bge-small-zh-v1',
                'dimension': 512
            }
    
    async def read_opinion_synonym_data(self) -> List[Dict]:
        """从MySQL读取观点近义词库数据"""
        logger.info("📖 开始读取MySQL观点近义词库数据...")
        
        sql = """
        SELECT 
            entity_id,
            entity,
            description_id,
            description,
            standard_opinion_id,
            opinion_type
        FROM opinion_synonym
        WHERE status = 1 AND opinion_type = 1
        ORDER BY id
        """
        
        try:
            results = await self.mysql_client.execute_query(sql)
            logger.info(f"✅ 成功读取观点近义词库数据: {len(results)} 条记录 (仅opinion_type=1)")
            return results
        except Exception as e:
            logger.error(f"❌ 读取观点近义词库数据失败: {e}")
            return []
    
    async def prepare_entity_synonym_documents(self, opinion_data: List[Dict]) -> List[Dict]:
        """准备实体近义词文档（优化版：批量并发生成向量，合并相同entity的standard_opinion_id）"""
        entity_data = {}  # key: entity, value: {'rows': [row1, row2, ...], 'opinion_ids': set()}
        
        # 第一步：按entity分组，收集所有的standard_opinion_id
        for row in opinion_data:
            entity = row.get('entity', '').strip()
            if not entity:
                continue
            
            if entity not in entity_data:
                entity_data[entity] = {
                    'rows': [],
                    'opinion_ids': set(),
                    'first_row': row  # 保存第一个行的其他信息
                }
            
            entity_data[entity]['rows'].append(row)
            entity_data[entity]['opinion_ids'].add(row['standard_opinion_id'])
        
        unique_entities = list(entity_data.keys())
        logger.info(f"📝 实体分组: {len(opinion_data)} 条记录 -> {len(unique_entities)} 个唯一实体")
        
        # 打印统计信息
        total_opinion_ids = sum(len(data['opinion_ids']) for data in entity_data.values())
        logger.info(f"📈 实体统计: 平均每个实体对应 {total_opinion_ids/len(unique_entities):.1f} 个观点ID")
        
        # 第二步：批量并发生成向量
        entity_vectors = {}
        if self.vector_config and unique_entities:
            try:
                logger.info(f"🚀 开始批量生成实体向量: {len(unique_entities)} 个")
                vectors = await get_batch_vector_embeddings(
                    unique_entities,
                    self.vector_config['openai_base_url'],
                    self.vector_config['openai_model'],
                    self.vector_config['dimension']
                )
                # 创建 entity -> vector 的映射
                for i, entity in enumerate(unique_entities):
                    if i < len(vectors):
                        entity_vectors[entity] = vectors[i]
                
                logger.info(f"✅ 实体向量生成完成: {len(entity_vectors)} 个")
            except Exception as e:
                logger.error(f"❌ 批量生成实体向量失败: {e}")
                entity_vectors = {}
        
        # 第三步：构建文档
        documents = []
        for entity, data in entity_data.items():
            first_row = data['first_row']
            opinion_id_list = sorted(list(data['opinion_ids']))  # 排序以保证一致性
            vector = entity_vectors.get(entity)
            
            doc = {
                'id': f"entity_{first_row['entity_id']}",  # 使用第一个遇到的entity_id
                'entity_id': first_row['entity_id'],
                'sim_entity': entity,
                'vector': vector,
                'standard_opinion_id_list': opinion_id_list,  # 合并所有的opinion_id
                'status': 1,  # 固定为1，因为查询已过滤status=1
                'created_time': datetime.now().isoformat(),
                'updated_time': datetime.now().isoformat()
            }
            documents.append(doc)
            
            # 记录日志
            if len(opinion_id_list) > 1:
                logger.debug(f"🔗 实体 '{entity}' 合并了 {len(opinion_id_list)} 个观点ID: {opinion_id_list}")
        
        logger.info(f"📝 实体近义词文档准备完成: {len(documents)} 条")
        return documents
    
    async def prepare_description_synonym_documents(self, opinion_data: List[Dict]) -> List[Dict]:
        """准备描述近义词文档（按sim_description+standard_opinion_id组合去重）"""
        description_map = {}  # key: (description, standard_opinion_id), value: row
        unique_descriptions = []  # 唯一描述列表（用于批量生成向量）
        description_set = set()  # 用于去重唯一描述文本
        
        # 第一步：按(description, standard_opinion_id)组合去重
        for row in opinion_data:
            description = row.get('description', '').strip()
            if not description:
                continue
            
            standard_opinion_id = row['standard_opinion_id']
            key = (description, standard_opinion_id)
            
            # 只有当description和standard_opinion_id都相同时才去重
            if key not in description_map:
                description_map[key] = row
                
                # 收集唯一描述文本用于批量生成向量
                if description not in description_set:
                    description_set.add(description)
                    unique_descriptions.append(description)
        
        logger.info(f"📝 描述去重: {len(opinion_data)} 条记录 -> {len(description_map)} 个唯一组合 (description+opinion_id)")
        logger.info(f"🔍 唯一描述文本: {len(unique_descriptions)} 个")
        
        # 第二步：批量并发生成向量（只为唯一描述文本生成）
        description_vectors = {}
        if self.vector_config and unique_descriptions:
            try:
                logger.info(f"🚀 开始批量生成描述向量: {len(unique_descriptions)} 个")
                vectors = await get_batch_vector_embeddings(
                    unique_descriptions,
                    self.vector_config['openai_base_url'],
                    self.vector_config['openai_model'],
                    self.vector_config['dimension']
                )
                # 创建 description -> vector 的映射
                for i, description in enumerate(unique_descriptions):
                    if i < len(vectors):
                        description_vectors[description] = vectors[i]
                
                logger.info(f"✅ 描述向量生成完成: {len(description_vectors)} 个")
            except Exception as e:
                logger.error(f"❌ 批量生成描述向量失败: {e}")
                description_vectors = {}
        
        # 第三步：构建文档
        documents = []
        for (description, standard_opinion_id), row in description_map.items():
            vector = description_vectors.get(description)  # 从映射中获取对应向量
            
            doc = {
                'id': f"desc_{row['description_id']}",
                'description_id': row['description_id'],
                'sim_description': description,
                'vector': vector,
                'standard_opinion_id': standard_opinion_id,
                'status': 1,  # 固定为1，因为查询已过滤status=1
                'created_time': datetime.now().isoformat(),
                'updated_time': datetime.now().isoformat()
            }
            documents.append(doc)
        
        logger.info(f"📝 描述近义词文档准备完成: {len(documents)} 条")
        return documents
    
    async def sync_to_elasticsearch(self):
        """同步数据到Elasticsearch"""
        try:
            # 1. 读取MySQL数据
            opinion_data = await self.read_opinion_synonym_data()
            if not opinion_data:
                logger.warning("⚠️  没有读取到观点近义词库数据（opinion_type=1），跳过同步")
                return False
            
            # 2. 创建ES索引（如果不存在）
            # 从配置文件获取向量维度
            config = load_config()
            vector_dims = config.get('vector', {}).get('dimension', 512) if config else 512
            
            entity_mapping = {
                "properties": {
                    "entity_id": {"type": "keyword"},
                    "sim_entity": {"type": "keyword"},
                    "vector": {
                        "type": "dense_vector",
                        "dims": vector_dims,
                        "similarity": "cosine"
                    },
                    "standard_opinion_id_list": {
                        "type": "text",
                        "index": False  # 不索引，纯存储，提高性能
                    },
                    "status": {"type": "integer"},
                    "created_time": {"type": "date"},
                    "updated_time": {"type": "date"}
                }
            }
            
            description_mapping = {
                "properties": {
                    "description_id": {"type": "keyword"},
                    "sim_description": {"type": "keyword"},
                    "vector": {
                        "type": "dense_vector",
                        "dims": vector_dims,
                        "similarity": "cosine"
                    },
                    "standard_opinion_id": {"type": "keyword"},
                    "status": {"type": "integer"},
                    "created_time": {"type": "date"},
                    "updated_time": {"type": "date"}
                }
            }
            
            await self.es_client.create_index_if_not_exists("voc_entity_synonym", entity_mapping)
            await self.es_client.create_index_if_not_exists("voc_description_synonym", description_mapping)
            
            # 3. 准备文档
            logger.info("📝 开始准备文档...")
            
            # 先统计需要生成向量的文档数量
            # 实体统计：按entity去重
            unique_entities = set()
            for row in opinion_data:
                entity = row.get('entity', '').strip()
                if entity:
                    unique_entities.add(entity)
            
            # 描述统计：按(description, standard_opinion_id)组合去重，但向量生成只需要唯一描述文本
            description_combinations = set()  # (description, standard_opinion_id)组合
            unique_description_texts = set()  # 唯一描述文本
            for row in opinion_data:
                description = row.get('description', '').strip()
                if description:
                    standard_opinion_id = row['standard_opinion_id']
                    description_combinations.add((description, standard_opinion_id))
                    unique_description_texts.add(description)
            
            unique_entities_count = len(unique_entities)
            description_combinations_count = len(description_combinations)
            unique_description_texts_count = len(unique_description_texts)
            total_embeddings_needed = unique_entities_count + unique_description_texts_count
            
            logger.info(f"📊 文档统计:")
            logger.info(f"  - 唯一主体数量: {unique_entities_count:,} 个 (将生成 {unique_entities_count:,} 个实体文档)")
            logger.info(f"  - 描述组合数量: {description_combinations_count:,} 个 (将生成 {description_combinations_count:,} 个描述文档)")
            logger.info(f"  - 唯一描述文本: {unique_description_texts_count:,} 个")
            logger.info(f"📊 向量嵌入统计:")
            logger.info(f"  - 需要嵌入的主体: {unique_entities_count:,} 个")
            logger.info(f"  - 需要嵌入的描述: {unique_description_texts_count:,} 个")
            logger.info(f"  - 总计需要嵌入: {total_embeddings_needed:,} 个")
            
            # 临时禁用httpx的INFO级别日志
            httpx_logger = logging.getLogger("httpx")
            original_level = httpx_logger.level
            httpx_logger.setLevel(logging.WARNING)
            
            try:
                logger.info("🚀 开始生成向量嵌入...")
                entity_docs = await self.prepare_entity_synonym_documents(opinion_data)
                description_docs = await self.prepare_description_synonym_documents(opinion_data)
                logger.info("✅ 向量嵌入生成完成")
            finally:
                # 恢复httpx日志级别
                httpx_logger.setLevel(original_level)
            
            # 4. 批量索引到ES
            total_entity_docs = len(entity_docs) if entity_docs else 0
            total_description_docs = len(description_docs) if description_docs else 0
            total_docs = total_entity_docs + total_description_docs
            
            logger.info(f"🚀 开始同步数据到Elasticsearch...")
            logger.info(f"📊 同步统计: 实体文档 {total_entity_docs:,} 条, 描述文档 {total_description_docs:,} 条, 总计 {total_docs:,} 条")
            
            start_time = datetime.now()
            synced_count = 0
            
            # 同步实体近义词
            if entity_docs:
                logger.info(f"🔄 开始同步实体近义词: {len(entity_docs):,} 条")
                success = await self.es_client.bulk_index("voc_entity_synonym", entity_docs)
                if not success:
                    logger.error("❌ 实体近义词同步失败")
                    return False
                synced_count += len(entity_docs)
                logger.info(f"✅ 实体近义词同步完成: {len(entity_docs):,} 条")
                logger.info(f"📈 总体进度: {synced_count:,}/{total_docs:,} ({(synced_count/total_docs)*100:.1f}%)")
            else:
                logger.info("ℹ️  没有实体近义词需要同步")
            
            # 同步描述近义词
            if description_docs:
                logger.info(f"🔄 开始同步描述近义词: {len(description_docs):,} 条")
                success = await self.es_client.bulk_index("voc_description_synonym", description_docs)
                if not success:
                    logger.error("❌ 描述近义词同步失败")
                    return False
                synced_count += len(description_docs)
                logger.info(f"✅ 描述近义词同步完成: {len(description_docs):,} 条")
                logger.info(f"📈 总体进度: {synced_count:,}/{total_docs:,} ({(synced_count/total_docs)*100:.1f}%)")
            else:
                logger.info("ℹ️  没有描述近义词需要同步")
            
            # 计算总耗时
            end_time = datetime.now()
            duration = end_time - start_time
            duration_seconds = duration.total_seconds()
            
            if synced_count > 0:
                docs_per_second = synced_count / duration_seconds if duration_seconds > 0 else 0
                logger.info(f"⏱️  同步性能统计: 总耗时 {duration_seconds:.1f} 秒, 平均速度 {docs_per_second:.1f} 条/秒")
            
            logger.info("✅ 观点近义词库同步到Elasticsearch完成!")
            return True
            
        except Exception as e:
            logger.error(f"❌ 同步过程中发生错误: {e}")
            return False
    
    async def close(self):
        """关闭客户端连接"""
        if self.mysql_client:
            await self.mysql_client.close()


async def main():
    """主函数"""
    logger.info("🚀 开始观点近义词库同步任务...")
    
    sync_service = OpinionSyncService()
    
    try:
        # 初始化服务
        await sync_service.initialize()
        
        # 执行同步
        success = await sync_service.sync_to_elasticsearch()
        
        if success:
            logger.info("🎉 观点近义词库同步任务完成!")
        else:
            logger.error("💥 观点近义词库同步任务失败!")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"💥 同步任务执行失败: {e}")
        sys.exit(1)
    finally:
        await sync_service.close()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("👋 用户中断操作")
    except Exception as e:
        logger.error(f"💥 脚本执行失败: {e}")
        sys.exit(1)
