#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Elasticsearch测试数据推送脚本 - 符合VOC2.0架构
为VOC2.0智能打标系统的ES索引推送完整的测试数据
"""

import asyncio
import json
import logging
import sys
import os
from typing import List, Dict, Any
from datetime import datetime
import httpx

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from shared.elasticsearch.es_client import ElasticsearchClient
from shared.models.schemas import ElasticsearchConfig
from shared.utils.config import get_config_manager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def get_vector_embedding(text: str, openai_base_url: str, openai_model: str, dimension: int = 512) -> List[float]:
    """调用 vLLM OpenAI Embeddings 接口获取向量"""
    try:
        async with httpx.AsyncClient() as client:
            url = f"{openai_base_url.rstrip('/')}/embeddings"
            logger.info(f"正在获取文本 '{text}' 的向量 (model={openai_model})...")
            response = await client.post(
                url,
                json={"model": openai_model, "input": text},
                timeout=30.0
            )
            response.raise_for_status()
            result = response.json()
            if isinstance(result, dict) and "data" in result and result["data"]:
                embedding = result["data"][0].get("embedding")
                if isinstance(embedding, list) and embedding:
                    logger.info(f"成功获取向量，维度: {len(embedding)}")
                    return embedding
            logger.warning(f"Embeddings API 非预期响应结构: {str(result)[:200]}...")
    except httpx.ConnectError as e:
        logger.error(f"向量API连接失败 ('{text}'): {e}")
        logger.warning("将使用随机向量作为备用方案")
    except httpx.HTTPStatusError as e:
        logger.error(f"向量API返回错误状态 {e.response.status_code} ('{text}'): {e}")
        logger.warning("将使用随机向量作为备用方案")
    except Exception as e:
        logger.error(f"获取向量时发生未知错误 ('{text}'): {e}")
        logger.warning("将使用随机向量作为备用方案")
    
    # 返回随机向量作为备用
    import random
    vector = [random.uniform(-1.0, 1.0) for _ in range(dimension)]
    logger.info(f"使用随机向量 (维度: {dimension}) 作为 '{text}' 的备用方案")
    return vector


async def get_changan_standard_opinions_test_data() -> List[Dict[str, Any]]:
    """获取长安标准观点库测试数据 - 匹配voc_standard_opinion索引结构"""
    opinions = [
        # 用户提供的真实数据
        ("opinion_001", "std_001", "车灯好看", "车灯好看", "外观设计", "外观", 1)
    ]
    
    result = []
    for opinion_id, standard_id, standard_opinion, normalized_opinion, business_dept, category, status in opinions:
        result.append({
            "normalized_opinion_id": opinion_id,
            "standard_id": standard_id,
            "standard_opinion": standard_opinion,
            "normalized_opinion": normalized_opinion,
            "business_dept": business_dept,
            "category": category,
            "status": status,
            "created_time": datetime.now().isoformat(),
            "updated_time": datetime.now().isoformat()
        })
        
    return result





async def get_entity_synonyms_test_data(openai_base_url: str, openai_model: str, dimension: int) -> List[Dict[str, Any]]:
    """获取主体近义词库测试数据 - 匹配voc_entity_synonym索引结构"""
    entities = [
        ("entity_001", "大灯", "大灯", ["std_opinion_001"], 1),
        ("entity_002", "内饰", "内饰", ["std_opinion_002"], 1),
        ("entity_003", "动力", "动力", ["std_opinion_003"], 1),
        ("entity_004", "空间", "空间", ["std_opinion_004"], 1),
        ("entity_005", "油耗", "油耗", ["std_opinion_005"], 1),
        ("entity_006", "座椅", "座椅", ["std_opinion_006"], 1),
        ("entity_007", "操控", "操控", ["std_opinion_007"], 1),
        ("entity_008", "噪音", "噪音", ["std_opinion_008"], 1),
        ("entity_009", "配置", "配置", ["std_opinion_009"], 1),
        ("entity_010", "价格", "价格", ["std_opinion_010"], 1),
    ]
    
    result = []
    for entity_id, sim_entity, normalized_entity, opinion_ids, status in entities:
        vector = await get_vector_embedding(sim_entity, openai_base_url, openai_model, dimension)
        result.append({
            "entity_id": entity_id,
            "sim_entity": sim_entity,
            "vector": vector,
            "normalized_entity": normalized_entity,
            "standard_opinion_id_list": opinion_ids,  # 这是数组，需要确保索引表支持
            "status": status,
            "created_time": datetime.now().isoformat(),
            "updated_time": datetime.now().isoformat()
        })
        
    return result


async def get_description_synonyms_test_data(openai_base_url: str, openai_model: str, dimension: int) -> List[Dict[str, Any]]:
    """获取描述近义词库测试数据 - 匹配voc_description_synonym索引结构"""
    descriptions = [
        ("desc_001", "好看", "好看", "std_opinion_001", 1),
        ("desc_002", "丑", "丑", "std_opinion_002", 1),
        ("desc_003", "强劲", "强劲", "std_opinion_003", 1),
        ("desc_004", "宽敞", "宽敞", "std_opinion_004", 1),
        ("desc_005", "省", "省", "std_opinion_005", 1),
        ("desc_006", "舒适", "舒适", "std_opinion_006", 1),
        ("desc_007", "精准", "精准", "std_opinion_007", 1),
        ("desc_008", "安静", "安静", "std_opinion_008", 1),
        ("desc_009", "丰富", "丰富", "std_opinion_009", 1),
        ("desc_010", "合理", "合理", "std_opinion_010", 1),
    ]
    
    result = []
    for desc_id, sim_desc, normalized_desc, opinion_id, status in descriptions:
        vector = await get_vector_embedding(sim_desc, openai_base_url, openai_model, dimension)
        result.append({
            "description_id": desc_id,
            "sim_description": sim_desc,
            "vector": vector,
            "normalized_description": normalized_desc,
            "standard_opinion_id": opinion_id,
            "status": status,
            "created_time": datetime.now().isoformat(),
            "updated_time": datetime.now().isoformat()
        })
        
    return result


# async def get_new_words_test_data(vector_api_url: str) -> List[Dict[str, Any]]:
#     """获取新词库测试数据 - 匹配voc_new_words索引结构"""
#     new_words = [
#         ("new_001", "新车型", "新外观", "新车型新外观", 1, "topic_001", 1)
#     ]
#     
#     result = []
#     for word_id, new_entity, new_description, combined_text, frequency, source_topic_id, review_status in new_words:
#         vector = await get_vector_embedding(combined_text, vector_api_url)
#         result.append({
#             "new_entity": new_entity,
#             "new_description": new_description,
#             "combined_text": combined_text,
#             "vector_embedding": vector,
#             "frequency": frequency,
#             "source_topic_id": source_topic_id,
#             "review_status": review_status,
#             "created_time": datetime.now().isoformat(),
#             "updated_time": datetime.now().isoformat()
#         })
#         
#     return result


def get_brand_series_test_data() -> List[Dict[str, Any]]:
    """获取品牌车系库测试数据 - 匹配voc_brand_series_model索引结构"""
    return [
        {
            "brand": "长安",
            "series": "CS75 PLUS",
            "model": "2024款 2.0T 自动豪华型",
            "brand_code": "CHANGAN",
            "series_code": "CS75PLUS",
            "model_code": "CS75PLUS_2024_2.0T",
            "synonyms": "CS75+ CS75PLUS",
            "status": 1,
            "created_time": datetime.now().isoformat(),
            "updated_time": datetime.now().isoformat()
        },
        {
            "brand": "比亚迪",
            "series": "汉",
            "model": "2024款 EV 创世版",
            "brand_code": "BYD",
            "series_code": "HAN",
            "model_code": "HAN_2024_EV",
            "synonyms": "比亚迪汉 HAN 汉EV",
            "status": 1,
            "created_time": datetime.now().isoformat(),
            "updated_time": datetime.now().isoformat()
        },
        {
            "brand": "吉利",
            "series": "星越L",
            "model": "2024款 2.0T 四驱旗舰型",
            "brand_code": "GEELY",
            "series_code": "XINGYUEL",
            "model_code": "XINGYUEL_2024_2.0T",
            "synonyms": "星越L XINGYUEL 吉利星越",
            "status": 1,
            "created_time": datetime.now().isoformat(),
            "updated_time": datetime.now().isoformat()
        },
        {
            "brand": "蔚来",
            "series": "ES6",
            "model": "2024款 75kWh 运动版",
            "brand_code": "NIO",
            "series_code": "ES6",
            "model_code": "ES6_2024_75kWh",
            "synonyms": "蔚来ES6 NIO ES6",
            "status": 1,
            "created_time": datetime.now().isoformat(),
            "updated_time": datetime.now().isoformat()
        }
    ]


async def push_test_data():
    """推送测试数据到Elasticsearch"""
    try:
        # 加载配置
        config_manager = get_config_manager()
        es_config = config_manager.get_elasticsearch_config()
        # 显式覆盖为生产环境集群与认证
        es_config.hosts = ["http://************:29200"]
        es_config.username = "elastic"
        es_config.password = "L7bzd1gmm+es"
        
        # 创建ES客户端
        es_client = ElasticsearchClient(es_config)
        await es_client.initialize()
        
        # 检查ES连接
        try:
            info = await es_client.client.info()
            logger.info(f"✅ ES连接成功，版本: {info.get('version', {}).get('number', 'unknown')}")
        except Exception as e:
            logger.warning(f"⚠️ ES连接检查异常: {e}")
            logger.info("继续执行数据推送...")
        
        logger.info("开始推送测试数据到Elasticsearch (VOC2.0架构)...")
        
        # 获取向量API配置（vLLM OpenAI Embeddings）
        vector_config = config_manager.get_vector_config()
        openai_base_url = vector_config.get('openai_base_url') or 'http://************:8002/v1'
        openai_model = vector_config.get('openai_model') or 'bge-small-zh-v1'
        vector_dimension = int(vector_config.get('dimension', 512))
        
        
        # 2. 推送主体近义词库数据
        logger.info("推送主体近义词库数据...")
        entity_synonyms_data = await get_entity_synonyms_test_data(openai_base_url, openai_model, vector_dimension)
        for entity in entity_synonyms_data:
            try:
                await es_client.client.index(
                    index="voc_entity_synonym",
                    body=entity
                )
                logger.info(f"主体近义词数据推送成功: {entity['entity_id']} - {entity['sim_entity']}")
            except Exception as e:
                logger.error(f"主体近义词数据推送失败: {entity['entity_id']}, 错误: {e}")
        
        # 3. 推送描述近义词库数据
        logger.info("推送描述近义词库数据...")
        description_synonyms_data = await get_description_synonyms_test_data(openai_base_url, openai_model, vector_dimension)
        for desc in description_synonyms_data:
            try:
                await es_client.client.index(
                    index="voc_description_synonym",
                    body=desc
                )
                logger.info(f"描述近义词数据推送成功: {desc['description_id']} - {desc['sim_description']}")
            except Exception as e:
                logger.error(f"描述近义词数据推送失败: {desc['description_id']}, 错误: {e}")
        
        
        # 6. 刷新索引
        logger.info("刷新所有索引...")
        indices_to_refresh = [
            "voc_entity_synonym", 
            "voc_description_synonym"
        ]
        for index_name in indices_to_refresh:
            try:
                await es_client.client.indices.refresh(index=index_name)
                logger.info(f"索引 {index_name} 刷新成功")
            except Exception as e:
                logger.warning(f"索引 {index_name} 刷新失败: {e}")
        
        # 7. 验证数据
        logger.info("验证推送的数据...")
        await verify_test_data(es_client)
        
        await es_client.close()
        logger.info("测试数据推送完成!")
        
    except Exception as e:
        logger.error(f"推送测试数据失败: {e}")
        raise


async def verify_test_data(es_client: ElasticsearchClient):
    """验证推送的测试数据"""
    indices = [
        ("voc_entity_synonym", "主体近义词库"),
        ("voc_description_synonym", "描述近义词库")
    ]
    
    logger.info("=" * 60)
    logger.info("数据验证结果 (VOC2.0架构):")
    logger.info("=" * 60)
    
    total_records = 0
    
    for index_name, display_name in indices:
        try:
            response = await es_client.client.count(index=index_name)
            count = response['count']
            total_records += count
            logger.info(f"{display_name:15} ({index_name:30}): {count:3d} 条记录")
            
            # 如果有数据，显示一条示例
            if count > 0:
                search_response = await es_client.client.search(
                    index=index_name,
                    body={"query": {"match_all": {}}, "size": 1}
                )
                
                if search_response['hits']['hits']:
                    hit = search_response['hits']['hits'][0]
                    source = hit['_source']
                    if 'vector' in source:
                        source = source.copy()
                        source['vector'] = f"[{len(source['vector'])}维向量]"
                    if 'vector_embedding' in source:
                        source = source.copy()
                        source['vector_embedding'] = f"[{len(source['vector_embedding'])}维向量]"
                    logger.info(f"  示例: {json.dumps(source, ensure_ascii=False)}")
                
        except Exception as e:
            logger.warning(f"{display_name:15} ({index_name:30}): 验证失败 - {e}")
    
    logger.info("=" * 60)
    logger.info(f"总计: {total_records} 条记录")
    logger.info("=" * 60)


async def search_test():
    """测试搜索功能"""
    try:
        # 加载配置
        config_manager = get_config_manager()
        es_config = config_manager.get_elasticsearch_config()
        # 显式覆盖为生产环境集群与认证
        es_config.hosts = ["http://************:29200"]
        es_config.username = "elastic"
        es_config.password = "L7bzd1gmm+es"
        
        # 创建ES客户端
        es_client = ElasticsearchClient(es_config)
        await es_client.initialize()
        
        # 检查ES连接
        try:
            info = await es_client.client.info()
            logger.info(f"✅ ES连接成功，版本: {info.get('version', {}).get('number', 'unknown')}")
        except Exception as e:
            logger.warning(f"⚠️ ES连接检查异常: {e}")
            logger.info("继续执行搜索测试...")
        
        logger.info("开始测试搜索功能...")
        
        # 测试主体精确搜索
        logger.info("测试主体精确搜索...")
        search_response = await es_client.client.search(
            index="voc_entity_synonym",
            body={
                "query": {
                    "term": {
                        "sim_entity": "车灯"
                    }
                },
                "size": 10
            }
        )
        results = search_response['hits']['hits']
        logger.info(f"搜索 '车灯' 结果: {len(results)} 条")
        for hit in results:
            logger.info(f"  {hit['_source']}")
        
        
        await es_client.close()
        logger.info("搜索功能测试完成!")
        
    except Exception as e:
        logger.error(f"搜索功能测试失败: {e}")
        raise


def print_usage():
    """打印使用说明"""
    print("""
VOC2.0 Elasticsearch测试数据推送脚本 (符合优化架构)

使用方法:
    python push_es_test_data.py [command]

命令:
    push     - 推送测试数据到ES (默认)
    verify   - 验证现有数据
    search   - 测试搜索功能
    help     - 显示此帮助信息

算法后端索引结构 (4个核心索引):
    2. voc_entity_synonym        - 主体近义词库 (含512维向量)
    3. voc_description_synonym   - 描述近义词库 (含512维向量)

示例:
    python push_es_test_data.py           # 推送测试数据
    python push_es_test_data.py push      # 推送测试数据
    python push_es_test_data.py verify    # 验证数据
    python push_es_test_data.py search    # 测试搜索
    """)


async def main():
    """主函数"""
    command = sys.argv[1] if len(sys.argv) > 1 else "push"
    
    if command == "help":
        print_usage()
        return
    elif command == "push":
        await push_test_data()
    elif command == "verify":
        # 加载配置
        config_manager = get_config_manager()
        es_config = config_manager.get_elasticsearch_config()
        
        # 创建ES客户端
        es_client = ElasticsearchClient(es_config)
        await es_client.initialize()
        
        # 检查ES连接
        try:
            info = await es_client.client.info()
            logger.info(f"✅ ES连接成功，版本: {info.get('version', {}).get('number', 'unknown')}")
        except Exception as e:
            logger.warning(f"⚠️ ES连接检查异常: {e}")
            logger.info("继续执行数据验证...")
        
        await verify_test_data(es_client)
        await es_client.close()
    elif command == "search":
        await search_test()
    else:
        logger.error(f"未知命令: {command}")
        print_usage()
        sys.exit(1)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("用户中断操作")
    except Exception as e:
        logger.error(f"脚本执行失败: {e}")
        sys.exit(1)