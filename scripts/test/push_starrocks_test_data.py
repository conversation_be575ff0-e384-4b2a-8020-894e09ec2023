#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
StarRocks测试数据推送脚本 - 符合VOC2.0架构
为VOC2.0智能打标系统的StarRocks数据库推送完整的测试数据
"""

import asyncio
import json
import logging
import sys
import os
from typing import List, Dict, Any
from datetime import datetime
import pymysql
import pymysql.cursors
try:
    import openpyxl  # 读取Excel
except Exception:
    openpyxl = None

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from shared.utils.config import get_config_manager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# StarRocks配置
STARROCKS_HOST = "*************"
STARROCKS_PORT = 9030
STARROCKS_USER = "root"
STARROCKS_PASSWORD = ""
STARROCKS_DATABASE = "voc_db"


class StarRocksClient:
    """StarRocks数据库客户端"""
    
    def __init__(self, host: str, port: int, user: str, password: str, database: str):
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.database = database
        self.connection = None
    
    async def connect(self):
        """连接StarRocks数据库"""
        try:
            self.connection = pymysql.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.database,
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor,
                autocommit=True
            )
            logger.info(f"✅ StarRocks连接成功: {self.host}:{self.port}/{self.database}")
            return True
        except Exception as e:
            logger.error(f"❌ StarRocks连接失败: {e}")
            return False
    
    async def execute_query(self, sql: str, params: tuple = None) -> List[Dict]:
        """执行查询SQL"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(sql, params)
                return cursor.fetchall()
        except Exception as e:
            logger.error(f"查询执行失败: {sql}, 错误: {e}")
            return []
    
    async def execute_insert(self, sql: str, params: tuple = None) -> bool:
        """执行插入SQL"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(sql, params)
                self.connection.commit()
                return True
        except Exception as e:
            logger.error(f"插入执行失败: {sql}, 错误: {e}")
            return False
    
    async def execute_batch_insert(self, sql: str, params_list: List[tuple]) -> int:
        """批量插入数据"""
        try:
            with self.connection.cursor() as cursor:
                affected_rows = cursor.executemany(sql, params_list)
                self.connection.commit()
                logger.info(f"批量插入成功: {affected_rows} 行")
                return affected_rows
        except Exception as e:
            logger.error(f"批量插入失败: {sql}, 错误: {e}")
            return 0
    
    async def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            logger.info("🔌 StarRocks连接已关闭")


def get_opinion_synonym_test_data() -> List[tuple]:
    """获取观点近义词库测试数据 - 匹配opinion_synonym表结构
    
    根据字段映射表：
    - id: 主键ID
    - opinion_id: 六级非规范观点ID
    - opinion: 六级非规范观点
    - entity_id: 六级非规范观点主体ID
    - entity: 六级非规范观点主体
    - description_id: 六级非规范观点描述ID
    - description: 六级非规范观点描述
    - standard_opinion_id: 五级标准观点ID
    - standard_opinion: 五级标准观点
    """
    return [
        (1, 'opinion_001', '大灯好看', 'entity_001', '大灯', 'desc_001', '好看', 'std_opinion_001', '前大灯外观设计好看', 1, datetime.now(), datetime.now()),
        (2, 'opinion_002', '内饰丑', 'entity_002', '内饰', 'desc_002', '丑', 'std_opinion_002', '内饰设计风格丑', 1, datetime.now(), datetime.now()),
        (3, 'opinion_003', '动力强劲', 'entity_003', '动力', 'desc_003', '强劲', 'std_opinion_003', '发动机动力性能强劲', 1, datetime.now(), datetime.now()),
        (4, 'opinion_004', '空间宽敞', 'entity_004', '空间', 'desc_004', '宽敞', 'std_opinion_004', '车内乘坐空间宽敞', 1, datetime.now(), datetime.now()),
        (5, 'opinion_005', '油耗省', 'entity_005', '油耗', 'desc_005', '省', 'std_opinion_005', '燃油经济性省', 1, datetime.now(), datetime.now()),
        (6, 'opinion_006', '座椅舒适', 'entity_006', '座椅', 'desc_006', '舒适', 'std_opinion_006', '座椅舒适性舒适', 1, datetime.now(), datetime.now()),
        (7, 'opinion_007', '操控精准', 'entity_007', '操控', 'desc_007', '精准', 'std_opinion_007', '操控性能精准', 1, datetime.now(), datetime.now()),
        (8, 'opinion_008', '噪音安静', 'entity_008', '噪音', 'desc_008', '安静', 'std_opinion_008', '车内噪音控制安静', 1, datetime.now(), datetime.now()),
        (9, 'opinion_009', '配置丰富', 'entity_009', '配置', 'desc_009', '丰富', 'std_opinion_009', '车辆配置丰富', 1, datetime.now(), datetime.now()),
        (10, 'opinion_010', '价格合理', 'entity_010', '价格', 'desc_010', '合理', 'std_opinion_010', '价格性价比合理', 1, datetime.now(), datetime.now())
    ]


def get_sentiment_rule_keywords_test_data() -> List[tuple]:
    """从Excel按行读取情感规则关键词（第二列=一级情感，第三列=二级情感，第四列=规则关键词）。

    文件: 项目根目录 `情感层级分类定义.xlsx`
    规则关键词按分隔符拆分为多条（每个关键词一行）
    """
    excel_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), '情感层级分类定义.xlsx')

    if openpyxl is None:
        logger.error("缺少 openpyxl 依赖，请先安装: pip install openpyxl")
        raise RuntimeError("openpyxl not installed")
    if not os.path.exists(excel_path):
        logger.error(f"未找到Excel文件: {excel_path}")
        raise FileNotFoundError(excel_path)

    workbook = openpyxl.load_workbook(excel_path, data_only=True)

    def split_keywords(text: Any) -> List[str]:
        if text is None:
            return []
        normalized = str(text)
        for sep in ['\n', '、', '，', '；', ';', ',']:
            normalized = normalized.replace(sep, ',')
        normalized = normalized.replace('\u3000', ' ').replace('  ', ' ')
        parts: List[str] = []
        for seg in normalized.split(','):
            seg = seg.strip()
            if not seg:
                continue
            if ' ' in seg:
                for s in seg.split(' '):
                    s = s.strip()
                    if s:
                        parts.append(s)
            else:
                parts.append(seg)
        return parts

    rows: List[tuple] = []
    next_id = 1
    for sheet in workbook.worksheets:
        is_first_row = True
        # 默认列索引（兼容旧格式）：0=一级情感，1=二级情感，3=规则关键词；类型列可选
        idx_primary = 0
        idx_secondary = 1
        idx_keywords = 3
        idx_type = 4
        for row in sheet.iter_rows(values_only=True):
            if not row or len(row) < 4:
                continue
            if is_first_row:
                is_first_row = False
                headers = [str(v).strip() if v is not None else '' for v in row]
                header_set = set(headers)
                # 如果首行看起来是表头，则根据表头名定位列，并跳过该行
                if header_set & {'主项', '子项', '相关描述', '一级情感', '二级情感', '规则关键词', '情感', '级别', '关键词', '类型'}:
                    def find_idx(names: List[str], default: int) -> int:
                        for name in names:
                            if name in headers:
                                return headers.index(name)
                        return default
                    idx_primary = find_idx(['一级情感', '情感'], 0)
                    idx_secondary = find_idx(['二级情感', '级别'], 1)
                    idx_keywords = find_idx(['规则关键词', '关键词', '相关描述'], 3)
                    idx_type = headers.index('类型') if '类型' in headers else None
                    continue

            # 提取各列
            try:
                primary = row[idx_primary]
                secondary = row[idx_secondary]
                desc = row[idx_keywords]
                rule_type = (row[idx_type] if idx_type is not None and idx_type < len(row) else None)
            except Exception:
                continue
            if not primary or not secondary:
                continue

            keywords = split_keywords(desc)
            if not keywords:
                keywords = [str(secondary).strip()]
            for kw in keywords:
                rows.append((
                    next_id,
                    str(primary).strip(),
                    str(secondary).strip(),
                    (str(rule_type).strip() if rule_type is not None and str(rule_type).strip() != '' else None),
                    kw,
                    datetime.now(),
                    datetime.now()
                ))
                next_id += 1

    logger.info(f"从Excel读取情感规则关键词: {len(rows)} 条 (按行读取 2/3/4 列)")
    return rows


def get_scenario_rule_keywords_test_data() -> List[tuple]:
    """从Excel按行读取(第二列=一级场景, 第三列=二级场景, 第四列=规则关键词)。

    - 文件: 项目根目录 `汽车场景分析结构体系-0422.xlsx`
    - 规则关键词按 "、，；; , 换行 空格" 拆分，生成“每个关键词一行”
    """
    excel_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), '汽车场景分析结构体系-0422.xlsx')

    if openpyxl is None:
        logger.error("缺少 openpyxl 依赖，请先安装: pip install openpyxl")
        raise RuntimeError("openpyxl not installed")
    if not os.path.exists(excel_path):
        logger.error(f"未找到Excel文件: {excel_path}")
        raise FileNotFoundError(excel_path)

    workbook = openpyxl.load_workbook(excel_path, data_only=True)

    def split_keywords(text: Any) -> List[str]:
        if text is None:
            return []
        normalized = str(text)
        for sep in ['\n', '、', '，', '；', ';', ',']:
            normalized = normalized.replace(sep, ',')
        normalized = normalized.replace('\u3000', ' ').replace('  ', ' ')
        parts: List[str] = []
        for seg in normalized.split(','):
            seg = seg.strip()
            if not seg:
                continue
            if ' ' in seg:
                for s in seg.split(' '):
                    s = s.strip()
                    if s:
                        parts.append(s)
            else:
                parts.append(seg)
        return parts

    rows: List[tuple] = []
    next_id = 1

    # 仅处理指定工作表：用车场景指标
    try:
        target_sheet = workbook['用车场景指标']
    except KeyError:
        logger.error("未找到名为 '用车场景指标' 的工作表，请检查Excel文件")
        raise

    for sheet in [target_sheet]:
        is_first_row = True
        for row in sheet.iter_rows(values_only=True):
            if not row or len(row) < 4:
                continue
            # 跳过首行表头（第二/三/四列为列名）
            if is_first_row:
                is_first_row = False
                header_vals = [str(v).strip() if v is not None else '' for v in row[:4]]
                expected_headers = {'分类', '主项', '子项', '相关描述'}
                if set(header_vals) >= expected_headers:
                    # 表头正确，跳过
                    continue
                else:
                    logger.error(f"工作表 '{sheet.title}' 首行表头不符合要求，期望包含: {expected_headers}，实际: {header_vals}")
                    raise ValueError("Excel表头不符合要求：必须为 ‘分类/主项/子项/相关描述’")

            primary = row[1]
            secondary = row[2]
            desc = row[3]
            if not primary or not secondary:
                continue
            keywords = split_keywords(desc)
            if not keywords:
                keywords = [str(secondary).strip()]
            for kw in keywords:
                rows.append((next_id, str(primary).strip(), str(secondary).strip(), kw, datetime.now(), datetime.now()))
                next_id += 1

    logger.info(f"从Excel读取场景规则关键词: {len(rows)} 条 (按行读取 2/3/4 列)")
    return rows


def get_error_records_test_data() -> List[tuple]:
    """获取错误记录测试数据 - 匹配error_records表结构"""
    return [
        (1, 'topic_001', '这是一条测试错误记录文本，用于验证错误处理功能', 5, 'PROCESSING_ERROR', '处理超时，重试5次后失败', datetime.now(), 'FAILED', datetime.now()),
        (2, 'topic_002', '另一条测试错误记录，模拟网络连接问题', 3, 'NETWORK_ERROR', '网络连接超时', datetime.now(), 'RETRYING', datetime.now()),
        (3, 'topic_003', '第三条测试错误记录，模拟数据格式错误', 2, 'FORMAT_ERROR', '数据格式不正确', datetime.now(), 'PENDING', datetime.now())
    ]


async def push_test_data():
    """推送测试数据到StarRocks"""
    try:
        # 创建StarRocks客户端
        sr_client = StarRocksClient(
            host=STARROCKS_HOST,
            port=STARROCKS_PORT,
            user=STARROCKS_USER,
            password=STARROCKS_PASSWORD,
            database=STARROCKS_DATABASE
        )
        
        # 连接数据库
        if not await sr_client.connect():
            logger.error("无法连接到StarRocks数据库")
            return False
        
        logger.info("开始推送测试数据到StarRocks (VOC2.0架构)...")
        
        # 1. 推送观点近义词库数据
        logger.info("推送观点近义词库数据...")
        opinion_data = get_opinion_synonym_test_data()
        opinion_sql = """
            INSERT INTO opinion_synonym 
            (id, opinion_id, opinion, entity_id, entity, description_id, description, standard_opinion_id, standard_opinion, status, created_time, updated_time) 
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        success_count = await sr_client.execute_batch_insert(opinion_sql, opinion_data)
        logger.info(f"观点近义词库数据推送完成: {success_count} 条记录")
        
        # 2. 推送二级情感规则关键词数据
        logger.info("推送二级情感规则关键词数据...")
        sentiment_data = get_sentiment_rule_keywords_test_data()
        sentiment_sql = """
            INSERT INTO sentiment_rule_keywords 
            (id, primary_sentiment, secondary_sentiment, rule_type, rule_keywords, created_at, updated_at) 
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """
        success_count = await sr_client.execute_batch_insert(sentiment_sql, sentiment_data)
        logger.info(f"二级情感规则关键词数据推送完成: {success_count} 条记录")
        
        # 3. 推送用车场景规则关键词数据
        logger.info("推送用车场景规则关键词数据...")
        scenario_data = get_scenario_rule_keywords_test_data()
        scenario_sql = """
            INSERT INTO scenario_rule_keywords 
            (id, primary_scenario, secondary_scenario, rule_keywords, created_at, updated_at) 
            VALUES (%s, %s, %s, %s, %s, %s)
        """
        success_count = await sr_client.execute_batch_insert(scenario_sql, scenario_data)
        logger.info(f"用车场景规则关键词数据推送完成: {success_count} 条记录")
        
        # 4. 推送错误记录数据
        logger.info("推送错误记录数据...")
        error_data = get_error_records_test_data()
        error_sql = """
            INSERT INTO error_records 
            (id, topic_id, topic_text, retry_count, error_type, error_msg, last_ts, final_status, created_time) 
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        success_count = await sr_client.execute_batch_insert(error_sql, error_data)
        logger.info(f"错误记录数据推送完成: {success_count} 条记录")
        
        # 5. 验证数据
        logger.info("验证推送的数据...")
        await verify_test_data(sr_client)
        
        await sr_client.close()
        logger.info("测试数据推送完成!")
        return True
        
    except Exception as e:
        logger.error(f"推送测试数据失败: {e}")
        return False


async def verify_test_data(sr_client: StarRocksClient):
    """验证推送的测试数据"""
    tables = [
        ("opinion_synonym", "观点近义词库"),
        ("sentiment_rule_keywords", "二级情感规则关键词表"),
        ("scenario_rule_keywords", "用车场景规则关键词表"),
        ("error_records", "错误记录表")
    ]
    
    logger.info("=" * 60)
    logger.info("数据验证结果 (VOC2.0架构 - StarRocks):")
    logger.info("=" * 60)
    
    total_records = 0
    
    for table_name, display_name in tables:
        try:
            count_sql = f"SELECT COUNT(*) as count FROM {table_name}"
            result = await sr_client.execute_query(count_sql)
            count = result[0]['count'] if result else 0
            total_records += count
            logger.info(f"{display_name:20} ({table_name:30}): {count:3d} 条记录")
            
            # 如果有数据，显示一条示例
            if count > 0:
                sample_sql = f"SELECT * FROM {table_name} LIMIT 1"
                sample_result = await sr_client.execute_query(sample_sql)
                if sample_result:
                    sample = sample_result[0]
                    logger.info(f"  示例: {json.dumps(sample, ensure_ascii=False, default=str)}")
                
        except Exception as e:
            logger.warning(f"{display_name:20} ({table_name:30}): 验证失败 - {e}")
    
    logger.info("=" * 60)
    logger.info(f"总计: {total_records} 条记录")
    logger.info("=" * 60)


async def search_test():
    """测试搜索功能"""
    try:
        # 创建StarRocks客户端
        sr_client = StarRocksClient(
            host=STARROCKS_HOST,
            port=STARROCKS_PORT,
            user=STARROCKS_USER,
            password=STARROCKS_PASSWORD,
            database=STARROCKS_DATABASE
        )
        
        # 连接数据库
        if not await sr_client.connect():
            logger.error("无法连接到StarRocks数据库")
            return False
        
        logger.info("开始测试搜索功能...")
        
        # 测试观点近义词搜索
        logger.info("测试观点近义词搜索...")
        search_sql = "SELECT * FROM opinion_synonym WHERE entity LIKE '%大灯%' LIMIT 5"
        results = await sr_client.execute_query(search_sql)
        logger.info(f"搜索 '大灯' 结果: {len(results)} 条")
        for result in results:
            logger.info(f"  {result}")
        
        # 测试情感规则搜索
        logger.info("测试情感规则搜索...")
        search_sql = "SELECT * FROM sentiment_rule_keywords WHERE primary_sentiment = '正面' LIMIT 5"
        results = await sr_client.execute_query(search_sql)
        logger.info(f"搜索 '正面' 情感结果: {len(results)} 条")
        for result in results:
            logger.info(f"  {result}")
        
        # 测试场景规则搜索
        logger.info("测试场景规则搜索...")
        search_sql = "SELECT * FROM scenario_rule_keywords WHERE primary_scenario = '路况' LIMIT 5"
        results = await sr_client.execute_query(search_sql)
        logger.info(f"搜索 '路况' 场景结果: {len(results)} 条")
        for result in results:
            logger.info(f"  {result}")
        
        await sr_client.close()
        logger.info("搜索功能测试完成!")
        return True
        
    except Exception as e:
        logger.error(f"搜索功能测试失败: {e}")
        return False


def print_usage():
    """打印使用说明"""
    print("""
VOC2.0 StarRocks测试数据推送脚本 (符合优化架构)

使用方法:
    python push_starrocks_test_data.py [command]

命令:
    push     - 推送测试数据到StarRocks (默认)
    verify   - 验证现有数据
    search   - 测试搜索功能
    help     - 显示此帮助信息

算法后端和后处理服务表结构 (4个核心表):
    1. opinion_synonym           - 观点近义词库 (长安运维手动维护)
    2. sentiment_rule_keywords   - 二级情感规则关键词表 (后处理服务)
    3. scenario_rule_keywords    - 用车场景规则关键词表 (后处理服务)
    4. error_records             - 错误记录表 (超过5次重试失败的数据)

示例:
    python push_starrocks_test_data.py           # 推送测试数据
    python push_starrocks_test_data.py push      # 推送测试数据
    python push_starrocks_test_data.py verify    # 验证数据
    python push_starrocks_test_data.py search    # 测试搜索
    """)


async def main():
    """主函数"""
    command = sys.argv[1] if len(sys.argv) > 1 else "push"
    
    if command == "help":
        print_usage()
        return
    elif command == "push":
        success = await push_test_data()
        if not success:
            sys.exit(1)
    elif command == "verify":
        # 创建StarRocks客户端
        sr_client = StarRocksClient(
            host=STARROCKS_HOST,
            port=STARROCKS_PORT,
            user=STARROCKS_USER,
            password=STARROCKS_PASSWORD,
            database=STARROCKS_DATABASE
        )
        
        # 连接数据库
        if not await sr_client.connect():
            logger.error("无法连接到StarRocks数据库")
            sys.exit(1)
        
        await verify_test_data(sr_client)
        await sr_client.close()
    elif command == "search":
        success = await search_test()
        if not success:
            sys.exit(1)
    else:
        logger.error(f"未知命令: {command}")
        print_usage()
        sys.exit(1)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("用户中断操作")
    except Exception as e:
        logger.error(f"脚本执行失败: {e}")
        sys.exit(1)
