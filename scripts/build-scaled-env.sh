#!/bin/bash
# VOC2.0 扩展部署（多实例）一键部署脚本

set -e

echo "🚀 VOC2.0 扩展部署开始..."

# 检查生产镜像是否存在
echo "🔍 检查生产镜像..."
if docker image inspect voc-services:latest &> /dev/null; then
    echo "✅ 生产镜像 voc-services:latest 已存在"
    docker images | grep voc-services || true
else
    echo "📦 生产镜像不存在，开始构建（不使用缓存）..."
    docker build --no-cache -t voc-services:latest .
    echo "✅ 生产镜像构建完成"
    docker images | grep voc-services || true
fi

# 检查外部API可用性
echo "🔍 检查外部API可用性..."
echo "   检查UIE API..."
for port in 5000 5001 5002; do
    if curl -s --connect-timeout 3 http://*************:$port/health &>/dev/null; then
        echo "   ✅ UIE API :$port 可用"
    else
        echo "   ⚠️  UIE API :$port 不可用"
    fi
done

echo "   检查LLM API..."
if curl -s --connect-timeout 3 http://localhost:8001/health &>/dev/null; then
    echo "   ✅ LLM API :8001 可用"
else
    echo "   ⚠️  LLM API :8001 不可用"
fi

echo "   检查Intent API..."
if curl -s --connect-timeout 3 http://*************:5001/health &>/dev/null; then
    echo "   ✅ Intent API :5001 可用"
else
    echo "   ⚠️  Intent API :5001 不可用"
fi

# 停止旧环境
echo "🔄 清理旧环境..."
docker compose -f docker-compose.scaled.yml down --remove-orphans 2>/dev/null || true

# 创建日志目录
mkdir -p logs/scaled

# 拉取基础设施镜像
echo "📥 拉取基础设施镜像..."
docker pull starrocks/allin1-ubuntu:latest &
docker pull docker.elastic.co/elasticsearch/elasticsearch:8.8.0 &
docker pull confluentinc/cp-zookeeper:latest &
docker pull confluentinc/cp-kafka:latest &
wait
echo "✅ 基础设施镜像拉取完成"

# 启动扩展部署
echo "🚀 启动扩展部署..."
docker compose -f docker-compose.scaled.yml up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 15

# 检查服务状态
echo "🔍 检查服务状态..."
docker compose -f docker-compose.scaled.yml ps

echo ""
echo "✅ 扩展部署完成！"
echo ""
echo "🌐 服务访问地址："
echo "   API网关:         http://localhost:8100"
echo "   UIE实例1:        http://localhost:8140"
echo "   UIE实例2:        http://localhost:8141" 
echo "   UIE实例3:        http://localhost:8142"
echo "   LLM实例1:        http://localhost:8500"
echo "   LLM实例2:        http://localhost:8501"
echo "   Intent实例1:     http://localhost:8600"
echo "   Intent实例2:     http://localhost:8601"
echo "   Elasticsearch:   http://localhost:9200"
echo "   StarRocks:       http://localhost:8030"
echo ""
echo "📊 多实例配置："
echo "   UIE服务:    3个实例 (高并发实体抽取)"
echo "   LLM服务:    2个实例 (大语言模型处理)" 
echo "   Intent服务: 2个实例 (意图情感分析)"
echo "   其他服务:   单实例 (稳定运行)"
echo ""
echo "📋 监控命令："
echo "   查看状态: docker compose -f docker-compose.scaled.yml ps"
echo "   查看日志: docker compose -f docker-compose.scaled.yml logs -f [service-name]"
echo "   重启服务: docker compose -f docker-compose.scaled.yml restart [service-name]"
echo "   停止环境: docker compose -f docker-compose.scaled.yml down"
echo ""
echo "⚡ 性能特点："
echo "   - 多实例并行处理，大幅提升吞吐量"
echo "   - 自动负载均衡，充分利用资源"
echo "   - 故障隔离，单实例故障不影响整体"
echo "   - 适合高并发生产环境"
