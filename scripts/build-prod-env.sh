#!/bin/bash
# VOC2.0 生产环境一键部署脚本

set -e

echo "🚀 VOC2.0 生产环境部署开始..."

# 检查生产镜像是否存在
echo "🔍 检查生产镜像..."
if docker image inspect voc-services:latest &> /dev/null; then
    echo "✅ 生产镜像 voc-services:latest 已存在"
    docker images | grep voc-services || true
    
    # 询问是否重新构建
    echo "❓ 是否重新构建镜像？ (y/N)"
    read -n 1 -r rebuild
    echo
    if [ "$rebuild" = "y" ] || [ "$rebuild" = "Y" ]; then
        echo "📦 重新构建生产镜像（不使用缓存）..."
        docker build --no-cache -t voc-services:latest .
        echo "✅ 生产镜像重新构建完成"
    fi
else
    echo "📦 生产镜像不存在，开始构建（不使用缓存）..."
    docker build --no-cache -t voc-services:latest .
    echo "✅ 生产镜像构建完成"
    docker images | grep voc-services || true
fi

# 停止旧环境
echo "🔄 清理旧环境..."
docker compose -f docker-compose.yml down --remove-orphans 2>/dev/null || true

# 创建日志目录
mkdir -p logs/prod

# 智能检测基础设施服务
echo "🔍 检测基础设施服务状态..."

check_service() {
    local service_name=$1
    local host=$2
    local port=$3
    
    if timeout 3 bash -c "</dev/tcp/$host/$port" 2>/dev/null; then
        echo "✅ $service_name ($host:$port) 已运行"
        return 0
    else
        echo "❌ $service_name ($host:$port) 未运行"
        return 1
    fi
}

# 检查外部基础设施服务
external_kafka_ok=false
external_es_ok=false
external_starrocks_ok=false

echo "📡 检查外部基础设施服务..."
if check_service "外部Kafka" "*************" "5084"; then
    external_kafka_ok=true
fi

if check_service "外部Elasticsearch" "*************" "9200"; then
    external_es_ok=true
fi

if check_service "外部StarRocks" "*************" "9030"; then
    external_starrocks_ok=true
fi

# 检查本地Docker基础设施服务
local_kafka_ok=false
local_es_ok=false
local_starrocks_ok=false

echo "🐳 检查本地Docker基础设施服务..."
if check_service "本地Kafka" "localhost" "9092"; then
    local_kafka_ok=true
fi

if check_service "本地Elasticsearch" "localhost" "9200"; then
    local_es_ok=true
fi

if check_service "本地StarRocks" "localhost" "8030"; then
    local_starrocks_ok=true
fi

# 决定部署策略
use_external_services=false
skip_infrastructure=false

if [ "$external_kafka_ok" = true ] && [ "$external_es_ok" = true ] && [ "$external_starrocks_ok" = true ]; then
    echo ""
    echo "🎯 检测到完整的外部基础设施服务！"
    echo "   - Kafka: *************:5084 ✅"
    echo "   - Elasticsearch: *************:9200 ✅"
    echo "   - StarRocks: *************:9030 ✅"
    echo ""
    echo "❓ 是否使用外部服务而不启动Docker基础设施? (Y/n)"
    read -n 1 -r use_external
    echo
    if [ "$use_external" != "n" ] && [ "$use_external" != "N" ]; then
        use_external_services=true
        skip_infrastructure=true
        echo "✅ 将使用外部基础设施服务"
    fi
elif [ "$local_kafka_ok" = true ] || [ "$local_es_ok" = true ] || [ "$local_starrocks_ok" = true ]; then
    echo ""
    echo "⚠️  检测到部分本地基础设施服务正在运行！"
    echo "   这可能导致端口冲突"
    echo ""
    echo "❓ 是否继续启动完整的Docker基础设施? (y/N)"
    read -n 1 -r continue_anyway
    echo
    if [ "$continue_anyway" != "y" ] && [ "$continue_anyway" != "Y" ]; then
        echo "❌ 部署取消。请先停止冲突的服务或选择使用外部服务"
        exit 1
    fi
fi

# 生成动态的docker-compose配置
compose_file="docker-compose.yml"
if [ "$use_external_services" = true ]; then
    echo "📝 生成连接外部服务的配置..."
    compose_file="docker-compose.external.yml"
    
    # 从原始配置中提取VOC服务，连接外部基础设施
    cat > $compose_file << 'EOF'
# VOC2.0 生产环境配置 - 连接外部基础设施
version: '3.8'

networks:
  voc-network:
    driver: bridge

services:
EOF

    # 提取VOC服务配置并修改环境变量
    python3 << 'EOL'
import yaml
import sys

# 读取原始配置
with open('docker-compose.yml', 'r', encoding='utf-8') as f:
    config = yaml.safe_load(f)

# 过滤出VOC服务（排除基础设施）
voc_services = {}
for service_name, service_config in config['services'].items():
    if service_name not in ['starrocks-fe', 'starrocks-be', 'elasticsearch', 'kafka', 'zookeeper', 'kibana', 'kafka-ui']:
        # 修改环境变量为外部服务
        if 'environment' in service_config:
            new_env = []
            for env_var in service_config['environment']:
                if env_var.startswith('KAFKA_BOOTSTRAP_SERVERS='):
                    new_env.append('KAFKA_BOOTSTRAP_SERVERS=*************:5084')
                elif env_var.startswith('ELASTICSEARCH_URL='):
                    new_env.append('ELASTICSEARCH_URL=http://*************:9200')
                elif env_var.startswith('STARROCKS_HOST='):
                    new_env.append('STARROCKS_HOST=*************')
                elif env_var.startswith('STARROCKS_PORT='):
                    new_env.append('STARROCKS_PORT=9030')
                else:
                    new_env.append(env_var)
            service_config['environment'] = new_env
        
        # 移除depends_on中的基础设施依赖
        if 'depends_on' in service_config:
            new_depends = [dep for dep in service_config['depends_on'] 
                          if dep not in ['kafka', 'elasticsearch', 'starrocks-fe']]
            if new_depends:
                service_config['depends_on'] = new_depends
            else:
                del service_config['depends_on']
        
        voc_services[service_name] = service_config

# 写入新配置
config['services'] = voc_services
with open('docker-compose.external.yml', 'a', encoding='utf-8') as f:
    yaml.dump({'services': voc_services}, f, default_flow_style=False, allow_unicode=True)
EOL

    echo "✅ 外部服务配置生成完成"
else
    echo "📦 将启动完整的Docker基础设施"
    
    if [ "$skip_infrastructure" = false ]; then
        # 拉取基础设施镜像
        echo "📥 拉取基础设施镜像..."
        docker pull starrocks/fe-ubuntu:latest &
        docker pull starrocks/be-ubuntu:latest &
        docker pull docker.elastic.co/elasticsearch/elasticsearch:8.11.0 &
        docker pull confluentinc/cp-zookeeper:7.4.0 &
        docker pull confluentinc/cp-kafka:7.4.0 &
        wait
        echo "✅ 基础设施镜像拉取完成"
    fi
fi

# 启动服务
echo "🚀 启动服务..."
docker compose -f $compose_file up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker compose -f $compose_file ps

echo ""
echo "✅ 生产环境部署完成！"
echo ""

if [ "$use_external_services" = true ]; then
    echo "🌐 VOC服务访问地址："
    echo "   API网关:        http://localhost:8100"
    echo "   文本处理:       http://localhost:8200"
    echo "   规则匹配:       http://localhost:8300"
    echo "   向量匹配:       http://localhost:8400"
    echo "   UIE服务:        http://localhost:8140"
    echo "   LLM服务:        http://localhost:8500"
    echo "   Intent服务:     http://localhost:8600"
    echo "   品牌归因:       http://localhost:8700"
    echo "   错误处理:       http://localhost:8800"
    echo ""
    echo "🔗 连接的外部基础设施："
    echo "   Kafka:          *************:5084"
    echo "   Elasticsearch:  *************:9200"
    echo "   StarRocks:      *************:9030"
    echo "   Embedding:      *************:7777"
    echo ""
    echo "💡 部署特点："
    echo "   - 智能检测到外部基础设施，跳过Docker基础设施部署"
    echo "   - 直接连接已有的Kafka、ES、StarRocks服务"
    echo "   - 避免端口冲突，与deploy_all_optimized.py完美配合"
else
    echo "🌐 服务访问地址："
    echo "   API网关:        http://localhost:8000"
    echo "   Elasticsearch:  http://localhost:9200" 
    echo "   Kibana:         http://localhost:5601"
    echo "   Kafka UI:       http://localhost:8080"
    echo "   StarRocks:      http://localhost:8030"
    echo "   Grafana:        http://localhost:3000"
    echo ""
    echo "🗄️  数据持久化："
    echo "   数据卷会自动创建，停止容器不会丢失数据"
    echo "   完全清理: docker compose -f docker-compose.yml down -v"
    echo ""
    echo "💡 部署特点："
    echo "   - 完整的Docker基础设施环境"
    echo "   - 包含所有必要的基础服务"
    echo "   - 数据持久化，重启不丢失"
fi

echo ""
echo "📊 管理命令："
echo "   查看状态: docker compose -f $compose_file ps"
echo "   查看日志: docker compose -f $compose_file logs -f [service-name]"
echo "   重启服务: docker compose -f $compose_file restart [service-name]"
echo "   停止环境: docker compose -f $compose_file down"
echo ""
echo "🎯 通用特点："
echo "   - 代码打包在镜像中，稳定可靠"
echo "   - 修改代码需重新构建镜像"
echo "   - 智能检测基础设施，避免冲突"
