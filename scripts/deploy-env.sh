#!/bin/bash
# VOC2.0 统一部署脚本入口
# 使用方法: ./scripts/deploy-env.sh [environment]
# 环境选项: dev, prod, scaled

set -e

# 默认环境
ENVIRONMENT=${1:-prod}

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo -e "${BLUE}🚀 VOC2.0 统一部署脚本${NC}"
    echo ""
    echo "使用方法:"
    echo "  $0 [environment]"
    echo ""
    echo "环境选项:"
echo -e "  ${GREEN}dev${NC}        - 开发环境 (代码挂载，快速迭代)"
echo -e "  ${YELLOW}prod${NC}       - 生产环境 (智能检测基础设施，自动选择模式)"
echo -e "  ${PURPLE}scaled${NC}     - 扩展部署 (多实例，高并发)"
    echo ""
    echo "示例:"
echo "  $0 dev      # 部署开发环境"
echo "  $0 prod     # 部署生产环境(智能检测已有基础设施)"
echo "  $0 scaled   # 部署扩展环境"
    echo ""
}

# 验证环境参数
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_help
    exit 0
fi

# 检查环境参数是否有效
case "$ENVIRONMENT" in
    dev|prod|scaled)
        # 有效的环境
        ;;
    *)
        echo -e "${RED}❌ 无效的环境: $ENVIRONMENT${NC}"
        echo ""
        show_help
        exit 1
        ;;
esac

# 显示环境信息
echo -e "${BLUE}🚀 VOC2.0 部署脚本${NC}"
echo -e "环境: ${GREEN}$ENVIRONMENT${NC}"
echo ""

# 根据环境调用对应的脚本
case $ENVIRONMENT in
    "dev")
        echo -e "${GREEN}📦 启动开发环境部署...${NC}"
        chmod +x scripts/build-dev-env.sh 2>/dev/null || true
        bash scripts/build-dev-env.sh
        ;;
    "prod")
        echo -e "${YELLOW}🏭 启动生产环境部署...${NC}"
        chmod +x scripts/build-prod-env.sh 2>/dev/null || true
        bash scripts/build-prod-env.sh
        ;;
    "scaled")
        echo -e "${PURPLE}⚡ 启动扩展部署...${NC}"
        chmod +x scripts/build-scaled-env.sh 2>/dev/null || true
        bash scripts/build-scaled-env.sh
        ;;

esac

echo ""
echo -e "${GREEN}🎉 部署完成！${NC}"
echo ""
echo -e "${BLUE}💡 快速操作指南:${NC}"

case $ENVIRONMENT in
    "dev")
        echo "  修改代码: 直接编辑文件，服务自动重启"
        echo "  查看日志: docker-compose -f docker-compose.dev.yml logs -f"
        echo "  停止环境: docker-compose -f docker-compose.dev.yml down"
        ;;
    "prod")
        echo "  查看状态: docker-compose -f docker-compose.yml ps"
        echo "  查看日志: docker-compose -f docker-compose.yml logs -f"
        echo "  停止环境: docker-compose -f docker-compose.yml down"
        ;;
    "scaled")
        echo "  查看状态: docker-compose -f docker-compose.scaled.yml ps"
        echo "  查看日志: docker-compose -f docker-compose.scaled.yml logs -f"
        echo "  停止环境: docker-compose -f docker-compose.scaled.yml down"
        ;;

esac
