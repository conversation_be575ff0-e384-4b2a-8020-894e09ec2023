#!/bin/bash
# VOC2.0 开发环境一键部署脚本

set -e

echo "🚀 VOC2.0 开发环境部署开始..."

# 检查基础镜像是否存在
echo "🔍 检查基础镜像..."
if docker image inspect voc-base:latest &> /dev/null; then
    echo "✅ 基础镜像 voc-base:latest 已存在，跳过构建"
    docker images | grep voc-base || true
else
    echo "📦 基础镜像不存在，开始构建（不使用缓存）..."
    docker build --no-cache -f Dockerfile.base -t voc-base:latest .
    echo "✅ 基础镜像构建完成"
    docker images | grep voc-base || true
fi

# 停止旧环境
echo "🔄 清理旧环境..."
docker compose -f docker-compose.dev.yml down --remove-orphans 2>/dev/null || true

# 创建日志目录
mkdir -p logs/dev

# 启动开发环境
echo "🚀 启动开发环境..."
docker compose -f docker-compose.dev.yml up -d

echo ""
echo "✅ 开发环境部署完成！"
echo "🌐 API网关: http://localhost:8100"
echo "📝 查看日志: docker compose -f docker-compose.dev.yml logs -f"
echo "🔄 重启服务: docker compose -f docker-compose.dev.yml restart [service-name]"
echo "🛑 停止环境: docker compose -f docker-compose.dev.yml down"
echo ""
echo "💡 现在可以直接修改代码，服务会自动重启！"
