# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

VOC2.0 智能打标系统 (Voice of Customer Intelligent Tagging System) is a microservice-based system for automotive industry customer opinion mining and sentiment analysis. It uses a hybrid approach of rule matching and vector similarity to automatically identify opinion subjects, descriptions, emotional tendencies, and intentions from user reviews.

## Development Commands

### Environment Setup
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate   # Windows

# Install dependencies
pip install -r requirements.txt
```

### Service Management
```bash
# Run individual microservice (each service has its own main.py)
cd services/text-processor
python main.py

# The services use uvicorn and typically run on different ports:
# - gateway: 8000
# - text-processor: 8100
# - rule-matcher: 8110
# - vector-matcher: 8120
# - llm: 8130
# - uie: 8140
# - intent: 8150
# - sentiment: 8160
# - result-assembler: 8170
# - error-handler: 8180
```

### Deployment and Testing
```bash
# One-click deployment of optimized backend
cd scripts/test
python3 deploy_all_optimized.py

# Verify deployment
python3 test_optimized_deployment.py

# Manual deployment steps (if needed)
python3 create_kafka_topics_lightweight.py
python3 create_elasticsearch_indices_optimized.py
python3 create_starrocks_optimized.py
```

### Health Checks
```bash
# Check individual service
curl http://localhost:8100/health

# Check gateway
curl http://localhost:8000/system/health

# Test processing API
curl -X POST http://localhost:8000/api/v1/process \
  -H "Content-Type: application/json" \
  -d '{"topic_id":"test001","topic_text":"长安UNI-T的大灯很好看"}'
```

### Code Quality (based on requirements.txt)
```bash
# Format code
black .
isort .

# Lint code
flake8

# Run tests
pytest
pytest-asyncio
```

## Architecture Overview

### Microservice Architecture (10 Core Services)
The system follows a microservice architecture with 10 core services communicating via Kafka:

1. **gateway** (8000) - API gateway and request routing
2. **text-processor** (8100) - Text preprocessing and routing decisions
3. **rule-matcher** (8110) - Exact rule matching
4. **vector-matcher** (8120) - Fuzzy vector matching  
5. **llm** (8130) - LLM-based opinion extraction
6. **uie** (8140) - UIE model entity extraction
7. **intent** (8150) - Intent recognition
8. **sentiment** (8160) - Sentiment analysis
9. **result-assembler** (8170) - Result assembly and formatting
10. **error-handler** (8180) - Error handling and retry logic

### Data Flow Pattern
```
Raw Text → Text Preprocessing → Smart Routing → Opinion Extraction → Result Assembly → Sentiment/Intent Analysis → Final Output
```

### Technology Stack
- **Web Framework**: FastAPI + Uvicorn (async high-performance APIs)
- **Data Storage**: StarRocks (OLAP data warehouse)
- **Search Engine**: Elasticsearch 8.11+ (vector search, 512-dimensional)
- **Message Queue**: Kafka (8 partitions, lightweight design)
- **ML Framework**: sentence-transformers (text vectorization)
- **Containerization**: Docker + Compose

### Storage Architecture (Lightweight Design)

**Elasticsearch (5 indexes)**:
- `voc_standard_opinion` - Standard opinion library with 512-dim vectors
- `voc_entity_synonym` - Entity synonym library
- `voc_description_synonym` - Description synonym library  
- `voc_new_words` - New word discovery
- `voc_brand_series_model` - Brand/series/model exact matching

**StarRocks (2 tables)**:
- `opinion_synonym` - Opinion synonym mapping (manually maintained)
- `error_records` - Error records for failed processing

**Kafka (8 topics, 1 partition each)**:
- `voc_toModel_topic` → `llm_topic`/`uie_topic` → `intent_topic` → `result_topic`

## Code Architecture Patterns

### Base Service Pattern
All microservices inherit from `BaseService` class in `shared/base/base_service.py`:
- Standardized initialization, logging, and health checks
- Built-in Kafka client integration
- Async/await pattern throughout

### Data Models
Comprehensive data models in `shared/models/schemas.py` (30+ models):
- `BaseKafkaMessage` - Base message format
- `SourceData` - Input data structure
- `OpinionMatch` - Opinion matching results
- `ProcessingStatus` and `ErrorType` enums

### Configuration Management
Centralized configuration in `configs/config.yaml`:
- External service endpoints (StarRocks, Elasticsearch, Kafka, Embedding)
- Service-specific settings
- Environment-specific configurations

### Shared Components
- `shared/database/` - StarRocks and MySQL clients
- `shared/elasticsearch/es_client.py` - Elasticsearch client
- `shared/kafka/kafka_client.py` - Kafka producer/consumer
- `shared/utils/` - Config, logging, text processing, embedding utilities

## External Service Dependencies

The system requires these external services to be running:
- **StarRocks**: *************:9030
- **Elasticsearch**: *************:9200
- **Kafka**: *************:5084
- **Embedding Service**: *************:7777

## Development Workflow

1. **Service Development**: Each service is in `services/{service-name}/` with its own `main.py`
2. **Shared Code**: Common functionality in `shared/` directory
3. **Configuration**: Update `configs/config.yaml` for service settings
4. **Testing**: Use scripts in `scripts/test/` for deployment and testing
5. **Data Models**: Add/modify models in `shared/models/schemas.py`

## Key Integration Points

- All services communicate via Kafka messages using standardized schemas
- Services use shared database clients for StarRocks and Elasticsearch
- Centralized logging and configuration management
- Health check endpoints for monitoring
- Error handling with retry logic and dead letter queues

The system is designed as an algorithm backend focusing on intelligent tagging core logic, pushing processed results to `result_topic` for downstream consumption.