"""
共享数据模型定义
基于VOC2.0智能打标（扫评部分）流程架构方案
"""
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum


class ProcessingStatus(str, Enum):
    """处理状态枚举"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed" 
    FAILED = "failed"
    RETRY = "retry"


class ErrorType(str, Enum):
    """错误类型枚举"""
    MODEL_TIMEOUT = "model_timeout"
    MODEL_ERROR = "model_error"
    NETWORK_ERROR = "network_error"
    DATA_FORMAT_ERROR = "data_format_error"
    UNKNOWN_ERROR = "unknown_error"


# 源数据模型
class SourceData(BaseModel):
    """原始输入数据模型"""
    dataSource: str  # 汽车之家(最细粒度的渠道名称or编码，用于定位不同渠道处理逻辑)
    dataSource_type: str
    title: str  # CS75 PLUS使用一个月感受
    usage_scenario_src: str  # 使用场景来源
    topic_text: List[Dict[str, str]]  # 包含role和content的对象列表
    brand: Optional[str] = None  # 长安
    series: Optional[str] = None  # CS75 PLUS
    ext: Dict[str, Any]  # 扩展-鸿兵备用
    create_time: str  # 2025-08-06 12:00:00


# Kafka消息模型
class BaseKafkaMessage(BaseModel):
    """Kafka消息基础模型"""
    topic_id: str
    source_data: SourceData
    retry_count: int = 0
    error_type: str = ""
    error_msg: str = ""
    last_ts: str = ""


class InitialMessage(BaseKafkaMessage):
    """初始消息模型（voc_toModel_topic）"""
    pass


class CategoryMessage(BaseKafkaMessage):
    """分类后消息模型（llm_topic, uie_topic）"""
    cat_type: str  # "llm" or "uie"
    raw_split_text: Optional[List[str]] = None
    brand_series_list: Optional[List[Dict[str, Any]]] = None
    qaresult: Optional[List[Dict[str, Any]]] = None  # 问卷处理结果，仅用于问卷数据源


class ModelResultMessage(BaseKafkaMessage):
    """模型结果消息（result_topic）"""
    topic_id: str
    source_data: SourceData
    cat_type: str
    retry_count: int
    error_type: str
    error_msg: str
    last_ts: str
    result: List[Dict[str, Any]]
    qaresult: Optional[List[Dict[str, Any]]] = None  # 问卷处理结果，仅用于问卷数据源


class IntentMessage(BaseKafkaMessage):
    """意图情感识别消息（intent_topic）"""
    cat_type: str
    result: List[Dict[str, Any]]


class BrandAttributionMessage(BaseKafkaMessage):
    """品牌归属判断消息（brand_attribution_topic）"""
    cat_type: str
    result: List[Dict[str, Any]]


class ErrorMessage(BaseKafkaMessage):
    """错误消息模型（model_error_topic, model_retry_topic）"""
    pass


# 业务数据模型
class BrandSeries(BaseModel):
    """品牌车系车型数据库模型"""
    id: Optional[int] = None
    brand: str  # 品牌：长安
    series: str  # 车系：长安UNI-Z
    model: Optional[str] = None  # 车型：2025款 500Bar MAX
    synonyms: Optional[List[str]] = None
    is_active: bool = True


class StandardOpinion(BaseModel):
    """长安标准观点库模型（1.1w条）"""
    standard_opinion_id: Optional[int] = None
    standard_opinion: str  # 标准观点描述
    business_tags: Optional[Dict[str, Any]] = None
    category: Optional[str] = None
    is_active: bool = True


class OpinionSynonym(BaseModel):
    """观点近义词库模型"""
    add_entity: str
    add_description: str
    standard_opinion: str
    standard_opinion_id: int


class EntitySynonym(BaseModel):
    """主体近义词库模型"""
    entity_id: Optional[int] = None
    sim_entity: str
    vector: Optional[str] = None  # 向量化字段
    normalized_entity: str
    standard_opinion_id_list: List[int]  # 支持一对多关系
    is_active: bool = True


class DescriptionSynonym(BaseModel):
    """描述近义词库模型"""
    description_id: Optional[int] = None
    sim_description: str
    vector: Optional[str] = None  # 向量化字段
    normalized_description: str
    standard_opinion_id: int  # 一对一关系
    is_active: bool = True


class NewWord(BaseModel):
    """新词库模型"""
    new_entity: str
    new_description: str
    frequency: int = 1
    status: str = "pending"
    processed_by: Optional[str] = None
    processed_at: Optional[datetime] = None


class ErrorRecord(BaseModel):
    """错误库模型"""
    topic_id: str
    topic_text: str
    retry_count: int = 0
    error_type: Optional[str] = None
    last_ts: Optional[str] = None
    final_status: str = "pending"  # 已修改/跳过/人工维护


# 处理结果模型
class Relation(BaseModel):
    """关系模型"""
    text: str
    start: Optional[int] = None
    end: Optional[int] = None


class NormalizedSubject(BaseModel):
    """标准化主体模型"""
    id: str
    text: str
    score: float


class NormalizedDescription(BaseModel):
    """标准化描述模型"""
    id: str
    text: str
    score: float


class NormalizedOpinion(BaseModel):
    """标准化观点模型"""
    id: str
    text: str
    score: float


class StandardViewpoint(BaseModel):
    """长安标准观点模型"""
    id: str
    text: str


class EvaluationObject(BaseModel):
    """评价对象模型"""
    text: str  # 主体
    start: Optional[int] = None
    end: Optional[int] = None
    relations: Dict[str, Any] = Field(default_factory=dict)


class ProcessingResult(BaseModel):
    """处理结果模型"""
    评价对象: List[EvaluationObject] = Field(default_factory=list, alias="evaluation_objects")


class OpinionMatch(BaseModel):
    """观点匹配结果"""
    original_text: str
    original_entity: str
    original_description: str
    normalized_subject: Optional[NormalizedSubject] = None
    normalized_description: Optional[NormalizedDescription] = None
    normalized_opinion: Optional[NormalizedOpinion] = None
    std_viewpoint: Optional[StandardViewpoint] = None
    match_type: str  # "rule" or "vector"


class VectorSearchResult(BaseModel):
    """向量搜索结果"""
    id: str
    score: float
    source: Dict[str, Any]


class RuleMatchResult(BaseModel):
    """规则匹配结果"""
    matched: bool
    entity: Optional[str] = None
    description: Optional[str] = None
    standard_opinion_id: Optional[str] = None  # 改为字符串类型，因为ES中的ID是字符串
    confidence: float = 0.0


class IntentResult(BaseModel):
    """意图识别结果"""
    segment: str  # 原文片段
    intent: str  # 表扬/抱怨/询问等
    confidence: float


class SentimentResult(BaseModel):
    """情感分析结果"""
    segment: str  # 原文片段
    sentiment: str  # "正面", "负面", "中性"
    confidence: float


class BrandAttributionResult(BaseModel):
    """品牌归属判断结果"""
    entity: str
    opinion: str
    segment: str
    brand_series: str  # 判断出的品牌车系，默认为"长安无车系"
    confidence: float


# API响应模型
class APIResponse(BaseModel):
    """API响应基础模型"""
    success: bool
    message: str
    data: Optional[Any] = None
    error_code: Optional[str] = None


class HealthCheckResponse(BaseModel):
    """健康检查响应"""
    status: str
    timestamp: datetime
    service: str
    version: str


# 配置模型
class DatabaseConfig(BaseModel):
    """数据库配置"""
    host: str
    port: int
    username: str
    password: str
    database: str


class ElasticsearchConfig(BaseModel):
    """Elasticsearch配置"""
    hosts: List[str]
    username: Optional[str] = None
    password: Optional[str] = None
    timeout: int = 30


class KafkaConfig(BaseModel):
    """Kafka配置"""
    bootstrap_servers: List[str]
    client_id: str
    group_id: str
    auto_offset_reset: str = "latest"


class ServiceConfig(BaseModel):
    """服务配置"""
    name: str
    version: str
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = False
    log_level: str = "INFO"