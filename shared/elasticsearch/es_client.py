"""
Elasticsearch客户端
"""
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from elasticsearch import AsyncElasticsearch
from shared.models.schemas import ElasticsearchConfig, VectorSearchResult

logger = logging.getLogger(__name__)


class ElasticsearchClient:
    """Elasticsearch客户端"""
    
    def __init__(self, config: ElasticsearchConfig, vector_config: Optional[Dict[str, Any]] = None):
        self.config = config
        self.vector_config = vector_config or {}
        self.client: Optional[AsyncElasticsearch] = None
        
        # 索引名称 - 与create_elasticsearch_indices_optimized.py保持一致
        self.CHANGAN_STANDARD_OPINIONS_INDEX = "voc_standard_opinion"
        self.ENTITY_SYNONYMS_INDEX = "voc_entity_synonym"
        self.DESCRIPTION_SYNONYMS_INDEX = "voc_description_synonym"
        self.BRAND_SERIES_INDEX = "voc_brand_series_model"
        self.NEW_WORDS_INDEX = "voc_new_words"
    
    def _get_current_time(self) -> str:
        """获取当前时间的ISO格式字符串"""
        return datetime.now().isoformat()
    
    async def initialize(self):
        """初始化Elasticsearch客户端"""
        try:
            # ES客户端配置 - 针对ES 8.11.3优化
            client_config = {
                'hosts': self.config.hosts,
                'verify_certs': False,
                'request_timeout': 30,
                'retry_on_timeout': True,
                'max_retries': 3,
                # 强制使用兼容 8 的媒体类型，避免 "compatible-with=9" 报错
                'headers': {
                    'Accept': 'application/vnd.elasticsearch+json; compatible-with=8',
                    'Content-Type': 'application/vnd.elasticsearch+json; compatible-with=8'
                }
            }
            
            if self.config.username and self.config.password:
                client_config['basic_auth'] = (self.config.username, self.config.password)
            
            # 创建ES客户端实例
            self.client = AsyncElasticsearch(**client_config)
            
            # 测试连接
            try:
                await self.client.info()
            except Exception as e:
                if "compatible-with" in str(e) or "media-type" in str(e):
                    logger.warning(f"ES version compatibility issue, service will continue: {e}")
                    # 对于版本兼容性问题，记录警告但继续运行
                elif "unexpected keyword argument" in str(e):
                    logger.warning(f"ES client configuration issue: {e}")
                    # 重新创建客户端，使用更简单的配置
                    simple_config = {
                        'hosts': self.config.hosts,
                        'verify_certs': False,
                        'headers': {
                            'Accept': 'application/vnd.elasticsearch+json; compatible-with=8',
                            'Content-Type': 'application/vnd.elasticsearch+json; compatible-with=8'
                        }
                    }
                    if self.config.username and self.config.password:
                        simple_config['basic_auth'] = (self.config.username, self.config.password)
                    
                    self.client = AsyncElasticsearch(**simple_config)
                    try:
                        await self.client.info()
                        logger.info("ES client recreated with simplified config")
                    except Exception as e2:
                        logger.error(f"ES connection failed after retry: {e2}")
                        raise
                else:
                    logger.error(f"ES connection failed: {e}")
                    raise
            logger.info("Elasticsearch client initialized successfully")
            
            # 创建索引
            await self._create_indices()
            
        except Exception as e:
            logger.error(f"Failed to initialize Elasticsearch client: {e}")
            raise
    
    async def _create_indices(self):
        """创建索引"""
        indices_mappings = {
            self.CHANGAN_STANDARD_OPINIONS_INDEX: self._get_changan_standard_opinions_mapping(),
            self.ENTITY_SYNONYMS_INDEX: self._get_entity_synonyms_mapping(),
            self.DESCRIPTION_SYNONYMS_INDEX: self._get_description_synonyms_mapping(),
            self.BRAND_SERIES_INDEX: self._get_brand_series_mapping(),
            self.NEW_WORDS_INDEX: self._get_new_words_mapping()
        }
        
        for index_name, mapping in indices_mappings.items():
            try:
                exists = await self.client.indices.exists(index=index_name)
                if not exists:
                    await self.client.indices.create(index=index_name, body=mapping)
                    logger.info(f"Created index: {index_name}")
            except Exception as e:
                logger.warning(f"Failed to create index {index_name}: {e}")
                # 继续执行，不阻断服务启动
    
    def _get_changan_standard_opinions_mapping(self) -> Dict[str, Any]:
        """获取长安标准观点库索引映射"""
        return {
            "mappings": {
                "properties": {
                    "normalized_opinion_id": {"type": "keyword"},
                    "standard_id": {"type": "keyword"},
                    "standard_opinion": {"type": "keyword"},
                    "normalized_opinion": {"type": "keyword"},
                    "business_dept": {"type": "keyword"},
                    "category": {"type": "keyword"},
                    "status": {"type": "integer"},
                    "created_time": {"type": "date"},
                    "updated_time": {"type": "date"}
                }
            },
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0
            }
        }



    def _get_entity_synonyms_mapping(self) -> Dict[str, Any]:
        """获取主体近义词库索引映射"""
        return {
            "mappings": {
                "properties": {
                    "entity_id": {"type": "keyword"},
                    "sim_entity": {"type": "keyword"},
                    "vector": {
                        "type": "dense_vector",
                        "dims": 512,
                        "similarity": "cosine"
                    },
                    "normalized_entity": {"type": "keyword"},
                    "standard_opinion_id_list": {"type": "keyword"},
                    "status": {"type": "integer"},
                    "created_time": {"type": "date"},
                    "updated_time": {"type": "date"}
                }
            },
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0
            }
        }
    
    def _get_description_synonyms_mapping(self) -> Dict[str, Any]:
        """获取描述近义词库索引映射"""
        return {
            "mappings": {
                "properties": {
                    "description_id": {"type": "keyword"},
                    "sim_description": {"type": "keyword"},
                    "vector": {
                        "type": "dense_vector",
                        "dims": 512,
                        "similarity": "cosine"
                    },
                    "normalized_description": {"type": "keyword"},
                    "standard_opinion_id": {"type": "keyword"},
                    "status": {"type": "integer"},
                    "created_time": {"type": "date"},
                    "updated_time": {"type": "date"}
                }
            },
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0
            }
        }
    
    def _get_brand_series_mapping(self) -> Dict[str, Any]:
        """获取品牌车系库索引映射"""
        return {
            "mappings": {
                "properties": {
                    "brand": {"type": "keyword"},
                    "series": {"type": "keyword"},
                    "model": {"type": "keyword"},
                    "brand_code": {"type": "keyword"},
                    "series_code": {"type": "keyword"},
                    "model_code": {"type": "keyword"},
                    "synonyms": {"type": "keyword"},
                    "status": {"type": "integer"},
                    "created_time": {"type": "date"},
                    "updated_time": {"type": "date"}
                }
            },
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0
            }
        }
    
    def _get_new_words_mapping(self) -> Dict[str, Any]:
        """获取新词库索引映射"""
        return {
            "mappings": {
                "properties": {
                    "new_entity": {"type": "keyword"},
                    "new_description": {"type": "keyword"},
                    "combined_text": {"type": "keyword"},
                    "vector_embedding": {
                        "type": "dense_vector",
                        "dims": 512,
                        "similarity": "cosine"
                    },
                    "frequency": {"type": "integer"},
                    "source_topic_id": {"type": "keyword"},
                    "review_status": {"type": "integer"},
                    "created_time": {"type": "date"},
                    "updated_time": {"type": "date"}
                }
            },
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0
            }
        }


    
    # 向量搜索相关方法
    async def search_similar_entities(
        self, 
        query_vector: List[float], 
        threshold: float = 0.7, 
        size: int = 5
    ) -> List[VectorSearchResult]:
        """搜索相似主体"""
        query = {
            "query": {
                "bool": {
                    "must": [
                        {
                            "script_score": {
                                "query": {"match_all": {}},
                                "script": {
                                    "source": "cosineSimilarity(params.query_vector, 'vector') + 1.0",
                                    "params": {"query_vector": query_vector}
                                },
                                "min_score": threshold + 1.0
                            }
                        }
                    ],
                    "filter": [
                        {"term": {"status": 1}}
                    ]
                }
            },
            "size": size,
            "_source": ["entity_id", "sim_entity", "normalized_entity", "standard_opinion_id_list"]
        }
        
        try:
            response = await self.client.search(index=self.ENTITY_SYNONYMS_INDEX, body=query)
            logger.info(f"Entity vector search response: {response}")
            results = []
            for hit in response['hits']['hits']:
                results.append(VectorSearchResult(
                    id=hit['_id'],
                    score=hit['_score'] - 1.0,  # 减去偏移量得到真实相似度
                    source=hit['_source']
                ))
            return results
        except Exception as e:
            logger.error(f"Entity vector search failed: {e}")
            return []
    
    async def search_similar_descriptions(
        self, 
        query_vector: List[float], 
        standard_opinion_ids: List[str],
        threshold: float = 0.7, 
        size: int = 5
    ) -> List[VectorSearchResult]:
        """搜索相似描述"""
        query = {
            "query": {
                "bool": {
                    "must": [
                        {
                            "script_score": {
                                "query": {"match_all": {}},
                                "script": {
                                    "source": "cosineSimilarity(params.query_vector, 'vector') + 1.0",
                                    "params": {"query_vector": query_vector}
                                },
                                "min_score": threshold + 1.0
                            }
                        }
                    ],
                    "filter": [
                        {"term": {"status": 1}},
                        {"terms": {"standard_opinion_id": standard_opinion_ids}}
                    ]
                }
            },
            "size": size,
            "_source": ["description_id", "sim_description", "normalized_description", "standard_opinion_id"]
        }
        
        try:
            response = await self.client.search(index=self.DESCRIPTION_SYNONYMS_INDEX, body=query)
            results = []
            for hit in response['hits']['hits']:
                results.append(VectorSearchResult(
                    id=hit['_id'],
                    score=hit['_score'] - 1.0,
                    source=hit['_source']
                ))
            return results
        except Exception as e:
            logger.error(f"Description vector search failed: {e}")
            return []
    
    # 精确匹配相关方法
    async def search_exact_entities(self, entity: str) -> List[VectorSearchResult]:
        """精确搜索主体"""
        query = {
            "query": {
                "bool": {
                    "must": [
                        {"term": {"sim_entity": entity}}
                    ],
                    "filter": [
                        {"term": {"status": 1}}
                    ]
                }
            },
            "_source": ["entity_id", "sim_entity", "normalized_entity", "standard_opinion_id_list"]
        }
        
        try:
            response = await self.client.search(index=self.ENTITY_SYNONYMS_INDEX, body=query)
            results = []
            for hit in response['hits']['hits']:
                results.append(VectorSearchResult(
                    id=hit['_id'],
                    score=1.0,  # 精确匹配置信度为1.0
                    source=hit['_source']
                ))
            return results
        except Exception as e:
            if "compatible-with" in str(e) or "media-type" in str(e):
                logger.warning(f"Entity search skipped due to ES version compatibility: {e}")
            else:
                logger.warning(f"Entity exact search failed (may be index not exist): {e}")
            return []
    
    async def search_exact_descriptions(
        self, 
        standard_opinion_ids: List[str]
    ) -> List[VectorSearchResult]:
        """根据标准观点ID搜索所有相关描述"""
        query = {
            "query": {
                "bool": {
                    "must": [
                        {"match_all": {}}
                    ],
                    "filter": [
                        {"term": {"status": 1}},
                        {"terms": {"standard_opinion_id": standard_opinion_ids}}
                    ]
                }
            },
            "_source": ["description_id", "sim_description", "normalized_description", "standard_opinion_id"]
        }
        
        try:
            response = await self.client.search(index=self.DESCRIPTION_SYNONYMS_INDEX, body=query)
            results = []
            for hit in response['hits']['hits']:
                results.append(VectorSearchResult(
                    id=hit['_id'],
                    score=1.0,  # 所有匹配的置信度为1.0
                    source=hit['_source']
                ))
            return results
        except Exception as e:
            if "compatible-with" in str(e) or "media-type" in str(e):
                logger.warning(f"Description search skipped due to ES version compatibility: {e}")
            else:
                logger.warning(f"Description search by opinion IDs failed (may be index not exist): {e}")
            return []

    # 新词库相关方法
    async def search_similar_new_words(
        self, 
        query_vector: List[float], 
        threshold: float = 0.7, 
        size: int = 5
    ) -> List[VectorSearchResult]:
        """搜索相似新词"""
        query = {
            "query": {
                "bool": {
                    "must": [
                        {
                            "script_score": {
                                "query": {"match_all": {}},
                                "script": {
                                    "source": "cosineSimilarity(params.query_vector, 'vector_embedding') + 1.0",
                                    "params": {"query_vector": query_vector}
                                },
                                "min_score": threshold + 1.0
                            }
                        }
                    ],
                    "filter": [
                        {"term": {"review_status": 0}}  # 只搜索待审核的新词
                    ]
                }
            },
            "size": size,
            "_source": ["new_entity", "new_description", "combined_text", "frequency", "source_topic_id"]
        }
        
        try:
            response = await self.client.search(index=self.NEW_WORDS_INDEX, body=query)
            results = []
            for hit in response['hits']['hits']:
                results.append(VectorSearchResult(
                    id=hit['_id'],
                    score=hit['_score'] - 1.0,  # 减去偏移量得到真实相似度
                    source=hit['_source']
                ))
            return results
        except Exception as e:
            logger.error(f"New words vector search failed: {e}")
            return []
    
    async def add_new_word(self, entity: str, description: str, topic_id: str, failure_reason: str) -> bool:
        """添加新词到新词库"""
        doc = {
            "new_entity": entity,
            "new_description": description,
            "combined_text": f"{entity} {description}",
            "source_topic_id": topic_id,
            "frequency": 1,
            "review_status": 0,
            "created_time": self._get_current_time(),
            "updated_time": self._get_current_time()
        }
        
        try:
            await self.client.index(
                index=self.NEW_WORDS_INDEX,
                body=doc
            )
            return True
        except Exception as e:
            logger.error(f"Failed to add new word: {e}")
            return False
    
    # 品牌车系搜索相关方法
    async def search_brand_series(self, text: str, size: int = 10) -> List[Dict[str, Any]]:
        """搜索品牌车系"""
        query = {
            "query": {
                "bool": {
                    "should": [
                        {"match": {"brand": {"query": text, "boost": 3}}},
                        {"match": {"series": {"query": text, "boost": 2}}},
                        {"match": {"model": {"query": text, "boost": 1}}},
                        {"match": {"synonyms": {"query": text, "boost": 1.5}}}
                    ],
                    "minimum_should_match": 1,
                    "filter": [
                        {"term": {"status": 1}}
                    ]
                }
            },
            "_source": ["brand", "series", "model", "brand_code", "series_code", "model_code"],
            "size": size
        }
        
        try:
            response = await self.client.search(index=self.BRAND_SERIES_INDEX, body=query)
            return [hit['_source'] for hit in response['hits']['hits']]
        except Exception as e:
            logger.error(f"Brand series search failed: {e}")
            return []
    
    # 数据管理方法
    async def index_entity_synonym(
        self, 
        entity_id: str, 
        sim_entity: str, 
        normalized_entity: str,
        vector: List[float],
        standard_opinion_id_list: List[str]
    ):
        """索引主体近义词"""
        doc = {
            "entity_id": entity_id,
            "sim_entity": sim_entity,
            "normalized_entity": normalized_entity,
            "vector": vector,
            "standard_opinion_id_list": standard_opinion_id_list,
            "status": 1,
            "created_time": self._get_current_time(),
            "updated_time": self._get_current_time()
        }
        
        try:
            await self.client.index(
                index=self.ENTITY_SYNONYMS_INDEX,
                id=entity_id,
                body=doc
            )
        except Exception as e:
            logger.error(f"Failed to index entity synonym: {e}")
            raise
    
    async def index_description_synonym(
        self, 
        description_id: str, 
        sim_description: str,
        normalized_description: str,
        vector: List[float],
        standard_opinion_id: str
    ):
        """索引描述近义词"""
        doc = {
            "description_id": description_id,
            "sim_description": sim_description,
            "normalized_description": normalized_description,
            "vector": vector,
            "standard_opinion_id": standard_opinion_id,
            "status": 1,
            "created_time": self._get_current_time(),
            "updated_time": self._get_current_time()
        }
        
        try:
            await self.client.index(
                index=self.DESCRIPTION_SYNONYMS_INDEX,
                id=description_id,
                body=doc
            )
        except Exception as e:
            logger.error(f"Failed to index description synonym: {e}")
            raise
    
    async def index_standard_opinion(
        self,
        standard_opinion_id: str,
        standard_entity: str,
        standard_description: str,
        standard_opinion: str,
        vector: List[float],
        business_tags: List[str] = None,
        category: str = None
    ):
        """索引长安标准观点"""
        doc = {
            "normalized_opinion_id": standard_opinion_id,
            "standard_id": standard_opinion_id,
            "standard_opinion": standard_opinion,
            "normalized_opinion": standard_opinion,
            "business_dept": business_tags or [],
            "category": category,
            "status": 1,
            "created_time": self._get_current_time(),
            "updated_time": self._get_current_time()
        }
        
        try:
            await self.client.index(
                index=self.CHANGAN_STANDARD_OPINIONS_INDEX,
                id=standard_opinion_id,
                body=doc
            )
        except Exception as e:
            logger.error(f"Failed to index standard opinion: {e}")
            raise



    async def index_brand_series(
        self,
        brand_id: str,
        brand: str,
        series: str,
        model: str = None,
        synonyms: List[str] = None
    ):
        """索引品牌车系"""
        doc = {
            "brand": brand,
            "series": series,
            "model": model,
            "brand_code": brand_id,
            "series_code": f"{brand_id}_{series}",
            "model_code": f"{brand_id}_{series}_{model}" if model else f"{brand_id}_{series}",
            "synonyms": " ".join(synonyms) if synonyms else "",
            "status": 1,
            "created_time": self._get_current_time(),
            "updated_time": self._get_current_time()
        }
        
        try:
            await self.client.index(
                index=self.BRAND_SERIES_INDEX,
                id=brand_id,
                body=doc
            )
        except Exception as e:
            logger.error(f"Failed to index brand series: {e}")
            raise

    async def get_standard_opinion(self, standard_opinion_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取标准观点信息"""
        query = {
            "query": {
                "bool": {
                    "must": [
                        {"term": {"standard_id": standard_opinion_id}}
                    ],
                    "filter": [
                        {"term": {"status": 1}}
                    ]
                }
            },
            "_source": ["normalized_opinion_id", "standard_opinion", "normalized_opinion", "business_dept", "category"]
        }
        
        try:
            response = await self.client.search(index=self.CHANGAN_STANDARD_OPINIONS_INDEX, body=query)
            logger.info(f"response: {response}")
            hits = response['hits']['hits']
            if hits:
                return hits[0]['_source']
            return None
        except Exception as e:
            logger.error(f"Failed to get standard opinion {standard_opinion_id}: {e}")
            return None

    async def refresh_indices(self):
        """刷新所有索引"""
        try:
            await self.client.indices.refresh(index="_all")
        except Exception as e:
            logger.error(f"Failed to refresh indices: {e}")
    
    async def close(self):
        """关闭客户端"""
        if self.client:
            await self.client.close()
            logger.info("Elasticsearch client closed")

    async def search_entities_in_text(self, text: str) -> List[VectorSearchResult]:
        """在文本中搜索匹配的实体"""
        try:
            results = []
            
            # 1. 直接匹配整个文本
            query = {
                "query": {
                    "bool": {
                        "should": [
                            {
                                "match": {
                                    "sim_entity": {
                                        "query": text,
                                        "operator": "and"
                                    }
                                }
                            },
                            {
                                "match_phrase": {
                                    "sim_entity": text
                                }
                            }
                        ],
                        "minimum_should_match": 1,
                        "filter": [
                            {"term": {"status": 1}}
                        ]
                    }
                },
                "_source": ["entity_id", "sim_entity", "normalized_entity", "standard_opinion_id_list"],
                "size": 10
            }
            
            response = await self.client.search(index=self.ENTITY_SYNONYMS_INDEX, body=query)
            for hit in response['hits']['hits']:
                results.append(VectorSearchResult(
                    id=hit['_id'],
                    score=hit['_score'],
                    source=hit['_source']
                ))
            
            # 2. 子串匹配 - 移除提前停止的逻辑
            for length in [4, 3, 2]:
                for i in range(len(text) - length + 1):
                    substring = text[i:i + length].strip()
                    if len(substring) >= 2:
                        sub_query = {
                            "query": {
                                "bool": {
                                    "should": [
                                        {
                                            "match": {
                                                "sim_entity": {
                                                    "query": substring,
                                                    "operator": "and"
                                                }
                                            }
                                        },
                                        {
                                            "match_phrase": {
                                                "sim_entity": substring
                                            }
                                        }
                                    ],
                                    "minimum_should_match": 1,
                                    "filter": [
                                        {"term": {"status": 1}}
                                    ]
                                }
                            },
                            "_source": ["entity_id", "sim_entity", "normalized_entity", "standard_opinion_id_list"],
                            "size": 10
                        }
                        
                        sub_response = await self.client.search(index=self.ENTITY_SYNONYMS_INDEX, body=sub_query)
                        for hit in sub_response['hits']['hits']:
                            # 避免重复
                            if not any(r.id == hit['_id'] for r in results):
                                results.append(VectorSearchResult(
                                    id=hit['_id'],
                                    score=hit['_score'],
                                    source=hit['_source']
                                ))
            
            # 3. 按分数排序并限制结果数量
            results.sort(key=lambda x: x.score, reverse=True)
            return results[:20]  # 返回最多20个结果
            
        except Exception as e:
            logger.error(f"Entity text search failed: {e}")
            return []

    async def batch_vector_search(
        self, 
        texts: List[str], 
        index_name: str,
        vector_field: str = "vector",
        size: int = 1,
        similarity_threshold: float = 0.7
    ) -> List[List[Dict[str, Any]]]:
        """批量向量搜索"""
        try:
            if not texts:
                return []
            
            # 首先需要获取所有文本的向量表示
            # 这里假设有一个向量化服务可以批量处理
            vectors = await self._get_batch_vectors(texts)
            
            if not vectors or len(vectors) != len(texts):
                logger.error(f"Vector generation failed or count mismatch: {len(vectors)} vs {len(texts)}")
                return [[] for _ in texts]
            
            # 使用 ES 的 msearch API 进行批量搜索
            search_body = []
            
            for i, (text, vector) in enumerate(zip(texts, vectors)):
                # 添加搜索头
                search_body.append({"index": index_name})
                
                # 添加搜索查询
                query = {
                    "query": {
                        "bool": {
                            "must": [
                                {
                                    "script_score": {
                                        "query": {"match_all": {}},
                                        "script": {
                                            "source": f"cosineSimilarity(params.query_vector, '{vector_field}') + 1.0",
                                            "params": {"query_vector": vector}
                                        },
                                        "min_score": similarity_threshold + 1.0
                                    }
                                }
                            ],
                            "filter": [
                                {"term": {"status": 1}}
                            ]
                        }
                    },
                    "size": size,
                    "_source": True
                }
                search_body.append(query)
            
            # 执行批量搜索
            response = await self.client.msearch(body=search_body)
            
            # 处理批量搜索结果
            results = []
            for i, search_result in enumerate(response['responses']):
                if 'error' in search_result:
                    logger.error(f"Search error for text {i}: {search_result['error']}")
                    results.append([])
                    continue
                
                text_results = []
                for hit in search_result['hits']['hits']:
                    # 根据索引类型获取正确的字段
                    if index_name == self.ENTITY_SYNONYMS_INDEX:
                        entity_id = hit['_source'].get('entity_id', hit['_id'])
                        text = hit['_source'].get('normalized_entity', hit['_source'].get('sim_entity', ''))
                        result_data = {
                            'id': entity_id,
                            'score': hit['_score'] - 1.0,
                            'text': text,
                            'source': hit['_source']
                        }
                    elif index_name == self.DESCRIPTION_SYNONYMS_INDEX:
                        entity_id = hit['_source'].get('description_id', hit['_id'])
                        text = hit['_source'].get('normalized_description', hit['_source'].get('sim_description', ''))
                        standard_opinion_id = hit['_source'].get('standard_opinion_id', '')
                        result_data = {
                            'id': entity_id,
                            'score': hit['_score'] - 1.0,
                            'text': text,
                            'standard_opinion_id': standard_opinion_id,
                            'source': hit['_source']
                        }
                    else:
                        entity_id = hit['_id']
                        text = hit['_source'].get('sim_entity') or hit['_source'].get('sim_description', '')
                        result_data = {
                            'id': entity_id,
                            'score': hit['_score'] - 1.0,
                            'text': text,
                            'source': hit['_source']
                        }
                    
                    text_results.append(result_data)
                results.append(text_results)
            
            logger.info(f"Batch vector search completed: {len(texts)} texts, {sum(len(r) for r in results)} total matches")
            return results
            
        except Exception as e:
            logger.error(f"Batch vector search failed: {e}")
            return [[] for _ in texts]

    async def batch_search_entities(self, entities: List[str]) -> List[List[Dict[str, Any]]]:
        """批量搜索实体"""
        return await self.batch_vector_search(
            texts=entities,
            index_name=self.ENTITY_SYNONYMS_INDEX,
            vector_field="vector"
        )

    async def batch_search_descriptions(self, descriptions: List[str]) -> List[List[Dict[str, Any]]]:
        """批量搜索描述"""
        return await self.batch_vector_search(
            texts=descriptions,
            index_name=self.DESCRIPTION_SYNONYMS_INDEX,
            vector_field="vector"
        )

    async def batch_search_descriptions_with_constraints(
        self, 
        description_constraints: List[Dict[str, Any]]
    ) -> List[List[Dict[str, Any]]]:
        """基于约束条件批量搜索描述"""
        try:
            if not description_constraints:
                return []
            
            # 提取所有描述文本进行向量化
            descriptions = [constraint['description'] for constraint in description_constraints]
            vectors = await self._get_batch_vectors(descriptions)
            
            if not vectors or len(vectors) != len(descriptions):
                logger.error(f"Vector generation failed or count mismatch: {len(vectors)} vs {len(descriptions)}")
                return [[] for _ in descriptions]
            
            # 使用 ES 的 msearch API 进行批量搜索
            search_body = []
            
            for i, (constraint, vector) in enumerate(zip(description_constraints, vectors)):
                # 添加搜索头
                search_body.append({"index": self.DESCRIPTION_SYNONYMS_INDEX})
                
                # 构建约束条件
                standard_opinion_ids = constraint.get('standard_opinion_ids', [])
                
                # 构建查询
                query = {
                    "query": {
                        "bool": {
                            "must": [
                                {
                                    "script_score": {
                                        "query": {"match_all": {}},
                                        "script": {
                                            "source": "cosineSimilarity(params.query_vector, 'vector') + 1.0",
                                            "params": {"query_vector": vector}
                                        },
                                        "min_score": 1.7  # 0.7 + 1.0
                                    }
                                }
                            ],
                            "filter": [
                                {"term": {"status": 1}}
                            ]
                        }
                    },
                    "size": 1,
                    "_source": True
                }
                
                # 如果有标准观点ID约束，添加过滤条件
                if standard_opinion_ids:
                    query["query"]["bool"]["filter"].append({
                        "terms": {"standard_opinion_id": standard_opinion_ids}
                    })
                
                search_body.append(query)
            
            # 执行批量搜索
            response = await self.client.msearch(body=search_body)
            
            # 处理批量搜索结果
            results = []
            for i, search_result in enumerate(response['responses']):
                if 'error' in search_result:
                    logger.error(f"Search error for description {i}: {search_result['error']}")
                    results.append([])
                    continue
                
                text_results = []
                for hit in search_result['hits']['hits']:
                    entity_id = hit['_source'].get('description_id', hit['_id'])
                    text = hit['_source'].get('normalized_description', hit['_source'].get('sim_description', ''))
                    standard_opinion_id = hit['_source'].get('standard_opinion_id', '')
                    
                    text_results.append({
                        'id': entity_id,
                        'score': hit['_score'] - 1.0,  # 减去偏移量得到真实相似度
                        'text': text,
                        'standard_opinion_id': standard_opinion_id,
                        'source': hit['_source']
                    })
                results.append(text_results)
            
            logger.info(f"Batch constrained description search completed: {len(descriptions)} descriptions, {sum(len(r) for r in results)} total matches")
            return results
            
        except Exception as e:
            logger.error(f"Batch constrained description search failed: {e}")
            return [[] for _ in description_constraints]

    async def _get_batch_vectors(self, texts: List[str]) -> List[List[float]]:
        """获取文本的批量向量表示"""
        try:
            # 这里需要调用向量化服务
            # 支持两种API格式：旧的自定义API和新的OpenAI兼容API
            import httpx
            
            # 优先使用OpenAI兼容API
            openai_base_url = self.vector_config.get('openai_base_url')
            openai_model = self.vector_config.get('openai_model', 'bge-small-zh-v1')
            
            if openai_base_url:
                # 使用OpenAI兼容API
                api_url = f"{openai_base_url.rstrip('/')}/embeddings"
                request_data = {
                    "input": texts,
                    "model": openai_model
                }
                logger.info(f"Using OpenAI-compatible API: {api_url} with model {openai_model} for {len(texts)} texts")
            else:
                # 回退到旧的自定义API
                vector_api_url = self.vector_config.get('api_url', 'http://*************:7777/bge-small')
                api_url = f"{vector_api_url}/embeddings"
                request_data = {"texts": texts}
                logger.info(f"Using legacy API: {api_url} for {len(texts)} texts")
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    api_url,
                    json=request_data,
                    timeout=30.0
                )
                
                logger.info(f"Vector API response status: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    
                    # 处理不同API格式的响应
                    if openai_base_url:
                        # OpenAI兼容API格式：{"data": [{"embedding": [...]}, ...]}
                        if 'data' in result:
                            embeddings = [item['embedding'] for item in result['data']]
                        else:
                            logger.error(f"Unexpected OpenAI API response format: {result}")
                            return []
                    else:
                        # 旧API格式：{"embeddings": [[...], ...]}
                        embeddings = result.get('embeddings', [])
                    
                    logger.info(f"Successfully got {len(embeddings)} embeddings for {len(texts)} texts")
                    return embeddings
                else:
                    logger.error(f"Vector API returned {response.status_code}, response: {response.text}")
                    return []
                    
        except httpx.TimeoutException as e:
            logger.error(f"Vector API timeout: {e}")
            return []
        except httpx.ConnectError as e:
            logger.error(f"Vector API connection error: {e}")
            return []
        except httpx.HTTPStatusError as e:
            logger.error(f"Vector API HTTP error: {e}")
            return []
        except Exception as e:
            logger.error(f"Batch vector generation failed with unexpected error: {type(e).__name__}: {e}")
            return []