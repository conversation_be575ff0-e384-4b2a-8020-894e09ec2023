"""
StarRocks数据库客户端
"""
import asyncio
import logging
from typing import List, Dict, Any, Optional, Tuple
from contextlib import asynccontextmanager
import pymysql
from pymysql.cursors import DictCursor
from shared.models.schemas import (
    BrandSeries, StandardOpinion, NewWord, ErrorRecord, DatabaseConfig
)

logger = logging.getLogger(__name__)


class StarRocksClient:
    """StarRocks数据库客户端"""
    
    def __init__(self, config: DatabaseConfig):
        self.config = config
        self._connection_pool = None
        
    async def initialize(self):
        """初始化连接池"""
        try:
            # StarRocks兼容MySQL协议，使用pymysql连接
            self.connection_config = {
                'host': self.config.host,
                'port': self.config.port,
                'user': self.config.username,
                'password': self.config.password,
                'database': self.config.database,
                'charset': 'utf8mb4',
                'autocommit': True,
                'cursorclass': DictCursor
            }
            logger.info("StarRocks client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize StarRocks client: {e}")
            raise
    
    @asynccontextmanager
    async def get_connection(self):
        """获取数据库连接的上下文管理器"""
        connection = None
        try:
            connection = pymysql.connect(**self.connection_config)
            yield connection
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            if connection:
                connection.rollback()
            raise
        finally:
            if connection:
                connection.close()
    
    async def execute_query(self, sql: str, params: Optional[Tuple] = None) -> List[Dict[str, Any]]:
        """执行查询SQL"""
        async with self.get_connection() as conn:
            cursor = conn.cursor()
            try:
                await asyncio.get_event_loop().run_in_executor(
                    None, cursor.execute, sql, params
                )
                result = await asyncio.get_event_loop().run_in_executor(
                    None, cursor.fetchall
                )
                return result
            finally:
                cursor.close()
    
    async def execute_insert(self, sql: str, params: Optional[Tuple] = None) -> int:
        """执行插入SQL，返回插入ID"""
        async with self.get_connection() as conn:
            cursor = conn.cursor()
            try:
                await asyncio.get_event_loop().run_in_executor(
                    None, cursor.execute, sql, params
                )
                return cursor.lastrowid
            finally:
                cursor.close()
    
    async def execute_update(self, sql: str, params: Optional[Tuple] = None) -> int:
        """执行更新SQL，返回影响行数"""
        async with self.get_connection() as conn:
            cursor = conn.cursor()
            try:
                result = await asyncio.get_event_loop().run_in_executor(
                    None, cursor.execute, sql, params
                )
                return result
            finally:
                cursor.close()
    
    # 品牌车系相关操作
    async def search_brand_series(self, text: str, limit: int = 10) -> List[BrandSeries]:
        """搜索品牌车系"""
        sql = """
        SELECT * FROM brand_series_model 
        WHERE (brand LIKE %s OR series LIKE %s OR model LIKE %s OR synonyms LIKE %s)
        AND is_active = 1
        LIMIT %s
        """
        pattern = f"%{text}%"
        params = (pattern, pattern, pattern, pattern, limit)
        
        results = await self.execute_query(sql, params)
        return [BrandSeries(**row) for row in results]
    
    async def count_brand_series_matches(self, text: str) -> int:
        """统计品牌车系匹配数量"""
        sql = """
        SELECT COUNT(*) as count FROM brand_series_model 
        WHERE (brand LIKE %s OR series LIKE %s OR model LIKE %s OR synonyms LIKE %s)
        AND is_active = 1
        """
        pattern = f"%{text}%"
        params = (pattern, pattern, pattern, pattern)
        
        result = await self.execute_query(sql, params)
        return result[0]['count'] if result else 0
    
    # 标准观点相关操作
    async def get_standard_opinion(self, opinion_id) -> Optional[StandardOpinion]:
        """根据ID获取标准观点"""
        # 支持字符串和整数类型的opinion_id
        try:
            opinion_id = int(opinion_id) if isinstance(opinion_id, str) else opinion_id
        except (ValueError, TypeError):
            logger.warning(f"Invalid opinion_id format: {opinion_id}")
            return None
            
        sql = "SELECT * FROM changan_standard_opinions WHERE standard_opinion_id = %s AND is_active = 1"
        results = await self.execute_query(sql, (opinion_id,))
        return StandardOpinion(**results[0]) if results else None
    
    async def get_standard_opinions_by_ids(self, opinion_ids: List) -> List[StandardOpinion]:
        """根据ID列表获取标准观点"""
        if not opinion_ids:
            return []
        
        # 转换为整数类型
        try:
            int_opinion_ids = [int(id) if isinstance(id, str) else id for id in opinion_ids]
        except (ValueError, TypeError) as e:
            logger.warning(f"Invalid opinion_ids format: {opinion_ids}, error: {e}")
            return []
        
        placeholders = ','.join(['%s'] * len(int_opinion_ids))
        sql = f"""
        SELECT * FROM changan_standard_opinions 
        WHERE standard_opinion_id IN ({placeholders}) AND is_active = 1
        """
        results = await self.execute_query(sql, tuple(int_opinion_ids))
        return [StandardOpinion(**row) for row in results]
    
    # 注意：主体和描述近义词数据已迁移到Elasticsearch
    # 以下方法已废弃，数据现在存储在ES的entity_vectors和description_vectors索引中
    # 这些方法保留是为了兼容性，但会返回空结果或抛出警告
    
    # 新词相关操作 - 暂时保留，但实际表可能不存在
    async def insert_new_word(self, entity: str, description: str) -> int:
        """插入新词 - 注意：new_words表可能不存在"""
        try:
            # 检查是否已存在
            check_sql = "SELECT id FROM new_words WHERE new_entity = %s AND new_description = %s"
            existing = await self.execute_query(check_sql, (entity, description))
            
            if existing:
                # 更新频次
                update_sql = "UPDATE new_words SET frequency = frequency + 1 WHERE id = %s"
                await self.execute_update(update_sql, (existing[0]['id'],))
                return existing[0]['id']
            else:
                # 插入新记录
                insert_sql = """
                INSERT INTO new_words (new_entity, new_description, frequency, status) 
                VALUES (%s, %s, 1, 'pending')
                """
                return await self.execute_insert(insert_sql, (entity, description))
        except Exception as e:
            # 如果表不存在，则记录日志但不抛出异常
            logger.warning(f"新词插入失败（可能表不存在）: {e}")
            return 0
    
    # 错误记录相关操作
    async def insert_error_record(self, error_record: ErrorRecord) -> int:
        """插入错误记录"""
        sql = """
        INSERT INTO error_records 
        (topic_id, topic_text, retry_count, error_type, error_msg, last_ts, final_status)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        """
        params = (
            error_record.topic_id,
            error_record.topic_text,
            error_record.retry_count,
            error_record.error_type,
            error_record.error_msg,
            error_record.last_ts,
            error_record.final_status
        )
        return await self.execute_insert(sql, params)
    
    async def get_error_record_by_topic_id(self, topic_id: str) -> Optional[ErrorRecord]:
        """根据topic_id获取错误记录"""
        sql = "SELECT * FROM error_records WHERE topic_id = %s ORDER BY created_at DESC LIMIT 1"
        results = await self.execute_query(sql, (topic_id,))
        return ErrorRecord(**results[0]) if results else None
    
    # 否定词相关操作
    async def get_negation_words(self) -> List[str]:
        """获取否定词列表"""
        sql = "SELECT word FROM negation_words WHERE is_active = 1"
        results = await self.execute_query(sql)
        return [row['word'] for row in results]
    
    # 问卷相关操作
    async def get_standard_opinion_id_by_content(self, content: str) -> Optional[str]:
        """根据content查询opinion_synonym表，获取standard_opinion_id
        
        根据字段映射表：
        - content对应opinion字段（六级非规范观点）
        - 查询standard_opinion_id字段（五级标准观点ID）
        """
        try:
            # 根据字段映射表，使用opinion字段查询standard_opinion_id
            sql = "SELECT standard_opinion_id FROM opinion_synonym WHERE opinion = %s LIMIT 1"
            results = await self.execute_query(sql, (content,))
            return results[0]['standard_opinion_id'] if results else None
        except Exception as e:
            logger.error(f"查询opinion_synonym表失败: {e}")
            return None
    
    async def get_opinion_synonym(
        self,
        subject_text: str,
        description_text: str,
        standard_opinion_id: Any
    ) -> Optional[Dict[str, Any]]:
        """从 opinion_synonym 查询观点映射
        
        按 subject_text、description_text、standard_opinion_id 精确查询，
        返回 opinion_id、opinion、standard_opinion。
        """
        sql = (
            "SELECT opinion_id, opinion, standard_opinion "
            "FROM opinion_synonym "
            "WHERE standard_opinion_id = %s AND entity = %s AND description = %s "
            "LIMIT 1"
        )
        rows = await self.execute_query(sql, (standard_opinion_id, subject_text, description_text))
        return rows[0] if rows else None
    
    async def insert_new_word_record(
        self,
        *,
        original_text: str,
        segment: str,
        new_entity: str,
        new_description: str,
        recommended_label: Optional[str] = None,
        status_flag: int = 0
    ) -> int:
        """向 new_words 表插入一条新词记录。
        
        字段映射：
        - original_text: 原文
        - segment: 片段
        - new_entity: 新观点主体
        - new_description: 新观点描述
        - new_opinion: 新观点（主体+描述）
        - recommended_label: 推荐上级标签-五级标准观点
        - status_flag: 处理状态位
        """
        try:
            new_opinion = f"{new_entity}{new_description}" if (new_entity or new_description) else ""
            sql = (
                "INSERT INTO new_words (original_text, segment, new_entity, new_description, new_opinion, recommended_label, status_flag, created_time, updated_time) "
                "VALUES (%s, %s, %s, %s, %s, %s, %s, NOW(), NOW())"
            )
            params = (
                original_text,
                segment,
                new_entity,
                new_description,
                new_opinion,
                recommended_label or "",
                status_flag,
            )
            return await self.execute_insert(sql, params)
        except Exception as e:
            logger.error(f"插入新词记录失败: {e}")
            return 0
    
    async def close(self):
        """关闭连接"""
        # pymysql连接在上下文管理器中自动关闭
        logger.info("StarRocks client closed")

    # ================== Business Query Helpers (moved from services) ==================
    async def fetch_secondary_sentiment_rules(self, primary_sentiment: str, rule_type: str = '关键词') -> List[Dict[str, Any]]:
        """按一级情感与规则类型查询二级情感规则。
        对应表：sentiment_rule_keywords(primary_sentiment, secondary_sentiment, rule_keywords, rule_type)
        rule_type 常用值：'关键词' | '分值'
        """
        sql = (
            "SELECT primary_sentiment, secondary_sentiment, rule_keywords, rule_type "
            "FROM sentiment_rule_keywords WHERE primary_sentiment = %s AND rule_type = %s"
        )
        return await self.execute_query(sql, (primary_sentiment, rule_type))

    async def fetch_all_usage_scenario_rules(self) -> List[Dict[str, Any]]:
        """读取全部用车场景规则。
        对应表：scenario_rule_keywords(primary_scenario, secondary_scenario, rule_keywords)
        """
        sql = (
            "SELECT primary_scenario, secondary_scenario, rule_keywords "
            "FROM scenario_rule_keywords"
        )
        return await self.execute_query(sql)

    async def fetch_all_secondary_sentiment_rules_by_type(self, rule_type: str) -> List[Dict[str, Any]]:
        """按规则类型一次性读取所有二级情感规则（跨一级情感）。"""
        sql = (
            "SELECT primary_sentiment, secondary_sentiment, rule_keywords, rule_type "
            "FROM sentiment_rule_keywords WHERE rule_type = %s"
        )
        return await self.execute_query(sql, (rule_type,))

    async def query_scenario_mapping(self, description: str, aspect: str, segment: str) -> Optional[Dict[str, Any]]:
        """按描述/方面/片段模糊匹配用车场景映射（若表存在该结构）。"""
        sql = (
            """
            SELECT usage_scenario, confidence 
            FROM scenario_rule_keywords 
            WHERE (description LIKE %s OR aspect LIKE %s OR segment LIKE %s)
            AND is_active = 1
            ORDER BY confidence DESC
            LIMIT 1
            """
        )
        description_pattern = f"%{description}%"
        aspect_pattern = f"%{aspect}%"
        segment_pattern = f"%{segment}%"
        rows = await self.execute_query(sql, (description_pattern, aspect_pattern, segment_pattern))
        return rows[0] if rows else None