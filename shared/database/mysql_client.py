"""
MySQL数据库客户端
基于StarRocks客户端转换，适配MySQL特性和语法
"""
import asyncio
import logging
from typing import List, Dict, Any, Optional, Tuple
from contextlib import asynccontextmanager
import pymysql
from pymysql.cursors import DictCursor
from shared.models.schemas import (
    BrandSeries, StandardOpinion, NewWord, ErrorRecord, DatabaseConfig
)

logger = logging.getLogger(__name__)


class MySQLClient:
    """MySQL数据库客户端"""
    
    def __init__(self, config: DatabaseConfig):
        self.config = config
        self._connection_pool = None
        
    async def initialize(self):
        """初始化连接池"""
        try:
            # MySQL连接配置
            self.connection_config = {
                'host': self.config.host,
                'port': self.config.port,
                'user': self.config.username,
                'password': self.config.password,
                'database': self.config.database,
                'charset': 'utf8mb4',
                'autocommit': True,
                'cursorclass': DictCursor
            }
            logger.info("MySQL client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize MySQL client: {e}")
            raise
    
    @asynccontextmanager
    async def get_connection(self):
        """获取数据库连接的上下文管理器"""
        connection = None
        try:
            connection = pymysql.connect(**self.connection_config)
            yield connection
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            if connection:
                connection.rollback()
            raise
        finally:
            if connection:
                connection.close()
    
    async def execute_query(self, sql: str, params: Optional[Tuple] = None) -> List[Dict[str, Any]]:
        """执行查询SQL"""
        async with self.get_connection() as conn:
            cursor = conn.cursor()
            try:
                await asyncio.get_event_loop().run_in_executor(
                    None, cursor.execute, sql, params
                )
                result = await asyncio.get_event_loop().run_in_executor(
                    None, cursor.fetchall
                )
                return result
            finally:
                cursor.close()
    
    async def execute_insert(self, sql: str, params: Optional[Tuple] = None) -> int:
        """执行插入SQL，返回插入ID"""
        async with self.get_connection() as conn:
            cursor = conn.cursor()
            try:
                await asyncio.get_event_loop().run_in_executor(
                    None, cursor.execute, sql, params
                )
                return cursor.lastrowid
            finally:
                cursor.close()
    
    async def execute_update(self, sql: str, params: Optional[Tuple] = None) -> int:
        """执行更新SQL，返回影响行数"""
        async with self.get_connection() as conn:
            cursor = conn.cursor()
            try:
                result = await asyncio.get_event_loop().run_in_executor(
                    None, cursor.execute, sql, params
                )
                return result
            finally:
                cursor.close()
    
    async def execute_batch_insert(self, sql: str, params_list: List[Tuple]) -> int:
        """批量插入数据"""
        async with self.get_connection() as conn:
            cursor = conn.cursor()
            try:
                affected_rows = await asyncio.get_event_loop().run_in_executor(
                    None, cursor.executemany, sql, params_list
                )
                return affected_rows
            finally:
                cursor.close()
    
    # 品牌车系相关操作
    async def search_brand_series(self, text: str, limit: int = 10) -> List[BrandSeries]:
        """搜索品牌车系"""
        sql = """
        SELECT * FROM brand_series_model 
        WHERE (brand LIKE %s OR series LIKE %s OR model LIKE %s OR synonyms LIKE %s)
        AND status = 1
        LIMIT %s
        """
        pattern = f"%{text}%"
        params = (pattern, pattern, pattern, pattern, limit)
        
        results = await self.execute_query(sql, params)
        return [BrandSeries(**row) for row in results]
    
    async def count_brand_series_matches(self, text: str) -> int:
        """统计品牌车系匹配数量"""
        sql = """
        SELECT COUNT(*) as count FROM brand_series_model 
        WHERE (brand LIKE %s OR series LIKE %s OR model LIKE %s OR synonyms LIKE %s)
        AND status = 1
        """
        pattern = f"%{text}%"
        params = (pattern, pattern, pattern, pattern)
        
        result = await self.execute_query(sql, params)
        return result[0]['count'] if result else 0
    
    # 标准观点相关操作
    async def get_standard_opinion(self, opinion_id) -> Optional[StandardOpinion]:
        """根据ID获取标准观点"""
        # 支持字符串和整数类型的opinion_id
        try:
            opinion_id = int(opinion_id) if isinstance(opinion_id, str) else opinion_id
        except (ValueError, TypeError):
            logger.warning(f"Invalid opinion_id format: {opinion_id}")
            return None
            
        sql = "SELECT * FROM opinion_synonym WHERE standard_opinion_id = %s AND status = 1"
        results = await self.execute_query(sql, (opinion_id,))
        return StandardOpinion(**results[0]) if results else None
    
    async def get_standard_opinions_by_ids(self, opinion_ids: List) -> List[StandardOpinion]:
        """根据ID列表获取标准观点"""
        if not opinion_ids:
            return []
        
        # 转换为整数类型
        try:
            int_opinion_ids = [int(id) if isinstance(id, str) else id for id in opinion_ids]
        except (ValueError, TypeError) as e:
            logger.warning(f"Invalid opinion_ids format: {opinion_ids}, error: {e}")
            return []
        
        placeholders = ','.join(['%s'] * len(int_opinion_ids))
        sql = f"""
        SELECT * FROM opinion_synonym 
        WHERE standard_opinion_id IN ({placeholders}) AND status = 1
        """
        results = await self.execute_query(sql, tuple(int_opinion_ids))
        return [StandardOpinion(**row) for row in results]
    
    # 观点近义词相关操作
    async def get_opinion_synonym_by_entity_description(
        self, 
        entity: str, 
        description: str, 
        standard_opinion_id: str
    ) -> Optional[Dict[str, Any]]:
        """根据主体、描述和标准观点ID查询观点近义词
        
        使用联合索引优化查询性能
        """
        sql = """
        SELECT opinion_id, opinion, standard_opinion, entity, description
        FROM opinion_synonym 
        WHERE standard_opinion_id = %s AND entity = %s AND description = %s
        AND status = 1
        LIMIT 1
        """
        results = await self.execute_query(sql, (standard_opinion_id, entity, description))
        return results[0] if results else None
    
    async def search_opinion_synonym_by_entity(self, entity: str, limit: int = 10) -> List[Dict[str, Any]]:
        """根据主体搜索观点近义词"""
        sql = """
        SELECT opinion_id, opinion, standard_opinion_id, standard_opinion, entity, description
        FROM opinion_synonym 
        WHERE entity LIKE %s AND status = 1
        LIMIT %s
        """
        pattern = f"%{entity}%"
        results = await self.execute_query(sql, (pattern, limit))
        return results
    
    async def search_opinion_synonym_by_standard_opinion_id(
        self, 
        standard_opinion_id: str, 
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """根据标准观点ID搜索观点近义词"""
        sql = """
        SELECT opinion_id, opinion, standard_opinion_id, standard_opinion, entity, description
        FROM opinion_synonym 
        WHERE standard_opinion_id = %s AND status = 1
        LIMIT %s
        """
        results = await self.execute_query(sql, (standard_opinion_id, limit))
        return results
    
    # 新词相关操作
    async def insert_new_word(self, entity: str, description: str) -> int:
        """插入新词"""
        try:
            # 检查是否已存在
            check_sql = "SELECT id FROM new_words WHERE new_entity = %s AND new_description = %s"
            existing = await self.execute_query(check_sql, (entity, description))
            
            if existing:
                # 更新状态计数/标志
                update_sql = "UPDATE new_words SET updated_time = NOW() WHERE id = %s"
                await self.execute_update(update_sql, (existing[0]['id'],))
                return existing[0]['id']
            else:
                # 插入新记录
                new_opinion = f"{entity}{description}" if (entity or description) else ""
                insert_sql = """
                INSERT INTO new_words (new_entity, new_description, new_opinion, status, created_time, updated_time) 
                VALUES (%s, %s, %s, '1', NOW(), NOW())
                """
                return await self.execute_insert(insert_sql, (entity, description, new_opinion))
        except Exception as e:
            logger.error(f"新词插入失败: {e}")
            return 0
    
    async def get_new_words_by_status(self, status: int = 0, limit: int = 100) -> List[Dict[str, Any]]:
        """根据状态获取新词列表"""
        sql = """
        SELECT * FROM new_words 
        WHERE status = %s 
        ORDER BY created_time DESC 
        LIMIT %s
        """
        results = await self.execute_query(sql, (str(status), limit))
        return results
    
    async def update_new_word_status(self, word_id: int, status: int, recommended_label: str = None) -> bool:
        """更新新词状态"""
        try:
            if recommended_label:
                sql = """
                UPDATE new_words 
                SET status = %s, recommended_label = %s, updated_time = NOW() 
                WHERE id = %s
                """
                await self.execute_update(sql, (str(status), recommended_label, word_id))
            else:
                sql = """
                UPDATE new_words 
                SET status = %s, updated_time = NOW() 
                WHERE id = %s
                """
                await self.execute_update(sql, (str(status), word_id))
            return True
        except Exception as e:
            logger.error(f"更新新词状态失败: {e}")
            return False
    
    # 错误记录相关操作
    async def insert_error_record(self, error_record: ErrorRecord) -> int:
        """插入错误记录"""
        sql = """
        INSERT INTO error_records 
        (topic_id, topic_text, retry_count, error_type, error_msg, last_ts, final_status, created_time)
        VALUES (%s, %s, %s, %s, %s, %s, %s, NOW())
        """
        params = (
            error_record.topic_id,
            error_record.topic_text,
            error_record.retry_count,
            error_record.error_type,
            error_record.error_msg,
            error_record.last_ts,
            error_record.final_status
        )
        return await self.execute_insert(sql, params)
    
    async def get_error_record_by_topic_id(self, topic_id: str) -> Optional[ErrorRecord]:
        """根据topic_id获取错误记录"""
        sql = "SELECT * FROM error_records WHERE topic_id = %s AND status = 1 ORDER BY created_time DESC LIMIT 1"
        results = await self.execute_query(sql, (topic_id,))
        return ErrorRecord(**results[0]) if results else None
    
    async def get_error_records_by_status(self, final_status: str, limit: int = 100) -> List[Dict[str, Any]]:
        """根据状态获取错误记录列表"""
        sql = """
        SELECT * FROM error_records 
        WHERE final_status = %s AND status = 1
        ORDER BY created_time DESC 
        LIMIT %s
        """
        results = await self.execute_query(sql, (final_status, limit))
        return results
    
    async def update_error_record_status(self, topic_id: str, final_status: str, retry_count: int = None) -> bool:
        """更新错误记录状态"""
        try:
            if retry_count is not None:
                sql = """
                UPDATE error_records 
                SET final_status = %s, retry_count = %s, last_ts = NOW() 
                WHERE topic_id = %s
                """
                await self.execute_update(sql, (final_status, retry_count, topic_id))
            else:
                sql = """
                UPDATE error_records 
                SET final_status = %s, last_ts = NOW() 
                WHERE topic_id = %s
                """
                await self.execute_update(sql, (final_status, topic_id))
            return True
        except Exception as e:
            logger.error(f"更新错误记录状态失败: {e}")
            return False
    
    # 否定词相关操作
    async def get_negation_words(self) -> List[str]:
        """获取否定词列表"""
        sql = "SELECT word FROM negation_words WHERE status = 1"
        results = await self.execute_query(sql)
        return [row['word'] for row in results]
    
    # 问卷相关操作
    async def get_standard_opinion_id_by_content(self, content: str) -> Optional[str]:
        """根据content查询opinion_synonym表，获取standard_opinion_id
        
        根据字段映射表：
        - content对应opinion字段（六级非规范观点）
        - 查询standard_opinion_id字段（五级标准观点ID）
        """
        try:
            # 根据字段映射表，使用opinion字段查询standard_opinion_id
            sql = "SELECT standard_opinion_id FROM opinion_synonym WHERE opinion = %s AND status = 1 LIMIT 1"
            results = await self.execute_query(sql, (content,))
            return results[0]['standard_opinion_id'] if results else None
        except Exception as e:
            logger.error(f"查询opinion_synonym表失败: {e}")
            return None
    
    async def get_opinion_synonym(
        self,
        subject_text: str,
        description_text: str,
        standard_opinion_id: Any
    ) -> Optional[Dict[str, Any]]:
        """从 opinion_synonym 查询观点映射
        
        按 subject_text、description_text、standard_opinion_id 精确查询，
        返回 opinion_id、opinion、standard_opinion。
        """
        sql = (
            "SELECT opinion_id, opinion, standard_opinion "
            "FROM opinion_synonym "
            "WHERE standard_opinion_id = %s AND entity = %s AND description = %s AND status = 1 "
            "LIMIT 1"
        )
        rows = await self.execute_query(sql, (standard_opinion_id, subject_text, description_text))
        return rows[0] if rows else None

    async def get_standard_opinion(
        self,
        standard_opinion_id: Any
    ) -> Optional[Dict[str, Any]]:
        """从 opinion_synonym 查询观点映射
        
        按 subject_text、description_text、standard_opinion_id 精确查询，
        返回 opinion_id、opinion、standard_opinion。
        """
        sql = (
            "SELECT standard_opinion "
            "FROM opinion_synonym "
            "WHERE standard_opinion_id = %s AND status = 1 "
            "LIMIT 1"
        )
        rows = await self.execute_query(sql, (standard_opinion_id))
        return rows[0] if rows else None
    
    async def insert_new_word_record(
        self,
        *,
        original_text: str,
        segment: str,
        new_entity: str,
        new_description: str,
        recommended_label: Optional[str] = None,
        status_flag: int = 0
    ) -> int:
        """向 new_words 表插入一条新词记录。
        
        字段映射：
        - original_text: 原文
        - segment: 片段
        - new_entity: 新观点主体
        - new_description: 新观点描述
        - new_opinion: 新观点（主体+描述）
        - recommended_label: 推荐上级标签-五级标准观点
        - status_flag: 处理状态位
        """
        try:
            new_opinion = f"{new_entity}{new_description}" if (new_entity or new_description) else ""
            sql = (
                "INSERT INTO new_words (original_text, segment, new_entity, new_description, new_opinion, recommended_label, status, created_time, updated_time) "
                "VALUES (%s, %s, %s, %s, %s, %s, %s, NOW(), NOW())"
            )
            params = (
                original_text,
                segment,
                new_entity,
                new_description,
                new_opinion,
                recommended_label or "",
                status_flag,
            )
            return await self.execute_insert(sql, params)
        except Exception as e:
            logger.error(f"插入新词记录失败: {e}")
            return 0
    
    # ================== Business Query Helpers ==================
    async def fetch_secondary_sentiment_rules(self, primary_sentiment: str, rule_type: str = '关键词') -> List[Dict[str, Any]]:
        """按一级情感与规则类型查询二级情感规则。
        对应表：sentiment_rule_keywords(primary_sentiment, secondary_sentiment, rule_keywords, rule_type)
        rule_type 常用值：'关键词' | '分值'
        """
        sql = (
            "SELECT primary_sentiment, secondary_sentiment, rule_keywords, rule_type "
            "FROM sentiment_rule_keywords WHERE primary_sentiment = %s AND rule_type = %s AND status = 1"
        )
        return await self.execute_query(sql, (primary_sentiment, rule_type))

    async def fetch_all_usage_scenario_rules(self) -> List[Dict[str, Any]]:
        """读取全部用车场景规则。
        对应表：scenario_rule_keywords(primary_scenario, secondary_scenario, rule_keywords)
        """
        sql = (
            "SELECT primary_scenario, secondary_scenario, rule_keywords "
            "FROM scenario_rule_keywords WHERE status = 1"
        )
        return await self.execute_query(sql)

    async def fetch_all_secondary_sentiment_rules_by_type(self, rule_type: str) -> List[Dict[str, Any]]:
        """按规则类型一次性读取所有二级情感规则（跨一级情感）。"""
        sql = (
            "SELECT primary_sentiment, secondary_sentiment, rule_keywords, rule_type "
            "FROM sentiment_rule_keywords WHERE rule_type = %s AND status = 1"
        )
        return await self.execute_query(sql, (rule_type,))

    async def query_scenario_mapping(self, description: str, aspect: str, segment: str) -> Optional[Dict[str, Any]]:
        """按描述/方面/片段模糊匹配用车场景映射（若表存在该结构）。"""
        sql = (
            """
            SELECT primary_scenario as usage_scenario, secondary_scenario as confidence 
            FROM scenario_rule_keywords 
            WHERE (rule_keywords LIKE %s OR rule_keywords LIKE %s OR rule_keywords LIKE %s) AND status = 1
            ORDER BY id DESC
            LIMIT 1
            """
        )
        description_pattern = f"%{description}%"
        aspect_pattern = f"%{aspect}%"
        segment_pattern = f"%{segment}%"
        rows = await self.execute_query(sql, (description_pattern, aspect_pattern, segment_pattern))
        return rows[0] if rows else None
    
    # ================== MySQL特有功能 ==================
    async def get_table_info(self, table_name: str) -> Dict[str, Any]:
        """获取表信息"""
        try:
            # 获取表结构
            sql = f"DESCRIBE {table_name}"
            columns = await self.execute_query(sql)
            
            # 获取表统计信息
            count_sql = f"SELECT COUNT(*) as count FROM {table_name}"
            count_result = await self.execute_query(count_sql)
            
            return {
                'table_name': table_name,
                'columns': columns,
                'row_count': count_result[0]['count'] if count_result else 0
            }
        except Exception as e:
            logger.error(f"获取表信息失败: {e}")
            return {}
    
    async def get_index_info(self, table_name: str) -> List[Dict[str, Any]]:
        """获取表索引信息"""
        try:
            sql = f"SHOW INDEX FROM {table_name}"
            return await self.execute_query(sql)
        except Exception as e:
            logger.error(f"获取索引信息失败: {e}")
            return []
    
    async def execute_explain_query(self, sql: str, params: Optional[Tuple] = None) -> List[Dict[str, Any]]:
        """执行EXPLAIN查询，分析查询计划"""
        try:
            explain_sql = f"EXPLAIN {sql}"
            return await self.execute_query(explain_sql, params)
        except Exception as e:
            logger.error(f"EXPLAIN查询失败: {e}")
            return []
    
    async def get_database_status(self) -> Dict[str, Any]:
        """获取数据库状态信息"""
        try:
            # 获取数据库版本
            version_result = await self.execute_query("SELECT VERSION() as version")
            
            # 获取当前连接数
            connections_result = await self.execute_query("SHOW STATUS LIKE 'Threads_connected'")
            
            # 获取数据库大小
            size_result = await self.execute_query("""
                SELECT 
                    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()
            """)
            
            return {
                'version': version_result[0]['version'] if version_result else 'Unknown',
                'connections': connections_result[0]['Value'] if connections_result else 'Unknown',
                'database_size_mb': size_result[0]['size_mb'] if size_result else 0
            }
        except Exception as e:
            logger.error(f"获取数据库状态失败: {e}")
            return {}
    
    async def query_car_levels(self, series_name: str) -> Dict[str, str]:
        """查询车系的car_level1和car_level2信息
        
        Args:
            series_name: 车系名称
            
        Returns:
            Dict包含car_level1和car_level2字段
        """
        try:
            if not series_name:
                return {"car_level1": "", "car_level2": ""}
            
            # 查询ins_car_series_info表获取car_level1和car_level2
            sql = """
            SELECT car_level1, car_level2
            FROM ins_car_series_info 
            WHERE name = %s
            LIMIT 1
            """
            
            results = await self.execute_query(sql, (series_name,))
            
            if results and len(results) > 0:
                row = results[0]
                return {
                    "car_level1": row.get('car_level1', '') or '',
                    "car_level2": row.get('car_level2', '') or ''
                }
                
        except Exception as e:
            logger.error(f"查询car_level信息失败: {e}")
            return {"car_level1": "", "car_level2": ""}
    
    async def query_brand_series_codes(self, brand_name: str = None, series_name: str = None) -> Dict[str, str]:
        """查询品牌和车系对应的code信息
        
        Args:
            brand_name: 品牌名称（可选）
            series_name: 车系名称（可选）
            
        Returns:
            Dict包含brand_code、series_code、car_level1、car_level2字段
        """
        try:
            result = {
                "brand_code": "",
                "series_code": "",
                "car_level1": "",
                "car_level2": ""
            }
            
            # 如果有品牌名称，查询品牌code
            if brand_name:
                brand_sql = """
                SELECT code as brand_code
                FROM ins_brand_info 
                WHERE name = %s
                LIMIT 1
                """
                brand_results = await self.execute_query(brand_sql, (brand_name,))
                if brand_results and len(brand_results) > 0:
                    result["brand_code"] = brand_results[0].get('brand_code', '') or ''
            
            # 如果有车系名称，查询车系code和car_level信息
            if series_name:
                series_sql = """
                SELECT 
                    s.code as series_code,
                    s.car_level1,
                    s.car_level2
                FROM ins_car_series_info s
                WHERE s.name = %s
                LIMIT 1
                """
                series_results = await self.execute_query(series_sql, (series_name,))
                if series_results and len(series_results) > 0:
                    row = series_results[0]
                    result["series_code"] = row.get('series_code', '') or ''
                    result["car_level1"] = row.get('car_level1', '') or ''
                    result["car_level2"] = row.get('car_level2', '') or ''
            
            return result
                
        except Exception as e:
            logger.error(f"查询品牌车系code信息失败: {e}")
            return {"brand_code": "", "series_code": "", "car_level1": "", "car_level2": ""}
    
    async def close(self):
        """关闭连接"""
        # pymysql连接在上下文管理器中自动关闭
        logger.info("MySQL client closed")
