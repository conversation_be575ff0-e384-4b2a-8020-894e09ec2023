"""
向量化客户端（vLLM OpenAI Embeddings 兼容）
默认调用 /v1/embeddings 接口，返回浮点向量（默认512维，可配置）
"""

import json
import logging
import asyncio
import aiohttp
from typing import List, Dict, Any, Optional
from shared.utils.config import get_config_manager

logger = logging.getLogger(__name__)


class EmbeddingClient:
    """向量化客户端"""
    
    def __init__(self):
        self.config = get_config_manager().get_vector_config()
        # vLLM OpenAI 风格配置
        self.openai_base_url = (self.config.get('openai_base_url')
                                or 'http://10.62.133.18:8002/v1')
        self.openai_model = (self.config.get('openai_model')
                             or 'bge-small-zh-v1')
        self.dimension = self.config.get('dimension', 512)
        self.max_batch_size = 50  # 最大批处理大小
        self.timeout = aiohttp.ClientTimeout(total=30)
        self.session = None
    
    async def _get_session(self):
        """获取HTTP会话"""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession(timeout=self.timeout)
        return self.session
    
    async def embed_texts(self, texts: List[str]) -> List[List[float]]:
        """
        批量向量化文本
        
        Args:
            texts: 文本列表
            
        Returns:
            向量列表，每个向量是512维的浮点数列表
        """
        if not texts:
            return []
        
        try:
            # 如果文本数量超过批处理大小，分批处理
            if len(texts) > self.max_batch_size:
                vectors = []
                for i in range(0, len(texts), self.max_batch_size):
                    batch = texts[i:i + self.max_batch_size]
                    batch_vectors = await self._embed_batch(batch)
                    vectors.extend(batch_vectors)
                return vectors
            else:
                return await self._embed_batch(texts)
                
        except Exception as e:
            logger.error(f"向量化失败: {e}")
            raise
    
    async def _embed_batch(self, texts: List[str]) -> List[List[float]]:
        """单批次向量化"""
        session = await self._get_session()
        
        url = f"{self.openai_base_url.rstrip('/')}/embeddings"
        payload = {"model": self.openai_model, "input": texts}
        
        try:
            async with session.post(url, json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    # OpenAI Embeddings 格式: {"data":[{"embedding": [...]} ...]}
                    data = result.get('data', []) if isinstance(result, dict) else []
                    vectors = [item.get('embedding') for item in data if isinstance(item, dict)]
                    
                    # 验证向量维度
                    for i, vector in enumerate(vectors):
                        if len(vector) != self.dimension:
                            logger.warning(f"向量 {i} 维度不匹配: 期望 {self.dimension}, 实际 {len(vector)}")
                    
                    logger.debug(f"成功向量化 {len(texts)} 个文本")
                    return vectors
                else:
                    error_text = await response.text()
                    raise Exception(f"向量化服务返回错误: {response.status} - {error_text}")
                    
        except aiohttp.ClientError as e:
            logger.error(f"向量化服务连接失败: {e}")
            raise Exception(f"向量化服务不可用: {e}")
        except json.JSONDecodeError as e:
            logger.error(f"向量化服务响应格式错误: {e}")
            raise Exception(f"向量化服务响应格式错误: {e}")
    
    async def embed_single(self, text: str) -> List[float]:
        """
        单个文本向量化
        
        Args:
            text: 文本
            
        Returns:
            512维向量
        """
        vectors = await self.embed_texts([text])
        return vectors[0] if vectors else []
    
    def cosine_similarity(self, vector1: List[float], vector2: List[float]) -> float:
        """
        计算余弦相似度
        
        Args:
            vector1: 向量1
            vector2: 向量2
            
        Returns:
            余弦相似度 (-1 到 1)
        """
        if len(vector1) != len(vector2):
            raise ValueError("向量维度不匹配")
        
        # 计算点积
        dot_product = sum(a * b for a, b in zip(vector1, vector2))
        
        # 计算向量模长
        norm1 = sum(a * a for a in vector1) ** 0.5
        norm2 = sum(b * b for b in vector2) ** 0.5
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return dot_product / (norm1 * norm2)
    
    def batch_cosine_similarity(self, query_vector: List[float], vectors: List[List[float]]) -> List[float]:
        """
        批量计算余弦相似度
        
        Args:
            query_vector: 查询向量
            vectors: 候选向量列表
            
        Returns:
            相似度列表
        """
        similarities = []
        for vector in vectors:
            try:
                similarity = self.cosine_similarity(query_vector, vector)
                similarities.append(similarity)
            except ValueError:
                similarities.append(0.0)
        
        return similarities
    
    async def close(self):
        """关闭客户端"""
        if self.session and not self.session.closed:
            await self.session.close()
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            test_vectors = await self.embed_texts(["测试文本"])
            return {
                "status": "healthy",
                "openai_base_url": self.openai_base_url,
                "model": self.openai_model,
                "dimension": self.dimension,
                "test_vector_dim": len(test_vectors[0]) if test_vectors else 0
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "openai_base_url": self.openai_base_url,
                "model": self.openai_model,
                "error": str(e)
            }


# 全局客户端实例
_embedding_client = None


def get_embedding_client() -> EmbeddingClient:
    """获取向量化客户端实例"""
    global _embedding_client
    if _embedding_client is None:
        _embedding_client = EmbeddingClient()
    return _embedding_client


async def embed_texts(texts: List[str]) -> List[List[float]]:
    """便捷的批量向量化函数"""
    client = get_embedding_client()
    return await client.embed_texts(texts)


async def embed_single(text: str) -> List[float]:
    """便捷的单个向量化函数"""
    client = get_embedding_client()
    return await client.embed_single(text)


# 示例使用
async def main():
    """测试示例"""
    client = get_embedding_client()
    
    # 测试单个文本
    vector = await client.embed_single("这是一个测试文本")
    print(f"单个向量维度: {len(vector)}")
    
    # 测试批量文本
    texts = ["大灯很好看", "内饰不错", "车灯质量差"]
    vectors = await client.embed_texts(texts)
    print(f"批量向量数量: {len(vectors)}")
    
    # 测试相似度计算
    similarity = client.cosine_similarity(vectors[0], vectors[1])
    print(f"相似度: {similarity}")
    
    # 健康检查
    health = await client.health_check()
    print(f"健康状态: {health}")
    
    await client.close()


if __name__ == "__main__":
    asyncio.run(main())