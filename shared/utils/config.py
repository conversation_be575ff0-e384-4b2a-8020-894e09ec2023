"""
配置管理工具
"""
import os
import yaml
import logging
from typing import Dict, Any, Optional
from pathlib import Path
from shared.models.schemas import (
    DatabaseConfig, ElasticsearchConfig, KafkaConfig, ServiceConfig
)

logger = logging.getLogger(__name__)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or os.environ.get('CONFIG_FILE', 'configs/config.yaml')
        self._config_data: Dict[str, Any] = {}
        self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        try:
            config_path = Path(self.config_file)
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    self._config_data = yaml.safe_load(f) or {}
                logger.info(f"Configuration loaded from {self.config_file}")
            else:
                logger.warning(f"Configuration file {self.config_file} not found, using environment variables")
                self._config_data = {}
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            raise
    
    def _get_env_or_config(self, env_key: str, config_path: str, default=None):
        """优先从环境变量获取，然后从配置文件获取"""
        # 首先检查环境变量
        env_value = os.environ.get(env_key)
        if env_value is not None and env_value.strip():
            return env_value
        
        # 然后检查配置文件
        keys = config_path.split('.')
        value = self._config_data
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        return value
    
    def get_database_config(self) -> DatabaseConfig:
        """获取数据库配置"""
        return DatabaseConfig(
            host=self._get_env_or_config('DB_HOST', 'database.host', 'localhost'),
            port=int(self._get_env_or_config('DB_PORT', 'database.port', 9030)),
            username=self._get_env_or_config('DB_USERNAME', 'database.username', 'root'),
            password=self._get_env_or_config('DB_PASSWORD', 'database.password', ''),
            database=self._get_env_or_config('DB_DATABASE', 'database.database', 'voc_db')
        )
    
    def get_elasticsearch_config(self) -> ElasticsearchConfig:
        """获取Elasticsearch配置"""
        hosts_config = self._get_env_or_config('ES_HOSTS', 'elasticsearch.hosts', 'localhost:9200')
        
        # 如果环境变量和配置文件都没有值，使用默认值
        if not hosts_config:
            hosts_config = 'localhost:9200'
        
        # 处理列表或字符串格式
        if isinstance(hosts_config, list):
            hosts = hosts_config
        else:
            hosts = [host.strip() for host in hosts_config.split(',')]
        
        # 确保每个host都有scheme
        processed_hosts = []
        for host in hosts:
            if not host.startswith(('http://', 'https://')):
                host = f'http://{host}'
            processed_hosts.append(host)
        
        return ElasticsearchConfig(
            hosts=processed_hosts,
            username=self._get_env_or_config('ES_USERNAME', 'elasticsearch.username', ''),
            password=self._get_env_or_config('ES_PASSWORD', 'elasticsearch.password', ''),
            timeout=int(self._get_env_or_config('ES_TIMEOUT', 'elasticsearch.timeout', 30))
        )
    
    def get_kafka_config(self) -> KafkaConfig:
        """获取Kafka配置"""
        bootstrap_servers_config = self._get_env_or_config(
            'KAFKA_BOOTSTRAP_SERVERS', 
            'kafka.bootstrap_servers', 
            'localhost:9092'
        )
        
        # 处理列表或字符串格式
        if isinstance(bootstrap_servers_config, list):
            bootstrap_servers = bootstrap_servers_config
        else:
            bootstrap_servers = [server.strip() for server in bootstrap_servers_config.split(',')]
        
        return KafkaConfig(
            bootstrap_servers=bootstrap_servers,
            client_id=self._get_env_or_config('KAFKA_CLIENT_ID', 'kafka.client_id', 'voc-client'),
            group_id=self._get_env_or_config('KAFKA_GROUP_ID', 'kafka.group_id', 'voc-group'),
            auto_offset_reset=self._get_env_or_config('KAFKA_AUTO_OFFSET_RESET', 'kafka.auto_offset_reset', 'latest')
        )
    
    def get_service_config(self, service_name: str) -> ServiceConfig:
        """获取服务配置"""
        service_upper = service_name.upper().replace('-', '_')
        
        return ServiceConfig(
            name=service_name,
            version=self._get_env_or_config('SERVICE_VERSION', f'services.{service_name}.version', '1.0.0'),
            host=self._get_env_or_config(f'{service_upper}_HOST', f'services.{service_name}.host', '0.0.0.0'),
            port=int(self._get_env_or_config(f'{service_upper}_PORT', f'services.{service_name}.port', 8000)),
            debug=self._get_env_or_config(f'{service_upper}_DEBUG', f'services.{service_name}.debug', False),
            log_level=self._get_env_or_config(f'{service_upper}_LOG_LEVEL', f'services.{service_name}.log_level', 'INFO')
        )
    
    def get_model_config(self, model_name: str) -> Dict[str, Any]:
        """获取模型配置"""
        model_upper = model_name.upper().replace('-', '_')
        
        return {
            'model_path': self._get_env_or_config(f'{model_upper}_MODEL_PATH', f'models.{model_name}.model_path'),
            'api_url': self._get_env_or_config(f'{model_upper}_API_URL', f'models.{model_name}.api_url'),
            'api_key': self._get_env_or_config(f'{model_upper}_API_KEY', f'models.{model_name}.api_key'),
            'timeout': int(self._get_env_or_config(f'{model_upper}_TIMEOUT', f'models.{model_name}.timeout', 30)),
            'max_retries': int(self._get_env_or_config(f'{model_upper}_MAX_RETRIES', f'models.{model_name}.max_retries', 3)),
            'batch_size': int(self._get_env_or_config(f'{model_upper}_BATCH_SIZE', f'models.{model_name}.batch_size', 10)),
            'batch_timeout': float(self._get_env_or_config(f'{model_upper}_BATCH_TIMEOUT', f'models.{model_name}.batch_timeout', 5.0)),
            'enable_batch_processing': self._get_env_or_config(f'{model_upper}_ENABLE_BATCH_PROCESSING', f'models.{model_name}.enable_batch_processing', True)
        }
    
    def get_vector_config(self) -> Dict[str, Any]:
        """获取向量配置"""
        return {
            # 向量化模型配置已移除，使用外部API
            'api_url': self._get_env_or_config('VECTOR_API_URL', 'vector.api_url', 'http://172.16.76.178:7777/bge-small'),
            'dimension': int(self._get_env_or_config('VECTOR_DIMENSION', 'vector.dimension', 768)),
            'similarity_threshold': float(self._get_env_or_config('VECTOR_SIMILARITY_THRESHOLD', 'vector.similarity_threshold', 0.7)),
            'max_results': int(self._get_env_or_config('VECTOR_MAX_RESULTS', 'vector.max_results', 5)),
            # 兼容 vLLM OpenAI Embeddings 风格配置（可选）
            'openai_base_url': self._get_env_or_config('VECTOR_OPENAI_BASE_URL', 'vector.openai_base_url'),
            'openai_model': self._get_env_or_config('VECTOR_OPENAI_MODEL', 'vector.openai_model')
        }
    
    def get_rule_config(self) -> Dict[str, Any]:
        """获取规则配置"""
        return {
            'max_distance': int(self._get_env_or_config('RULE_MAX_DISTANCE', 'rule.max_distance', 10)),
            'enable_negation_check': self._get_env_or_config('RULE_ENABLE_NEGATION_CHECK', 'rule.enable_negation_check', True),
            'enable_punctuation_check': self._get_env_or_config('RULE_ENABLE_PUNCTUATION_CHECK', 'rule.enable_punctuation_check', True)
        }
    
    def get_retry_config(self) -> Dict[str, Any]:
        """获取重试配置"""
        return {
            'max_retries': int(self._get_env_or_config('RETRY_MAX_RETRIES', 'retry.max_retries', 5)),
            'retry_delay': int(self._get_env_or_config('RETRY_DELAY', 'retry.retry_delay', 60)),
            'exponential_backoff': self._get_env_or_config('RETRY_EXPONENTIAL_BACKOFF', 'retry.exponential_backoff', True)
        }
    
    def get_all_config(self) -> Dict[str, Any]:
        """获取所有配置"""
        return {
            'database': self.get_database_config().model_dump(),
            'elasticsearch': self.get_elasticsearch_config().model_dump(),
            'kafka': self.get_kafka_config().model_dump(),
            'vector': self.get_vector_config(),
            'rule': self.get_rule_config(),
            'retry': self.get_retry_config()
        }
    
    def load_prompts(self) -> dict:
        """加载提示词配置文件"""
        try:
            config_path = Path(__file__).parent.parent.parent / "configs" / "llm.yaml"
            with open(config_path, 'r', encoding='utf-8') as f:
                import yaml
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"Failed to load prompts config: {e}")
            return {}


# 全局配置管理器实例
config_manager = ConfigManager()


def get_config_manager() -> ConfigManager:
    """获取配置管理器实例"""
    return config_manager