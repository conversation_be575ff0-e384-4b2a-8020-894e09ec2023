"""
文本处理工具
"""
import re
import logging
from typing import List, Tuple, Dict, Any, Optional
import sys


logger = logging.getLogger(__name__)


class TextProcessor:
    """文本处理器"""
    
    def __init__(self):
        # 内置常用否定词列表
        self._negation_words = [
            # 常见否定词
            '不', '没', '无', '非', '未', '别', '莫', '勿', '禁止',
            # 程度否定
            '不太', '不够', '不怎么', '不是很', '没有', '没那么',
            # 否定短语
            '不是', '不行', '不好', '不对', '不会', '不能', '不要',
            '没什么', '没办法', '没意思', '算不上', '称不上',
            # 转折否定
            '但不', '却不', '只是不', '就是不', '偏不', '反而不'
        ]
        
        # 预编译正则模式
        self.punctuation_pattern = re.compile(r'[。！？；，、：""''（）【】《》]')
        self.whitespace_pattern = re.compile(r'\s+')
        self.brand_series_pattern = re.compile(r'([^\w\s]+)(\w+)')
    
    async def initialize(self):
        """初始化文本处理器"""
        logger.info(f"Text processor initialized with {len(self._negation_words)} built-in negation words")
        return True
    
    def set_negation_words(self, words: List[str]):
        """设置否定词列表（可选，会覆盖内置列表）"""
        self._negation_words = words
        logger.info(f"Updated negation words list to {len(words)} words")
    
    def get_negation_words(self) -> List[str]:
        """获取当前否定词列表"""
        return self._negation_words.copy()
    
    def split_sentences(self, text: str) -> List[str]:
        """粗分句"""
        # 清理文本
        text = self.whitespace_pattern.sub(' ', text.strip())
        
        # 使用标点符号分句
        sentences = self.punctuation_pattern.split(text)
        
        # 过滤空句子和过短句子
        result = []
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 2:  # 至少3个字符
                result.append(sentence)
        
        return result
    
    def extract_brand_series_mentions(self, text: str) -> List[Dict[str, str]]:
        """提取品牌车系提及"""
        mentions = []
        
        # 简单的品牌车系识别模式
        # 这里可以根据实际需求扩展更复杂的识别逻辑
        brand_keywords = ['长安', '吉利', '比亚迪', '奔驰', '宝马', '奥迪', '丰田', '本田', '大众']
        series_patterns = [
            r'UNI-[A-Z]',
            r'星越',
            r'博越',
            r'缤越',
            r'A\d+',
            r'C\d+',
            r'E\d+',
            r'3系',
            r'5系',
            r'7系'
        ]
        
        for brand in brand_keywords:
            if brand in text:
                # 查找品牌附近的车系
                brand_pos = text.find(brand)
                context = text[max(0, brand_pos-10):brand_pos+30]
                
                for pattern in series_patterns:
                    matches = re.finditer(pattern, context)
                    for match in matches:
                        mentions.append({
                            'brand': brand,
                            'series': match.group(),
                            'context': context,
                            'position': brand_pos
                        })
        
        return mentions
    
    def count_brand_series_mentions(self, text: str) -> int:
        """统计品牌车系提及数量"""
        mentions = self.extract_brand_series_mentions(text)
        # 去重
        unique_mentions = set()
        for mention in mentions:
            unique_mentions.add(f"{mention['brand']}_{mention['series']}")
        return len(unique_mentions)
    
    def extract_entity_description_pairs(self, text: str) -> List[Tuple[str, str]]:
        """提取主体-描述对"""
        pairs = []
        
        # 简单的主体-描述提取逻辑
        # 基于常见的汽车评价模式
        entity_patterns = [
            r'(大灯|内饰|外观|座椅|发动机|变速箱|悬挂|轮胎|音响|空调|方向盘)',
            r'(颜值|设计|质量|性能|舒适性|操控|油耗|空间|配置)'
        ]
        
        description_patterns = [
            r'(好看|难看|漂亮|丑|美观|时尚|老气)',
            r'(好|不好|棒|差|优秀|糟糕|满意|失望)',
            r'(舒服|不舒服|舒适|硬|软|宽敞|狭窄)',
            r'(省油|费油|经济|耗油|节能)'
        ]
        
        for entity_pattern in entity_patterns:
            entity_matches = re.finditer(entity_pattern, text)
            for entity_match in entity_matches:
                entity = entity_match.group()
                entity_pos = entity_match.start()
                
                # 在主体附近查找描述
                context_start = max(0, entity_pos - 20)
                context_end = min(len(text), entity_pos + 50)
                context = text[context_start:context_end]
                
                for desc_pattern in description_patterns:
                    desc_matches = re.finditer(desc_pattern, context)
                    for desc_match in desc_matches:
                        description = desc_match.group()
                        
                        # 检查是否有否定词
                        if not self._has_negation_between(text, entity, description):
                            pairs.append((entity, description))
        
        return pairs
    
    def _has_negation_between(self, text: str, entity: str, description: str, negation_words: List[str]) -> bool:
        """检查主体和描述之间是否有否定词"""
        entity_pos = text.find(entity)
        desc_pos = text.find(description)
        
        if entity_pos == -1 or desc_pos == -1:
            return False
        
        # 确定主体和描述的位置关系
        if entity_pos < desc_pos:
            # 主体在描述前面：检查主体结束位置到描述开始位置之间的内容
            start = entity_pos + len(entity)
            end = desc_pos
        else:
            # 描述在主体前面：检查描述结束位置到主体开始位置之间的内容
            start = desc_pos + len(description)
            end = entity_pos
        
        # 提取主体和描述之间的内容
        between_text = text[start:end].strip()
        
        # 如果没有内容，直接返回False
        if not between_text:
            return False
        
        # 如果没有否定词，直接返回False
        if not negation_words:
            return False
        
        # 检查中间内容是否包含否定词
        for word in negation_words:
            if word in between_text:
                return True
        
        return False
    
    def check_distance_and_punctuation(
        self, 
        text: str, 
        entity: str, 
        description: str, 
        max_distance: int = 50
    ) -> bool:
        """检查主体和描述之间的距离和标点符号
        
        Args:
            text: 原始文本
            entity: 主体文本
            description: 描述文本
            max_distance: 最大允许距离（字符数）
            
        Returns:
            bool: True表示通过检查，False表示未通过
        """
        try:
            # 1. 查找主体和描述在文本中的位置
            entity_pos = text.find(entity)
            desc_pos = text.find(description)
            
            if entity_pos == -1 or desc_pos == -1:
                logger.warning(f"Entity or description not found in text")
                return False
            
            # 2. 计算实际距离（字符数）
            # 计算两个文本片段之间的最小距离
            entity_end = entity_pos + len(entity)
            desc_end = desc_pos + len(description)
            
            # 计算重叠情况
            if entity_pos <= desc_pos <= entity_end or desc_pos <= entity_pos <= desc_end:
                # 有重叠，距离为0
                actual_distance = 0
            else:
                # 无重叠，计算最近端点之间的距离
                actual_distance = min(
                    abs(entity_end - desc_pos),
                    abs(desc_end - entity_pos)
                )
            
            logger.debug(f"Entity position: {entity_pos}-{entity_end}, Description position: {desc_pos}-{desc_end}")
            logger.debug(f"Actual distance: {actual_distance}, Max allowed: {max_distance}")
            
            # 3. 检查距离是否超过限制
            if actual_distance > max_distance:
                logger.debug(f"Distance {actual_distance} exceeds max distance {max_distance}")
                return False
            
            # 4. 检查标点符号分隔
            # 获取主体和描述之间的文本片段
            start_pos = min(entity_pos, desc_pos)
            end_pos = max(entity_end, desc_end)
            segment = text[start_pos:end_pos]
            
            # 检查是否被标点符号合理分隔
            # 如果距离很近（<10个字符），检查是否有标点符号分隔
            if actual_distance < 10 and actual_distance > 0:
                # 查找主体和描述之间的文本
                if entity_pos < desc_pos:
                    between_text = text[entity_end:desc_pos]
                else:
                    between_text = text[desc_end:entity_pos]
                
                # 检查是否有标点符号
                has_punctuation = bool(self.punctuation_pattern.search(between_text))
                
                # 如果距离很近但没有标点符号，可能是误匹配
                if not has_punctuation and actual_distance > 3:
                    logger.debug(f"No punctuation between entity and description: '{between_text}'")
                    return False
            
            # 5. 检查是否在同一个句子中
            # 查找句子边界
            sentence_start = 0
            sentence_end = len(text)
            
            # 向前找句子开始
            for i in range(min(entity_pos, desc_pos) - 1, -1, -1):
                if text[i] in '。！？；':
                    sentence_start = i + 1
                    break
                elif i <= min(entity_pos, desc_pos) - 50:  # 最多向前找50个字符
                    sentence_start = min(entity_pos, desc_pos) - 50
                    break
            
            # 向后找句子结束
            for i in range(max(entity_end, desc_end), len(text)):
                if text[i] in '。！？；':
                    sentence_end = i + 1
                    break
                elif i >= max(entity_end, desc_end) + 50:  # 最多向后找50个字符
                    sentence_end = max(entity_end, desc_end) + 50
                    break
            
            # 检查主体和描述是否在同一个句子中
            sentence = text[sentence_start:sentence_end]
            entity_in_sentence = entity in sentence
            desc_in_sentence = description in sentence
            
            if not (entity_in_sentence and desc_in_sentence):
                logger.debug(f"Entity and description not in same sentence")
                return False
            
            logger.debug(f"Distance and punctuation check passed")
            return True
            
        except Exception as e:
            logger.error(f"Error in distance and punctuation check: {e}")
            return False
    

    
    def find_sentence_boundaries(self, text: str, entity: str, description: str) -> Optional[str]:
        """根据主体和描述找到对应的句子"""
        entity_pos = text.find(entity)
        desc_pos = text.find(description)
        
        if entity_pos == -1 or desc_pos == -1:
            return None
        
        start_pos = min(entity_pos, desc_pos)
        end_pos = max(entity_pos + len(entity), desc_pos + len(description))
        
        # 向前找最近的标点或开头
        sentence_start = 0
        for i in range(start_pos - 1, -1, -1):
            if text[i] in '。！？；' or i == 0:
                sentence_start = i + 1 if i > 0 else 0
                break
            elif i <= start_pos - 20:  # 最多向前找20个字符
                sentence_start = start_pos - 20
                break
        
        # 向后找最近的标点或结尾
        sentence_end = len(text)
        for i in range(end_pos, len(text)):
            if text[i] in '。！？；':
                sentence_end = i + 1
                break
            elif i >= end_pos + 20:  # 最多向后找20个字符
                sentence_end = end_pos + 20
                break
        
        return text[sentence_start:sentence_end].strip()
    
    def normalize_text(self, text: str) -> str:
        """文本标准化"""
        # 统一空白字符
        text = self.whitespace_pattern.sub(' ', text)
        
        # 转换为小写（保留中文）
        # text = text.lower()  # 可选，根据需求决定
        
        # 去除首尾空白
        text = text.strip()
        
        return text