"""
日志配置工具
"""
import logging
import sys
from typing import Optional
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
from pathlib import Path


def setup_logging(
    service_name: str,
    log_level: str = "INFO",
    log_dir: Optional[str] = None,
    console_output: bool = True,
    file_output: bool = True,
    max_bytes: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5
):
    """设置日志配置"""
    
    # 创建根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    
    # 清除现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 设置日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
    )
    
    # 控制台输出
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
    
    # 文件输出
    if file_output:
        if log_dir:
            log_path = Path(log_dir)
            log_path.mkdir(parents=True, exist_ok=True)
            log_file = log_path / f"{service_name}.log"
        else:
            log_file = f"{service_name}.log"
        
        # 使用轮转文件处理器
        file_handler = RotatingFileHandler(
            filename=log_file,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
        
        # 错误日志单独记录
        error_log_file = log_file.parent / f"{service_name}_error.log" if isinstance(log_file, Path) else f"{service_name}_error.log"
        error_handler = RotatingFileHandler(
            filename=error_log_file,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        root_logger.addHandler(error_handler)
    
    # 设置第三方库的日志级别
    logging.getLogger('aiokafka').setLevel(logging.WARNING)
    logging.getLogger('elasticsearch').setLevel(logging.WARNING)
    logging.getLogger('pymysql').setLevel(logging.WARNING)
    
    logger = logging.getLogger(service_name)
    logger.info(f"Logging initialized for {service_name} with level {log_level}")
    
    return logger


class StructuredLogger:
    """结构化日志器"""
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
    
    def log_request(self, method: str, path: str, status_code: int, duration: float, **kwargs):
        """记录请求日志"""
        self.logger.info(
            f"Request: {method} {path} - Status: {status_code} - Duration: {duration:.3f}s",
            extra={
                'event_type': 'request',
                'method': method,
                'path': path,
                'status_code': status_code,
                'duration': duration,
                **kwargs
            }
        )
    
    def log_kafka_message(self, action: str, topic: str, topic_id: str, **kwargs):
        """记录Kafka消息日志"""
        self.logger.info(
            f"Kafka {action}: {topic} - Topic ID: {topic_id}",
            extra={
                'event_type': 'kafka_message',
                'action': action,
                'topic': topic,
                'topic_id': topic_id,
                **kwargs
            }
        )
    
    def log_model_inference(self, model_name: str, input_size: int, duration: float, success: bool, **kwargs):
        """记录模型推理日志"""
        level = logging.INFO if success else logging.ERROR
        self.logger.log(
            level,
            f"Model {model_name}: {'Success' if success else 'Failed'} - Input size: {input_size} - Duration: {duration:.3f}s",
            extra={
                'event_type': 'model_inference',
                'model_name': model_name,
                'input_size': input_size,
                'duration': duration,
                'success': success,
                **kwargs
            }
        )
    
    def log_database_operation(self, operation: str, table: str, rows_affected: int, duration: float, **kwargs):
        """记录数据库操作日志"""
        self.logger.info(
            f"DB {operation}: {table} - Rows: {rows_affected} - Duration: {duration:.3f}s",
            extra={
                'event_type': 'database_operation',
                'operation': operation,
                'table': table,
                'rows_affected': rows_affected,
                'duration': duration,
                **kwargs
            }
        )
    
    def log_elasticsearch_operation(self, operation: str, index: str, docs_count: int, duration: float, **kwargs):
        """记录Elasticsearch操作日志"""
        self.logger.info(
            f"ES {operation}: {index} - Docs: {docs_count} - Duration: {duration:.3f}s",
            extra={
                'event_type': 'elasticsearch_operation',
                'operation': operation,
                'index': index,
                'docs_count': docs_count,
                'duration': duration,
                **kwargs
            }
        )
    
    def log_error(self, error_type: str, error_message: str, **kwargs):
        """记录错误日志"""
        self.logger.error(
            f"Error [{error_type}]: {error_message}",
            extra={
                'event_type': 'error',
                'error_type': error_type,
                'error_message': error_message,
                **kwargs
            }
        )
    
    def log_business_event(self, event_name: str, **kwargs):
        """记录业务事件日志"""
        self.logger.info(
            f"Business Event: {event_name}",
            extra={
                'event_type': 'business_event',
                'event_name': event_name,
                **kwargs
            }
        )