"""
服务基类
提供所有微服务的通用功能和结构
"""
import asyncio
import signal
import sys
from pathlib import Path
from typing import Dict, Any, Optional
from abc import ABC, abstractmethod
import httpx

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from fastapi import FastAPI
from contextlib import asynccontextmanager
import uvicorn

from shared.utils.config import get_config_manager
from shared.utils.logger import setup_logging, StructuredLogger
from shared.kafka.kafka_client import KafkaClient
from shared.models.schemas import APIResponse


class BaseService(ABC):
    """微服务基类"""
    
    def __init__(self, service_name: str):
        """
        初始化基类
        
        Args:
            service_name: 服务名称，用于配置和日志
        """
        self.service_name = service_name
        self.config_manager = get_config_manager()
        self.logger = None
        self.structured_logger = None
        self.kafka_client = None
        self.http_client = None
        self.shutdown_event = asyncio.Event()
        
        # 配置
        self.service_config = self.config_manager.get_service_config(service_name)
        self.kafka_config = self.config_manager.get_kafka_config()
        
        # 初始化子类特定配置
        self._init_service_specific_config()
    
    @abstractmethod
    def _init_service_specific_config(self):
        """初始化服务特定的配置，由子类实现"""
        pass
    
    async def initialize(self):
        """初始化服务的通用组件"""
        # 设置日志
        self.logger = setup_logging(
            service_name=self.service_name,
            log_level=self.service_config.log_level,
            log_dir='logs'
        )
        self.structured_logger = StructuredLogger(self.service_name)
        
        # 初始化Kafka客户端
        self.kafka_client = KafkaClient(self.kafka_config)
        await self.kafka_client.initialize()
        
        # 初始化HTTP客户端 - 优化连接池配置
        self.http_client = httpx.AsyncClient(
            timeout=httpx.Timeout(10.0, connect=3.0),  # 10s总超时，3s连接超时
            limits=httpx.Limits(
                max_connections=20,                     # 从10增到20
                max_keepalive_connections=10            # 从5增到10
            )
        )
        
        # 调用子类特定的初始化
        await self._initialize_service_specific()
        
        self.logger.info(f"{self.service_name} service initialized successfully")
    
    def _get_http_timeout(self) -> float:
        """获取HTTP超时配置，子类可以重写"""
        return 30.0
    
    @abstractmethod
    async def _initialize_service_specific(self):
        """服务特定的初始化逻辑，由子类实现"""
        pass
    
    @abstractmethod
    async def start_consumers(self):
        """启动Kafka消费者，由子类实现"""
        pass
    
    async def health_check(self) -> dict:
        """基础健康检查，子类可以扩展"""
        base_health = {
            'service': self.service_name,
            'status': 'healthy',
            'kafka': 'connected' if self.kafka_client else 'disconnected',
            'http_client': 'ready' if self.http_client else 'not_ready',
            'version': getattr(self.service_config, 'version', '1.0.0')
        }
        
        # 允许子类扩展健康检查
        service_specific_health = await self._service_specific_health_check()
        base_health.update(service_specific_health)
        
        return base_health
    
    async def _service_specific_health_check(self) -> dict:
        """服务特定的健康检查，子类可以重写"""
        return {}
    
    async def shutdown(self):
        """关闭服务的通用逻辑"""
        self.logger.info(f"Shutting down {self.service_name} service...")
        
        # 调用子类特定的关闭逻辑
        await self._shutdown_service_specific()
        
        # 关闭HTTP客户端
        if self.http_client:
            await self.http_client.aclose()
        
        # 关闭Kafka客户端
        if self.kafka_client:
            self.kafka_client.close()
        
        self.shutdown_event.set()
        self.logger.info(f"{self.service_name} service shutdown complete")
    
    async def _shutdown_service_specific(self):
        """服务特定的关闭逻辑，子类可以重写"""
        pass
    
    def create_fastapi_app(self, title: str = None, description: str = None) -> FastAPI:
        """创建FastAPI应用实例"""
        app_title = title or f"{self.service_name.title()} Service"
        app_description = description or f"{self.service_name}微服务"
        
        app = FastAPI(
            title=app_title,
            description=app_description,
            version="1.0.0",
            lifespan=self._create_lifespan()
        )
        
        # 添加通用的健康检查端点
        @app.get("/health")
        async def health_check_endpoint():
            """健康检查端点"""
            try:
                health_data = await self.health_check()
                return APIResponse(success=True, message="Service is healthy", data=health_data)
            except Exception as e:
                return APIResponse(success=False, message=f"Health check failed: {e}")
        
        # 允许子类添加自定义路由
        self._add_custom_routes(app)
        
        return app
    
    @abstractmethod
    def _add_custom_routes(self, app: FastAPI):
        """添加服务特定的路由，由子类实现"""
        pass
    
    def _create_lifespan(self):
        """创建应用生命周期管理器"""
        @asynccontextmanager
        async def lifespan(app: FastAPI):
            # 启动
            await self.initialize()
            
            # 启动消费者任务
            consumer_task = asyncio.create_task(self.start_consumers())
            
            # 信号处理
            def signal_handler(signum, frame):
                asyncio.create_task(self.shutdown())
            
            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
            
            yield
            
            # 关闭
            consumer_task.cancel()
            await self.shutdown()
        
        return lifespan
    
    def run(self, host: str = None, port: int = None, reload: bool = None):
        """运行服务"""
        app = self.create_fastapi_app()
        
        uvicorn.run(
            app,
            host=host or self.service_config.host,
            port=port or self.service_config.port,
            reload=reload if reload is not None else self.service_config.debug,
            log_level=self.service_config.log_level.lower()
        )
    
    def run_with_app_string(self, app_module: str = "main:app"):
        """使用应用字符串运行服务（用于热重载）"""
        uvicorn.run(
            app_module,
            host=self.service_config.host,
            port=self.service_config.port,
            reload=self.service_config.debug,
            log_level=self.service_config.log_level.lower()
        )


class ModelService(BaseService):
    """模型服务基类，继承自BaseService"""
    
    def __init__(self, service_name: str, model_name: str = None):
        """
        初始化模型服务
        
        Args:
            service_name: 服务名称
            model_name: 模型名称，默认与服务名称相同
        """
        self.model_name = model_name or service_name
        self.model_config = None
        super().__init__(service_name)
    
    def _init_service_specific_config(self):
        """初始化模型特定配置"""
        self.model_config = self.config_manager.get_model_config(self.model_name)
    
    def _get_http_timeout(self) -> float:
        """模型服务通常需要更长的超时时间"""
        return self.model_config.get('timeout', 30.0) if self.model_config else 30.0
    
    async def _service_specific_health_check(self) -> dict:
        """模型服务特定的健康检查"""
        return {
            'model_name': self.model_name,
            'model_config_loaded': self.model_config is not None
        }


class GatewayService(BaseService):
    """网关服务基类，继承自BaseService"""
    
    def __init__(self):
        super().__init__('gateway')
        self.services = {}
        self.service_health_cache = {}
        self.health_check_interval = 30
        self.last_health_check = 0
    
    def _init_service_specific_config(self):
        """初始化网关特定配置"""
        # 网关服务可能有特殊的配置需求
        pass
    
    async def _initialize_service_specific(self):
        """网关特定初始化"""
        # 网关特定的初始化逻辑
        pass
    
    def add_service_route(self, service_name: str, service_url: str):
        """添加服务路由"""
        self.services[service_name] = service_url
    
    async def check_service_health(self, service_name: str) -> bool:
        """检查单个服务健康状态"""
        if service_name not in self.services:
            return False
        
        try:
            url = f"{self.services[service_name]}/health"
            response = await self.http_client.get(url, timeout=5.0)
            return response.status_code == 200
        except Exception as e:
            self.logger.warning(f"Health check failed for {service_name}: {e}")
            return False
