"""
Kafka客户端 - 使用同步的kafka-python替代aiokafka
"""
import json
import logging
import threading
import socket
from typing import Dict, Any, Callable, Optional, List
from kafka import KafkaProducer, KafkaConsumer, KafkaClient
from kafka.errors import KafkaError
from shared.models.schemas import KafkaConfig, BaseKafkaMessage

logger = logging.getLogger(__name__)

# DNS别名映射：解决Kafka broker advertised主机名解析问题
_REAL_GETADDRINFO = socket.getaddrinfo

def _patched_getaddrinfo(host, *args, **kwargs):
    """DNS别名映射函数，将broker的advertised主机名映射到可达IP"""
    # 从配置文件获取实际的Kafka服务器地址
    try:
        from shared.utils.config import get_config_manager
        config = get_config_manager().get_kafka_config()
        actual_servers = config.bootstrap_servers
        if actual_servers:
            # 提取IP地址和端口
            actual_host = actual_servers[0].split(':')[0]
            actual_port = int(actual_servers[0].split(':')[1])
        else:
            # 如果配置读取失败，使用默认值
            actual_host = '************'
            actual_port = 29092
    except Exception as e:
        # 如果配置读取失败，使用默认值
        actual_host = '************'
        actual_port = 29092
    
    # 需要映射的主机名列表
    mapped_hosts = ['localhost', 'voc-kafka-service', 'voc-kafka-service.voc-test.svc.cluster.local', 'kafka', 'kafka-service']
    
    if host in mapped_hosts:
        new_host = actual_host
        if len(args) >= 1:
            port = args[0]
            # 如果端口是9092，映射到配置文件中的实际端口
            if str(port) == '9092':
                new_port = actual_port
                new_args = (new_port,) + args[1:]
                return _REAL_GETADDRINFO(new_host, *new_args, **kwargs)
            new_args = (port,) + args[1:]
            return _REAL_GETADDRINFO(new_host, *new_args, **kwargs)
        return _REAL_GETADDRINFO(new_host, *args, **kwargs)
    
    return _REAL_GETADDRINFO(host, *args, **kwargs)

# 应用DNS补丁
socket.getaddrinfo = _patched_getaddrinfo


class KafkaClient:
    """Kafka客户端 - 同步版本"""
    def __init__(self, config: KafkaConfig):
        self.config = config
        self.producer: Optional[KafkaProducer] = None
        self.consumers: Dict[str, KafkaConsumer] = {}
        self.consumer_threads: Dict[str, threading.Thread] = {}
        self._stop_consumers = False
        
        # Topic名称定义
        self.TOPICS = {
            'VOC_TOMODEL_TOPIC': 'voc_toModel_topic',
            'RESULT_TOPIC': 'result_topic',
            'TEXT_PROCESSOR_TOPIC': 'text_processor_topic',
            'RULE_MATCHER_TOPIC': 'rule_matcher_topic',
            'VECTOR_MATCHER_TOPIC': 'vector_matcher_topic',
            'UIE_TOPIC': 'uie_topic',
            'LLM_TOPIC': 'llm_topic',
            'INTENT_TOPIC': 'intent_topic',
            'BRAND_ATTRIBUTION_TOPIC': 'brand_attribution_topic',
            'FINAL_TOPIC': 'final_topic',
            'MODEL_RETRY_TOPIC': 'model_retry_topic',
            'MODEL_ERROR_TOPIC': 'model_error_topic'
        }
    
    async def initialize(self):
        """初始化Kafka客户端"""
        try:
            # 首先验证连接
            if not self._check_connection():
                raise ConnectionError("Cannot connect to Kafka servers")
            
            # 直接使用gzip压缩，确保兼容性
            compression_type = 'gzip'
            
            logger.info(f"Using compression type: {compression_type}")
            
            # 初始化生产者
            self.producer = KafkaProducer(
                bootstrap_servers=self.config.bootstrap_servers,
                client_id=f"{self.config.client_id}_producer",
                value_serializer=self._serialize_message,
                compression_type=compression_type,
                linger_ms=1,
                retry_backoff_ms=1000,
                request_timeout_ms=30000,
                api_version=(0, 10, 1),  # 使用固定版本，与create脚本保持一致
                security_protocol='PLAINTEXT',
            )
            logger.info(f"Kafka producer initialized successfully with servers: {self.config.bootstrap_servers}")
            
        except Exception as e:
            logger.error(f"Failed to initialize Kafka client: {e}")
            raise
    
    def _check_connection(self) -> bool:
        """检查Kafka连接"""
        try:
            from kafka import KafkaClient as KafkaClientCheck
            logger.info(f"🔍 Attempting to connect to Kafka servers: {self.config.bootstrap_servers}")
            client = KafkaClientCheck(hosts=self.config.bootstrap_servers, api_version=(0, 10, 1))
            client.check_version()
            logger.info(f"✅ Kafka connection verified: {self.config.bootstrap_servers}")
            client.close()
            return True
        except Exception as e:
            logger.error(f"❌ Cannot connect to Kafka servers: {self.config.bootstrap_servers}")
            logger.error(f"Connection error details: {e}")
            return False
    
    def _serialize_message(self, message):
        """序列化消息，支持 Pydantic 模型"""
        try:
            # 如果是 Pydantic 模型，使用 model_dump() 方法
            if hasattr(message, 'model_dump'):
                return json.dumps(message.model_dump(), ensure_ascii=False).encode('utf-8')
            # 如果是字典或其他可序列化对象
            else:
                return json.dumps(message, ensure_ascii=False).encode('utf-8')
        except Exception as e:
            logger.error(f"Message serialization failed: {e}")
            raise
    
    def create_consumer(
        self, 
        topics: List[str], 
        group_id: Optional[str] = None,
        auto_offset_reset: str = 'latest',
        enable_auto_commit: bool = False
    ) -> KafkaConsumer:
        """创建消费者 - 优化版本，支持静态成员和协作式分配"""
        consumer_group_id = group_id or self.config.group_id
        
        # 从配置文件获取消费者配置
        consumer_config = getattr(self.config, 'consumer', {})
        
        # 构建基础消费者参数
        consumer_kwargs = {
            'bootstrap_servers': self.config.bootstrap_servers,
            'client_id': f"{self.config.client_id}_consumer",
            'group_id': consumer_group_id,
            'auto_offset_reset': auto_offset_reset,
            'value_deserializer': lambda x: json.loads(x.decode('utf-8')),
            
            # 基础配置
            'enable_auto_commit': consumer_config.get('enable_auto_commit', True),
            'auto_commit_interval_ms': consumer_config.get('auto_commit_interval_ms', 5000),
            'max_poll_records': consumer_config.get('max_poll_records', 100),
            'fetch_max_wait_ms': consumer_config.get('fetch_max_wait_ms', 1000),
            
            # 关键超时配置
            'session_timeout_ms': consumer_config.get('session_timeout_ms', 90000),
            'request_timeout_ms': consumer_config.get('request_timeout_ms', 100000),
            'heartbeat_interval_ms': consumer_config.get('heartbeat_interval_ms', 15000),
            'max_poll_interval_ms': consumer_config.get('max_poll_interval_ms', 600000),
            
            # 网络重连配置
            'connections_max_idle_ms': consumer_config.get('connections_max_idle_ms', 540000),
            'retry_backoff_ms': consumer_config.get('retry_backoff_ms', 2000),
            'reconnect_backoff_ms': consumer_config.get('reconnect_backoff_ms', 1000),
            'reconnect_backoff_max_ms': consumer_config.get('reconnect_backoff_max_ms', 10000),
            
            # 协议配置
            'api_version': (0, 10, 1),
            'security_protocol': 'PLAINTEXT'
        }
        
        # 静态成员配置 - 极大降低rebalance幅度
        if consumer_config.get('enable_static_membership', False):
            # 生成唯一的group_instance_id
            if instance_id:
                consumer_kwargs['group_instance_id'] = instance_id
            else:
                import socket
                hostname = socket.gethostname()
                consumer_kwargs['group_instance_id'] = f"{consumer_group_id}_{hostname}_{id(self)}"
            logger.info(f"Static membership enabled with group_instance_id: {consumer_kwargs['group_instance_id']}")
        
        # 使用默认的range分配策略（最稳定兼容）
        
        consumer = KafkaConsumer(*topics, **consumer_kwargs)
        
        try:
            logger.info(f"Kafka consumer created for topics: {topics}")
            logger.info("使用gzip压缩，兼容性最佳")
            return consumer
        except Exception as e:
            logger.error(f"Failed to create Kafka consumer: {e}")
            raise
    
    def send_message(self, topic: str, message: Dict[str, Any], key: Optional[str] = None):
        """发送消息"""
        if not self.producer:
            raise RuntimeError("Kafka producer not initialized")
        
        try:
            # 发送消息
            future = self.producer.send(topic, value=message, key=key.encode('utf-8') if key else None)
            # 等待发送完成
            record_metadata = future.get(timeout=10)
            logger.debug(f"Message sent to topic {topic}, partition {record_metadata.partition}, offset {record_metadata.offset}")
            return record_metadata
        except Exception as e:
            logger.error(f"Failed to send message to topic {topic}: {e}")
            raise
    
    def start_consumer(
        self, 
        topics: List[str], 
        message_handler: Callable[[Dict[str, Any]], None],
        group_id: Optional[str] = None,
        consumer_name: Optional[str] = None,
        auto_offset_reset: str = 'latest',
        enable_auto_commit: bool = False
    ):
        """启动消费者线程"""
        if not consumer_name:
            consumer_name = f"consumer_{len(self.consumers)}"
        
        consumer = self.create_consumer(topics, group_id, auto_offset_reset, enable_auto_commit)
        self.consumers[consumer_name] = consumer
        
        def consume_messages():
            import asyncio
            
            # 获取消费者配置
            consumer_config = getattr(self.config, 'consumer', {})
            
            def run_async_handler(message_data, message_obj=None):
                """在同步线程中运行异步消息处理方法"""
                try:
                    # 检查 message_handler 是否为异步方法
                    if asyncio.iscoroutinefunction(message_handler):
                        # 异步方法：创建新的事件循环来运行
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        try:
                            loop.run_until_complete(message_handler(message_data))
                            # 消息处理成功后手动提交offset（如果禁用了自动提交）
                            if not consumer_config.get('enable_auto_commit', True) and message_obj:
                                consumer.commit()
                                logger.debug(f"Manual offset commit successful for {consumer_name}")
                        finally:
                            loop.close()
                    else:
                        # 同步方法：直接调用
                        message_handler(message_data)
                        # 消息处理成功后手动提交offset（如果禁用了自动提交）
                        if not consumer_config.get('enable_auto_commit', True) and message_obj:
                            consumer.commit()
                            logger.debug(f"Manual offset commit successful for {consumer_name}")
                except Exception as e:
                    logger.error(f"Error processing message in {consumer_name}: {e}")
                    # 重新抛出异常，确保外层能够捕获到处理失败
                    raise
            
            try:
                logger.info(f"Starting consumer {consumer_name} for topics: {topics}")
                for message in consumer:
                    if self._stop_consumers:
                        break
                    try:
                        run_async_handler(message.value)
                        # 如果启用手动提交，在消息处理成功后提交offset
                        if not enable_auto_commit:
                            consumer.commit()
                            logger.debug(f"Manually committed offset for message from {message.topic}")
                    except Exception as e:
                        logger.error(f"Error processing message from {message.topic}: {e}")
                        # 处理失败时不提交offset，让消息重新被消费
                        continue
            except Exception as e:
                logger.error(f"Consumer {consumer_name} error: {e}")
            finally:
                consumer.close()
                logger.info(f"Consumer {consumer_name} stopped")
        
        thread = threading.Thread(target=consume_messages, daemon=True)
        thread.start()
        self.consumer_threads[consumer_name] = thread
        logger.info(f"Consumer thread {consumer_name} started")
    
    def stop_consumer(self, consumer_name: str):
        """停止指定消费者"""
        if consumer_name in self.consumers:
            self._stop_consumers = True
            if consumer_name in self.consumer_threads:
                self.consumer_threads[consumer_name].join(timeout=5)
            del self.consumers[consumer_name]
            del self.consumer_threads[consumer_name]
            logger.info(f"Consumer {consumer_name} stopped")
    
    def stop_all_consumers(self):
        """停止所有消费者"""
        self._stop_consumers = True
        for consumer_name in list(self.consumers.keys()):
            self.stop_consumer(consumer_name)
        logger.info("All consumers stopped")
    
    def get_topic_name(self, topic_key: str) -> str:
        """获取topic名称"""
        return self.TOPICS.get(topic_key, topic_key)
    
    def send_to_voc_tomodel_topic(self, message: Dict[str, Any]):
        """发送到VOC_TOMODEL_TOPIC"""
        topic = self.get_topic_name('VOC_TOMODEL_TOPIC')
        return self.send_message(topic, message)
    
    def send_to_result_topic(self, message: Dict[str, Any]):
        """发送到RESULT_TOPIC"""
        topic = self.get_topic_name('RESULT_TOPIC')
        return self.send_message(topic, message)
    
    def send_to_text_processor_topic(self, message: Dict[str, Any]):
        """发送到TEXT_PROCESSOR_TOPIC"""
        topic = self.get_topic_name('TEXT_PROCESSOR_TOPIC')
        return self.send_message(topic, message)
    
    def send_to_rule_matcher_topic(self, message: Dict[str, Any]):
        """发送到RULE_MATCHER_TOPIC"""
        topic = self.get_topic_name('RULE_MATCHER_TOPIC')
        return self.send_message(topic, message)
    
    def send_to_vector_matcher_topic(self, message: Dict[str, Any]):
        """发送到VECTOR_MATCHER_TOPIC"""
        topic = self.get_topic_name('VECTOR_MATCHER_TOPIC')
        return self.send_message(topic, message)
    
    def send_to_uie_topic(self, message: Dict[str, Any]):
        """发送到UIE_TOPIC"""
        topic = self.get_topic_name('UIE_TOPIC')
        return self.send_message(topic, message)
    
    def send_to_llm_topic(self, message: Dict[str, Any]):
        """发送到LLM_TOPIC"""
        topic = self.get_topic_name('LLM_TOPIC')
        return self.send_message(topic, message)
    
    def send_to_intent_topic(self, message: Dict[str, Any]):
        """发送到INTENT_TOPIC"""
        topic = self.get_topic_name('INTENT_TOPIC')
        return self.send_message(topic, message)
    
    def send_to_brand_attribution_topic(self, message: Dict[str, Any]):
        """发送到BRAND_ATTRIBUTION_TOPIC"""
        topic = self.get_topic_name('BRAND_ATTRIBUTION_TOPIC')
        return self.send_message(topic, message)
    
    def send_to_error_topic(self, message: Dict[str, Any]):
        """发送到错误 topic"""
        topic = self.get_topic_name('MODEL_ERROR_TOPIC')
        return self.send_message(topic, message)
    
    def send_to_retry_topic(self, message: Dict[str, Any]):
        """发送到重试 topic"""
        topic = self.get_topic_name('MODEL_RETRY_TOPIC')
        return self.send_message(topic, message)
    
    def send_to_final_topic(self, message: Dict[str, Any]):
        """发送到FINAL_TOPIC"""
        topic = self.get_topic_name('FINAL_TOPIC')
        return self.send_message(topic, message)
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            if self.producer:
                # 测试生产者连接
                metadata = self.producer.list_topics(timeout=5)
                return {
                    "status": "healthy",
                    "producer": "connected",
                    "topics": list(metadata.topics.keys()),
                    "consumers": list(self.consumers.keys())
                }
            else:
                return {
                    "status": "unhealthy",
                    "error": "Producer not initialized"
                }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }
    
    async def close(self):
        """关闭客户端"""
        try:
            self.stop_all_consumers()
            if self.producer:
                self.producer.close()
                logger.info("Kafka client closed")
        except Exception as e:
            logger.error(f"Error closing Kafka client: {e}")


# 全局客户端实例
_kafka_client = None


def get_kafka_client() -> KafkaClient:
    """获取Kafka客户端实例"""
    global _kafka_client
    if _kafka_client is None:
        from shared.utils.config import get_config_manager
        config = get_config_manager().get_kafka_config()
        _kafka_client = KafkaClient(config)
        # 注意：这里不调用 initialize()，因为它是异步的
        # 调用者需要自己调用 await client.initialize()
    return _kafka_client


async def get_initialized_kafka_client() -> KafkaClient:
    """获取已初始化的Kafka客户端实例"""
    global _kafka_client
    if _kafka_client is None:
        from shared.utils.config import get_config_manager
        config = get_config_manager().get_kafka_config()
        _kafka_client = KafkaClient(config)
        await _kafka_client.initialize()
    return _kafka_client


def send_message(topic: str, message: Dict[str, Any], key: Optional[str] = None):
    """便捷的发送消息函数"""
    client = get_kafka_client()
    return client.send_message(topic, message, key)


def send_to_result(message: Dict[str, Any]):
    """便捷的发送到结果topic函数"""
    client = get_kafka_client()
    return client.send_to_result(message)


# 示例使用
def main():
    """测试示例"""
    try:
        client = get_kafka_client()
        
        # 测试健康检查
        health = client.health_check()
        print(f"Health status: {health}")
        
        if health["status"] == "healthy":
            # 测试发送消息
            test_message = {"test": "message", "timestamp": "2024-01-01"}
            client.send_to_result(test_message)
            print("✅ Test message sent successfully")
        else:
            print(f"❌ Kafka client is not healthy: {health.get('error', 'Unknown error')}")
        
        # 关闭客户端
        client.close()
        
    except Exception as e:
        print(f"❌ Test failed: {e}")


if __name__ == "__main__":
    main()