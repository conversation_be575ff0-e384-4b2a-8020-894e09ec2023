# VOC2.0 SCALED 环境 Docker Compose 配置
# 自动生成，请勿手动修改！
# 生成命令: python scripts/generate_compose.py scaled scaled
# 配置来源: configs/config.yaml

version: '3.8'
networks:
  voc-network-scaled:
    driver: bridge
volumes:
  starrocks-fe-data-scaled: null
  starrocks-be-data-scaled: null
  elasticsearch-data-scaled: null
  zookeeper-data-scaled: null
  zookeeper-logs-scaled: null
  kafka-data-scaled: null
services:
  starrocks-fe:
    image: starrocks/fe-ubuntu:latest
    container_name: starrocks-fe-scaled
    hostname: starrocks-fe
    ports:
    - 8030:8030
    - 9020:9020
    - 9030:9030
    environment:
    - FE_SERVERS=starrocks-fe:9010
    volumes:
    - starrocks-fe-data-scaled:/opt/starrocks/fe/meta
    networks:
    - voc-network-scaled
    restart: unless-stopped
  starrocks-be:
    image: starrocks/be-ubuntu:latest
    container_name: starrocks-be-scaled
    hostname: starrocks-be
    ports:
    - 8040:8040
    - 9060:9060
    - 8050:8050
    environment:
    - FE_SERVERS=starrocks-fe:9010
    volumes:
    - starrocks-be-data-scaled:/opt/starrocks/be/storage
    networks:
    - voc-network-scaled
    depends_on:
    - starrocks-fe
    restart: unless-stopped
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: elasticsearch-scaled
    environment:
    - node.name=elasticsearch
    - cluster.name=es-docker-cluster
    - discovery.type=single-node
    - '"ES_JAVA_OPTS=-Xms1g -Xmx1g"'
    - xpack.security.enabled=false
    - xpack.security.enrollment.enabled=false
    ports:
    - 9200:9200
    - 9300:9300
    volumes:
    - elasticsearch-data-scaled:/usr/share/elasticsearch/data
    networks:
    - voc-network-scaled
    restart: unless-stopped
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: zookeeper-scaled
    environment:
    - ZOOKEEPER_CLIENT_PORT=2181
    - ZOOKEEPER_TICK_TIME=2000
    volumes:
    - zookeeper-data-scaled:/var/lib/zookeeper/data
    - zookeeper-logs-scaled:/var/lib/zookeeper/log
    networks:
    - voc-network-scaled
    restart: unless-stopped
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: kafka-scaled
    depends_on:
    - zookeeper
    ports:
    - 9092:9092
    - 29092:29092
    environment:
    - KAFKA_BROKER_ID=1
    - KAFKA_ZOOKEEPER_CONNECT=zookeeper:2181
    - KAFKA_LISTENER_SECURITY_PROTOCOL_MAP=PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
    - KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
    - KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR=1
    - KAFKA_TRANSACTION_STATE_LOG_MIN_ISR=1
    - KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR=1
    - KAFKA_AUTO_CREATE_TOPICS_ENABLE=true
    - KAFKA_NUM_PARTITIONS=3
    volumes:
    - kafka-data-scaled:/var/lib/kafka/data
    networks:
    - voc-network-scaled
    restart: unless-stopped
  text-processor-scaled:
    image: voc-services:latest
    container_name: voc-text-processor-scaled
    ports:
    - 8200:8200
    environment:
    - SERVICE_NAME=text-processor
    - SERVICE_PORT=8200
    - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
    - ELASTICSEARCH_URL=http://elasticsearch:9200
    - STARROCKS_HOST=starrocks-fe
    - STARROCKS_PORT=9030
    - STARROCKS_USER=root
    - STARROCKS_PASSWORD=
    - PYTHONPATH=/app
    volumes:
    - ./configs:/app/configs
    - ./logs/scaled:/app/logs
    networks:
    - voc-network-scaled
    depends_on:
    - kafka
    - elasticsearch
    - starrocks-fe
    restart: unless-stopped
  rule-matcher-scaled:
    image: voc-services:latest
    container_name: voc-rule-matcher-scaled
    ports:
    - 8110:8110
    environment:
    - SERVICE_NAME=rule-matcher
    - SERVICE_PORT=8110
    - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
    - ELASTICSEARCH_URL=http://elasticsearch:9200
    - STARROCKS_HOST=starrocks-fe
    - STARROCKS_PORT=9030
    - STARROCKS_USER=root
    - STARROCKS_PASSWORD=
    - PYTHONPATH=/app
    volumes:
    - ./configs:/app/configs
    - ./logs/scaled:/app/logs
    networks:
    - voc-network-scaled
    depends_on:
    - kafka
    - elasticsearch
    - starrocks-fe
    restart: unless-stopped
  vector-matcher-scaled:
    image: voc-services:latest
    container_name: voc-vector-matcher-scaled
    ports:
    - 8120:8120
    environment:
    - SERVICE_NAME=vector-matcher
    - SERVICE_PORT=8120
    - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
    - ELASTICSEARCH_URL=http://elasticsearch:9200
    - STARROCKS_HOST=starrocks-fe
    - STARROCKS_PORT=9030
    - STARROCKS_USER=root
    - STARROCKS_PASSWORD=
    - PYTHONPATH=/app
    volumes:
    - ./configs:/app/configs
    - ./logs/scaled:/app/logs
    - ./models:/app/models
    networks:
    - voc-network-scaled
    depends_on:
    - kafka
    - elasticsearch
    - starrocks-fe
    restart: unless-stopped
  uie-scaled:
    image: voc-services:latest
    container_name: voc-uie-scaled
    ports:
    - 8140:8140
    environment:
    - SERVICE_NAME=uie
    - SERVICE_PORT=8140
    - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
    - ELASTICSEARCH_URL=http://elasticsearch:9200
    - STARROCKS_HOST=starrocks-fe
    - STARROCKS_PORT=9030
    - STARROCKS_USER=root
    - STARROCKS_PASSWORD=
    - PYTHONPATH=/app
    - UIE_API_URL=http://*************:5000/ppuie
    volumes: &id001
    - ./configs:/app/configs
    - ./logs/scaled:/app/logs
    networks: &id002
    - voc-network-scaled
    depends_on: &id003
    - kafka
    - elasticsearch
    - starrocks-fe
    restart: unless-stopped
  llm-scaled:
    image: voc-services:latest
    container_name: voc-llm-scaled
    ports:
    - 8150:8150
    environment:
    - SERVICE_NAME=llm
    - SERVICE_PORT=8150
    - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
    - ELASTICSEARCH_URL=http://elasticsearch:9200
    - STARROCKS_HOST=starrocks-fe
    - STARROCKS_PORT=9030
    - STARROCKS_USER=root
    - STARROCKS_PASSWORD=
    - PYTHONPATH=/app
    - LLM_API_URL=http://localhost:8001/v1/chat/completions
    volumes:
    - ./configs:/app/configs
    - ./logs/scaled:/app/logs
    networks:
    - voc-network-scaled
    depends_on:
    - kafka
    - elasticsearch
    - starrocks-fe
    restart: unless-stopped
  intent-scaled:
    image: voc-services:latest
    container_name: voc-intent-scaled
    ports:
    - 8160:8160
    environment:
    - SERVICE_NAME=intent
    - SERVICE_PORT=8160
    - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
    - ELASTICSEARCH_URL=http://elasticsearch:9200
    - STARROCKS_HOST=starrocks-fe
    - STARROCKS_PORT=9030
    - STARROCKS_USER=root
    - STARROCKS_PASSWORD=
    - PYTHONPATH=/app
    - INTENT_API_URL=http://*************:5001/analyze
    volumes:
    - ./configs:/app/configs
    - ./logs/scaled:/app/logs
    networks:
    - voc-network-scaled
    depends_on:
    - kafka
    - elasticsearch
    - starrocks-fe
    restart: unless-stopped
  error-handler-scaled:
    image: voc-services:latest
    container_name: voc-error-handler-scaled
    ports:
    - 8130:8130
    environment:
    - SERVICE_NAME=error-handler
    - SERVICE_PORT=8130
    - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
    - ELASTICSEARCH_URL=http://elasticsearch:9200
    - STARROCKS_HOST=starrocks-fe
    - STARROCKS_PORT=9030
    - STARROCKS_USER=root
    - STARROCKS_PASSWORD=
    - PYTHONPATH=/app
    volumes:
    - ./configs:/app/configs
    - ./logs/scaled:/app/logs
    networks:
    - voc-network-scaled
    depends_on:
    - kafka
    - elasticsearch
    - starrocks-fe
    restart: unless-stopped
  gateway-scaled:
    image: voc-services:latest
    container_name: voc-gateway-scaled
    ports:
    - 8100:8100
    environment:
    - SERVICE_NAME=gateway
    - SERVICE_PORT=8100
    - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
    - ELASTICSEARCH_URL=http://elasticsearch:9200
    - STARROCKS_HOST=starrocks-fe
    - STARROCKS_PORT=9030
    - STARROCKS_USER=root
    - STARROCKS_PASSWORD=
    - PYTHONPATH=/app
    volumes:
    - ./configs:/app/configs
    - ./logs/scaled:/app/logs
    networks:
    - voc-network-scaled
    depends_on:
    - kafka
    - elasticsearch
    - starrocks-fe
    restart: unless-stopped
  brand-attribution-scaled:
    image: voc-services:latest
    container_name: voc-brand-attribution-scaled
    ports:
    - 8170:8170
    environment:
    - SERVICE_NAME=brand-attribution
    - SERVICE_PORT=8170
    - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
    - ELASTICSEARCH_URL=http://elasticsearch:9200
    - STARROCKS_HOST=starrocks-fe
    - STARROCKS_PORT=9030
    - STARROCKS_USER=root
    - STARROCKS_PASSWORD=
    - PYTHONPATH=/app
    volumes:
    - ./configs:/app/configs
    - ./logs/scaled:/app/logs
    networks:
    - voc-network-scaled
    depends_on:
    - kafka
    - elasticsearch
    - starrocks-fe
    restart: unless-stopped
  post-processor-scaled:
    image: voc-services:latest
    container_name: voc-post-processor-scaled
    ports:
    - 8180:8180
    environment:
    - SERVICE_NAME=post-processor
    - SERVICE_PORT=8180
    - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
    - ELASTICSEARCH_URL=http://elasticsearch:9200
    - STARROCKS_HOST=starrocks-fe
    - STARROCKS_PORT=9030
    - STARROCKS_USER=root
    - STARROCKS_PASSWORD=
    - PYTHONPATH=/app
    volumes:
    - ./configs:/app/configs
    - ./logs/scaled:/app/logs
    networks:
    - voc-network-scaled
    depends_on:
    - kafka
    - elasticsearch
    - starrocks-fe
    restart: unless-stopped
  uie-scaled-2:
    image: voc-services:latest
    container_name: voc-uie-scaled-2
    ports:
    - 8141:8140
    environment:
    - SERVICE_NAME=uie
    - SERVICE_PORT=8140
    - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
    - ELASTICSEARCH_URL=http://elasticsearch:9200
    - STARROCKS_HOST=starrocks-fe
    - STARROCKS_PORT=9030
    - STARROCKS_USER=root
    - STARROCKS_PASSWORD=
    - PYTHONPATH=/app
    - UIE_API_URL=http://*************:5001/ppuie
    volumes: *id001
    networks: *id002
    depends_on: *id003
    restart: unless-stopped
  uie-scaled-3:
    image: voc-services:latest
    container_name: voc-uie-scaled-3
    ports:
    - 8142:8140
    environment:
    - SERVICE_NAME=uie
    - SERVICE_PORT=8140
    - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
    - ELASTICSEARCH_URL=http://elasticsearch:9200
    - STARROCKS_HOST=starrocks-fe
    - STARROCKS_PORT=9030
    - STARROCKS_USER=root
    - STARROCKS_PASSWORD=
    - PYTHONPATH=/app
    - UIE_API_URL=http://*************:5002/ppuie
    volumes: *id001
    networks: *id002
    depends_on: *id003
    restart: unless-stopped
