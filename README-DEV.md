# VOC2.0 开发环境部署指南

## 🎯 开发环境优势

- **代码挂载**：修改代码后容器自动重启，无需重新构建镜像
- **基础镜像**：环境依赖只需构建一次，大幅节省开发时间
- **即时调试**：代码修改立即生效，支持快速迭代

## 📋 部署步骤

### 1. 构建基础环境镜像（只需一次）

```bash
# 构建包含所有依赖的基础镜像（不使用缓存）
docker build --no-cache -f Dockerfile.base -t voc-base:latest .
```

### 2. 启动开发环境

```bash
# 停止旧环境（如果存在）
docker compose -f docker-compose.dev.yml down

# 启动开发环境
docker compose -f docker-compose.dev.yml up -d
```

### 3. 查看服务状态

```bash
# 查看所有服务状态
docker compose -f docker-compose.dev.yml ps

# 查看特定服务日志
docker compose -f docker-compose.dev.yml logs -f gateway-dev
```

## 🔧 日常开发命令

```bash
# 重启特定服务（代码修改后）
docker compose -f docker-compose.dev.yml restart uie-dev

# 查看服务日志
docker compose -f docker-compose.dev.yml logs -f brand-attribution-dev

# 进入容器调试
docker exec -it voc-uie-dev bash

# 停止所有服务
docker compose -f docker-compose.dev.yml down

# 完全清理（包括网络和卷）
docker compose -f docker-compose.dev.yml down -v --remove-orphans
```

## 📊 服务端口映射

| 服务 | 端口 | 访问地址 |
|------|------|----------|
| API网关 | 8100 | http://localhost:8100 |
| 文本处理 | 8200 | http://localhost:8200 |
| 规则匹配 | 8110 | http://localhost:8110 |
| 向量匹配 | 8120 | http://localhost:8120 |
| 错误处理 | 8130 | http://localhost:8130 |
| UIE服务 | 8140 | http://localhost:8140 |
| LLM服务 | 8150 | http://localhost:8150 |
| 意图分析 | 8160 | http://localhost:8160 |
| 品牌归属 | 8170 | http://localhost:8170 |

## 🚀 快速开始

### 统一部署入口 (推荐)

```bash
# 一键部署 - 开发环境
./scripts/deploy-env.sh dev

# 一键部署 - 生产环境  
./scripts/deploy-env.sh prod

# 一键部署 - 扩展环境
./scripts/deploy-env.sh scaled
```

### 单独部署脚本

```bash
# 开发环境 - 代码挂载
./scripts/build-dev-env.sh

# 生产环境 - 完整基础设施
./scripts/build-prod-env.sh

# 扩展部署 - 多实例高并发
./scripts/build-scaled-env.sh
```

## 📝 注意事项

1. **代码挂载**：代码目录以只读方式挂载，确保容器内不会意外修改源码
2. **日志目录**：日志写入 `./logs/dev/` 目录，便于查看和调试
3. **配置文件**：配置文件也是挂载模式，修改后需重启对应服务
4. **依赖更新**：如果 `requirements.txt` 发生变化，需要重新构建基础镜像

## 🔄 更新依赖

如果需要添加新的Python依赖：

```bash
# 1. 更新 requirements.txt
echo "new-package==1.0.0" >> requirements.txt

# 2. 重新构建基础镜像（不使用缓存）
docker build --no-cache -f Dockerfile.base -t voc-base:latest .

# 3. 重启服务
docker compose -f docker-compose.dev.yml down
docker compose -f docker-compose.dev.yml up -d
```

## 🐛 故障排除

```bash
# 查看所有容器状态
docker ps -a

# 查看镜像大小
docker images | grep voc

# 清理无用镜像和容器
docker system prune -f

# 重新构建并启动（不使用缓存）
docker build --no-cache -f Dockerfile.base -t voc-base:latest . && docker compose -f docker-compose.dev.yml up -d --force-recreate
```

## 📦 扩展部署说明

### 🎯 扩展部署特点

- **单实例启动**：默认每个服务启动一个实例
- **多实例支持**：核心AI服务（UIE、LLM、Intent）支持扩展多实例
- **注释配置**：扩展实例以注释形式提供，按需开启

### 🚀 启动扩展部署

```bash
# 1. 构建生产镜像（如果需要）
docker build --no-cache -t voc-services:latest .

# 2. 启动扩展部署
docker compose -f docker-compose.scaled.yml up -d

# 3. 查看服务状态
docker compose -f docker-compose.scaled.yml ps

# 4. 查看日志
docker compose -f docker-compose.scaled.yml logs -f [service-name]

# 5. 停止扩展部署
docker compose -f docker-compose.scaled.yml down
```

### 📋 端口分配

| 服务 | 开发环境端口 | 扩展部署端口 | 多实例端口 |
|------|-------------|-------------|-----------|
| Gateway | 8100 | 8100 | - |
| Text Processor | 8200 | 8200 | - |
| Rule Matcher | 8110 | 8300 | - |
| Vector Matcher | 8120 | 8400 | - |
| UIE | 8140 | 8140 | 8141, 8142 |
| LLM | 8150 | 8500 | 8501 |
| Intent | 8160 | 8600 | 8601 |
| Brand Attribution | 8170 | 8700 | - |
| Error Handler | 8130 | 8800 | - |

### 🔧 开启多实例

如需开启多实例，编辑 `docker-compose.scaled.yml`：

1. **取消注释**对应的服务配置
2. **确保外部API**有对应的多个实例
3. **重新启动**服务

```bash
# 例：开启UIE第二个实例
# 1. 编辑 docker-compose.scaled.yml，取消注释 uie-scaled-2
# 2. 确保外部API 5001端口可用
# 3. 重启服务
docker compose -f docker-compose.scaled.yml up -d uie-scaled-2
```

### ⚠️ 多实例注意事项

- **UIE服务**：需要外部API支持多个端口（5000, 5001, 5002）
- **LLM服务**：确保模型资源充足
- **Intent服务**：通常单实例即可满足需求
- **Kafka分区**：多实例时建议增加Topic分区数
