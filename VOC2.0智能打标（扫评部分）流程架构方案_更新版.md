# 数据库明细：
##### 品牌车系车型数据库——中间包含品牌车系和品牌车系的近义词等
例如：品牌：长安

车系：长安UNI-Z

车型：

2025款 500Bar MAX

2025款 1.5T 尊贵型

##### 长安标准观点库——目前为 1.1w，是长安用来直接映射不同业务部门不同标签的库，长安通过标准观点梳理映射到比业务部门的标签上（纯人工）
##### 观点近义词库——VOC2.0 新增库，类似于 1.0 的非规范观点库，再模型识别不到的时候可以通过长安的运维人员手动往里面添加，也会继承部分 1.0 的非规范观点，并且由长安维护如何映射到长安标准观点库上面。该库在存入的时候强制要求分为主体和描述分别录入，我们默认属于同一规范观点的所有观点近义词中的主体为主体近义词，同理描述也是一样，每个近义主体或描述都可以归一到一个标准主体和标准描述上面
表结构为：

Col: Add_entity | Add_description | Standard_opinion | Standard_opinion_id |

##### 主体近义词库——VOC2.0 新增库，为观点近义词库拆分后得到，通过表映射到标准主体，从而映射到长安标准观点库，注意，因为标准观点可能存在多个近似主体（每个主体对应多个标准观点），例如车灯不好看，大灯很好看，这属于两个标准观点，但实际主体是一致的，所以允许主体近义词对标准观点是一对多的关系
表结构为：

Col: Entity_id | Sim_entity | vector | Normoalized_entity | Standard_opinion_id_list |

##### 描述近义词库——VOC2.0 新增库，为观点近义词库拆分后得到，通过表映射到标准描述，从而映射到长安标准观点库，注意，与主体近义词库不同，不允许一对多的关系，哪怕有重复（完全重复的不同主体对应的相同描述，例子如下：大灯难看，内饰难看）的描述，也可通过Description_id 和 Standard_opinion_id 分辨
表结构为：

Col: Description_id | Sim_description | vector | Normoalized_description | Standard_opinion_id |

##### 新词库——VOC2.0 新增库，存放模糊匹配没有匹配成功的主体+描述
表结构为：

Col: New_Entity | New_description | 

##### 错误库——VOC2.0 新增库，存放超过五次回滚仍然处理失败的数据，比起 fakfa 数据多增加一个 Final_status，存放最终处理结果（已修改/跳过/人工维护）
表结构为：

Col: Topic_id | Topic_text | Retry_count | Error_type | Last_ts | Final_status

其中，需要向量化的库有：标准观点库、主体近义词库、描述近义词库



# 需要用到的词表有：
##### 否定词表


# Kafka Topic 数量：
| 序号 | Topic 名 | 用途说明 |
| --- | --- | --- |
| 1️⃣ | `voc_toModel_topic` | 原始任务投递入口，主流程起点 |
| 2️⃣ | `uie_topic` | 小模型 UIE 的处理任务 |
| 3️⃣ | `llm_topic` | 大模型（LLM）处理任务 |
| 4️⃣ | `model_error_topic` | 所有模型失败任务进入此统一错误 topic |
| 5️⃣ | `model_retry_topic` | retry processor 回退任务的调度入口 |
| 6️⃣ | `result_topic` | **模型输出观点结构化结果的中转 topic**（下游回评/问数等依赖） |
| 7️⃣ | `intent_topic` | **情感/意图识别后的结构化输出 topic**（用于最终结构化、建标签、RAG召回等） |
| 8️⃣ | `brand_attribution_topic` | **影响域判断车系** |


# 共性流程：
##### 规则模型：
处理的时候首先接入主体近义词库，通过进行精确匹配，匹配逻辑为如果精确匹配到某个主体，则直接找到对应的标准主体，然后找到长安标准观点（可能是多个），到这步后，通过Standard_opinion_id_list 中的 Standard_opinion_id 去描述近义词库搜寻该标准观点下面的所有描述，包含近义描述和标准描述，如果均匹配到了，如果两者的距离在某一距离内（例如 10 或者 8 等），并且中间没有否定词，没有标点符号，则认为匹配成功，输出主体+描述 （具体规则待添加更细致的）

##### 模糊匹配：
该模块的输入是主体+描述，通过嵌入主体向量化，算与主体近义词库中 vector 的相似度，召回匹配到的标准主体或近义主体（TOP 1），然后找到长安标准观点（可能是多个），到这步后，通过Standard_opinion_id_list 中的 Standard_opinion_id 去描述近义词库搜寻该标准观点下面的所有描述，，将描述嵌入与该标准观点下面的所有描述的 vector 进行相似度召回，如果均匹配成功则归一到标准主体+标准描述后输出，如果匹配失败则进入新词库，输出匹配到的原文主体+原文描述以及标准观点



# 具体智能打标流程：
##### 粗分句+分流：原始语句进来后，进行粗分句，这里对接第一个数据库——品牌车系数据库如果匹配到的车系大于等于3个，则分到大模型处理的kafka topic上面，如果小于三个，则分到UIE的topic上面，注意传到大模型处理和 UIE 的都是传整段话过去，放在一个 json 里面。如果崔总的 kafka topic 消费完成了并且 10 分钟内没有新的 json 进来，则开始消费 model_retry_topic
输入：

```json
[{
  "topic_id": "0000001",
  "source_data": {
    "dataSource": "汽车之家",
    "domainListJson": ["汽车","客服","售后"],
    "productName": "长安CS75 PLUS",
    "populationGroup": "25-35岁男性",
    "businessType": "产品评价",
    "businessScene": "购车前调研",
    "textType": 1,
    "title": "CS75 PLUS使用一个月感受",
    "topic_text": "这个车的大灯真的很亮，晚上开车很安全，但是内饰的塑料感有点强，奇瑞的要好点",
    "optionScore": {"overall":8,"appearance":9,"interior":6},
    "position": 1,
    "forumSeries": "CS75系列",
    "brand": "长安",
    "series": "CS75 PLUS",
    "create_time": "2025-08-06 12:00:00"
  }
}]
```

```json
{
  "topic_id": "0000001",
  "source_data":  {
        "dataSource": "汽车之家",
        "domainListJson": ["汽车","客服","售后"],
        "productName": "长安CS75 PLUS",
        "populationGroup": "25-35岁男性",
        "businessType": "产品评价",
        "businessScene": "购车前调研",
        "textType": 1,
        "title": "CS75 PLUS使用一个月感受",
        "topic_text": "这个车的大灯真的很亮，晚上开车很安全，但是内饰的塑料感有点强，奇瑞的要好点",
        "optionScore": {"overall":8,"appearance":9,"interior":6},
        "position": 1,
        "forumSeries": "CS75系列",
        "brand": "长安",
        "series": "CS75 PLUS",
        "create_time": "2025-08-06 12:00:00",
    },
  "retry_count":1,
  "error_type": "model_timeout",
  "error_msg":"TimeoutError: inference exceeded 10s",
  "last_ts":"2025-07-31T17:20:00"
}
```

输出：

```json
{
  "topic_id": "0000001",
  "source_data": {
    "dataSource": "汽车之家",
    "domainListJson": ["汽车","客服","售后"],
    "productName": "长安CS75 PLUS",
    "populationGroup": "25-35岁男性",
    "businessType": "产品评价",
    "businessScene": "购车前调研",
    "textType": 1,
    "title": "CS75 PLUS使用一个月感受",
    "topic_text": "这个车的大灯真的很亮，晚上开车很安全，但是内饰的塑料感有点强，奇瑞的要好点",
    "optionScore": {"overall":8,"appearance":9,"interior":6},
    "position": 1,
    "forumSeries": "CS75系列",
    "brand": "长安",
    "series": "CS75 PLUS",
    "create_time": "2025-08-06 12:00:00"
  },
  "raw_split_text": [],
  "cat_type": "llm",
  "retry_count": 1,
  "error_type": "model_timeout",
  "error_msg": "TimeoutError: inference exceeded 10s",
  "last_ts": "2025-07-31T17:20:00"
}
```

```json
{
  "topic_id": "0000001",
  "source_data": {
    "dataSource": "汽车之家",
    "domainListJson": ["汽车","客服","售后"],
    "productName": "长安CS75 PLUS",
    "populationGroup": "25-35岁男性",
    "businessType": "产品评价",
    "businessScene": "购车前调研",
    "textType": 1,
    "title": "CS75 PLUS使用一个月感受",
    "topic_text": "这个车的大灯真的很亮，晚上开车很安全，但是内饰的塑料感有点强，奇瑞的要好点",
    "optionScore": {"overall":8,"appearance":9,"interior":6},
    "position": 1,
    "forumSeries": "CS75系列",
    "brand": "长安",
    "series": "CS75 PLUS",
    "create_time": "2025-08-06 12:00:00"
  },
  "raw_split_text": [
    "这个车的大灯屌爆了，就是内饰有点丑。",
    "还是吉利星越内饰好看，就是破产了很可惜"
  ],
  "cat_type": "uie",
  "retry_count": 1,
  "error_type": "model_timeout",
  "error_msg": "TimeoutError: inference exceeded 10s",
  "last_ts": "2025-07-31T17:20:00"
}
```

##### 大模型全流程：对于大模型的topic，首先将整句话经过规则模型，匹配到所有的主体+描述后，作为 prompt 的一部分给大模型，要求大模型抽取是否还有其余没有抽到的主体+描述，大模型的输出为：全量的主体+描述，并且输出对应原文 segment，原文 segment 的意图，segment 的情感极性；其中这些主体+描述进入模糊匹配模块，输出标准观点+标准描述，和规则模型输出的去重后输出，将最终结果输出到打标结果 result_topic
输入：

```json
{
  "topic_id": "0000001",
  "source_data": {
    "dataSource": "汽车之家",
    "domainListJson": ["汽车","客服","售后"],
    "productName": "长安CS75 PLUS",
    "populationGroup": "25-35岁男性",
    "businessType": "产品评价",
    "businessScene": "购车前调研",
    "textType": 1,
    "title": "CS75 PLUS使用一个月感受",
    "topic_text": "这个车的大灯真的很亮，晚上开车很安全，但是内饰的塑料感有点强，奇瑞的要好点",
    "optionScore": {"overall":8,"appearance":9,"interior":6},
    "position": 1,
    "forumSeries": "CS75系列",
    "brand": "长安",
    "series": "CS75 PLUS",
    "create_time": "2025-08-06 12:00:00",
    
  },
  "raw_split_text": [],
  "cat_type": "llm",
  "retry_count": 1,
  "error_type": "model_timeout",
  "error_msg": "TimeoutError: inference exceeded 10s",
  "last_ts": "2025-07-31T17:20:00"
}
```

输出：

```json
{
  "topic_id": "0000001",
  "source_data":  {
        "dataSource": "汽车之家",
        "domainListJson": ["汽车","客服","售后"],
        "productName": "长安CS75 PLUS",
        "populationGroup": "25-35岁男性",
        "businessType": "产品评价",
        "businessScene": "购车前调研",
        "textType": 1,
        "title": "CS75 PLUS使用一个月感受",
        "topic_text": "这个车的大灯真的很亮，晚上开车很安全，但是内饰的塑料感有点强，奇瑞的要好点",
        "optionScore": {"overall":8,"appearance":9,"interior":6},
        "position": 1,
        "forumSeries": "CS75系列",
        "brand": "长安",
        "series": "CS75 PLUS",
        "create_time": "2025-08-06 12:00:00"
    },
  "cat_type": "llm",
  "retry_count":1,
  "error_type": "model_timeout",
  "error_msg":"TimeoutError: inference exceeded 10s",
  "last_ts":"2025-07-31T17:20:00",
  "result": [
    {
      "评价对象": [
        {
          "relations": {
            "segment": [
              {
                "text": "这个车的大灯屌爆了"
              }
            ],
            "品牌车系": [
              {
                "text": "长安无车系"
              }
            ],
            "情感倾向": [
              {
                "text": "正面"
              }
            ],
            "意图": [
              {
                "text": "表扬"
              }
            ],
            "观点": [
              {
                "text": "屌爆了"
              }
            ]
          },
          "text": "大灯"
        },
        {
          "relations": {
            "segment": [
              {
                "text": "就是内饰有点丑"
              }
            ],
            "品牌车系": [
              {
                "text": "长安无车系"
              }
            ],
            "情感倾向": [
              {
                "text": "负面"
              }
            ],
            "意图": [
              {
                "text": "抱怨"
              }
            ],
            "观点": [
              {
                "text": "丑"
              }
            ]
          },
          "text": "内饰"
        },
        {
          "relations": {
            "segment": [
              {
                "text": "还是吉利星越内饰好看"
              }
            ],
            "品牌车系": [
              {
                "text": "吉利星越"
              }
            ],
            "情感倾向": [
              {
                "text": "正面"
              }
            ],
            "意图": [
              {
                "text": "表扬"
              }
            ],
            "观点": [
              {
                "text": "好看"
              }
            ]
          },
          "text": "内饰"
        }
      ]
    }
  ]
}
```

##### UIE 到意图前：对于 UIE 的 topic，将片段经过规则模型，匹配到所有主体+描述后，等待 UIE 的结果，UIE 负责抽取主体+描述，输出的主体+描述进入模糊匹配模块，和规则模型输出的去重后输出标准主体+标准描述以及原文主体+原文描述，送入 意图情感识别 topic
输入：

```json
{
  "topic_id": "0000001",
  "source_data": {
    "dataSource": "汽车之家",
    "domainListJson": ["汽车","客服","售后"],
    "productName": "长安CS75 PLUS",
    "populationGroup": "25-35岁男性",
    "businessType": "产品评价",
    "businessScene": "购车前调研",
    "textType": 1,
    "title": "CS75 PLUS使用一个月感受",
    "topic_text": "这个车的大灯真的很亮，晚上开车很安全，但是内饰的塑料感有点强，奇瑞的要好点",
    "optionScore": {"overall":8,"appearance":9,"interior":6},
    "position": 1,
    "forumSeries": "CS75系列",
    "brand": "长安",
    "series": "CS75 PLUS",
    "create_time": "2025-08-06 12:00:00"
  },
  "raw_split_text": [
    "这个车的大灯屌爆了，就是内饰有点丑。",
    "还是吉利星越内饰好看，就是破产了很可惜"
  ],
  "cat_type": "uie",
  "retry_count": 1,
  "error_type": "model_timeout",
  "error_msg": "TimeoutError: inference exceeded 10s",
  "last_ts": "2025-07-31T17:20:00"
}
```

输出：

```json
{
  "topic_id": "0000001",
  "source_data": {
    "dataSource": "汽车之家",
    "domainListJson": ["汽车","客服","售后"],
    "productName": "长安CS75 PLUS",
    "populationGroup": "25-35岁男性",
    "businessType": "产品评价",
    "businessScene": "购车前调研",
    "textType": 1,
    "title": "CS75 PLUS使用一个月感受",
    "topic_text": "这个车的大灯真的很亮，晚上开车很安全，但是内饰的塑料感有点强，奇瑞的要好点",
    "optionScore": {"overall":8,"appearance":9,"interior":6},
    "position": 1,
    "forumSeries": "CS75系列",
    "brand": "长安",
    "series": "CS75 PLUS",
    "create_time": "2025-08-06 12:00:00"
  },
  "cat_type": "uie",
  "retry_count": 1,
  "error_type": "model_timeout",
  "error_msg": "TimeoutError: inference exceeded 10s",
  "last_ts": "2025-07-31T17:20:00",
  "result": [
    {
      "评价对象": [
        {
          "relations": {
            "segment": [
              {
                "text": ""
              }
            ],
            "品牌车系": [
              {
                "text": ""
              }
            ],
            "情感倾向": [
              {
                "text": ""
              }
            ],
            "意图": [
              {
                "text": ""
              }
            ],
            "观点": [
              {
                "text": "屌爆了",
                "start": 6,
                "end": 9
              }
            ],
            "normalized_subject": {
              "id": "SUBJ_000123",
              "text": "大灯",
              "score": 0.92
            },
            "normalized_description": {
              "id": "DESC_100045",
              "text": "好看",
              "score": 0.90
            },
            "normalized_opinion": {
              "id": "OP_00000027",
              "text": "大灯-好看"
            },
            "std_viewpoint": {
              "id": "CA_STD_011234",
              "text": "大灯好看",
              "confidence": 0.87
            }
          },
          "text": "大灯",
          "start": 4,
          "end": 6
        },
        {
          "relations": {
            "segment": [
              {
                "text": ""
              }
            ],
            "品牌车系": [
              {
                "text": ""
              }
            ],
            "情感倾向": [
              {
                "text": ""
              }
            ],
            "意图": [
              {
                "text": ""
              }
            ],
            "观点": [
              {
                "text": "丑",
                "start": 6,
                "end": 7
              }
            ],
            "normalized_subject": {
              "id": "SUBJ_000210",
              "text": "内饰",
              "score": 0.94
            },
            "normalized_description": {
              "id": "DESC_200012",
              "text": "不好看",
              "score": 0.89
            },
            "normalized_opinion": {
              "id": "OP_00009001",
              "text": "内饰-不好看"
            },
            "std_viewpoint": {
              "id": "CA_STD_022678",
              "text": "内饰不好看",
              "confidence": 0.83
            }
          },
          "text": "内饰",
          "start": 2,
          "end": 4
        },
        {
          "relations": {
            "segment": [
              {
                "text": ""
              }
            ],
            "品牌车系": [
              {
                "text": ""
              }
            ],
            "情感倾向": [
              {
                "text": ""
              }
            ],
            "意图": [
              {
                "text": ""
              }
            ],
            "观点": [
              {
                "text": "好看",
                "start": 8,
                "end": 10
              }
            ],
            "normalized_subject": {
              "id": "SUBJ_000210",
              "text": "内饰",
              "score": 0.94
            },
            "normalized_description": {
              "id": "DESC_100045",
              "text": "好看",
              "score": 0.90
            },
            "normalized_opinion": {
              "id": "OP_00009002",
              "text": "内饰-好看"
            },
            "std_viewpoint": {
              "id": "CA_STD_022345",
              "text": "内饰好看",
              "confidence": 0.82
            }
          },
          "text": "内饰",
          "start": 6,
          "end": 8
        }
      ]
    }
  ]
}
```

##### 意图到结果输出：消费意图情感识别 topic， 根据原文主体+原文描述找到对应的原文语句，例如主体向前搜最近的标点，然后描述向后搜最近的标点拼成一句话，如果前进了 20 个字符还没找到标点就截断等。对应的原句通过意图识别模型和情感识别模型，汇总结果，通过车系的影响域以及句式匹配判断这个主体属于哪个车系，从而完成输出，如果未注明品牌车系，则默认为长安无车系
输入：

```json
{
  "topic_id": "0000001",
  "source_data": {
    "dataSource": "汽车之家",
    "domainListJson": ["汽车","客服","售后"],
    "productName": "长安CS75 PLUS",
    "populationGroup": "25-35岁男性",
    "businessType": "产品评价",
    "businessScene": "购车前调研",
    "textType": 1,
    "title": "CS75 PLUS使用一个月感受",
    "topic_text": "这个车的大灯真的很亮，晚上开车很安全，但是内饰的塑料感有点强，奇瑞的要好点",
    "optionScore": {"overall":8,"appearance":9,"interior":6},
    "position": 1,
    "forumSeries": "CS75系列",
    "brand": "长安",
    "series": "CS75 PLUS",
    "create_time": "2025-08-06 12:00:00"
  },
  "cat_type": "uie",
  "retry_count": 1,
  "error_type": "model_timeout",
  "error_msg": "TimeoutError: inference exceeded 10s",
  "last_ts": "2025-07-31T17:20:00",
  "result": [
    {
      "评价对象": [
        {
          "relations": {
            "segment": [
              {
                "text": ""
              }
            ],
            "品牌车系": [
              {
                "text": ""
              }
            ],
            "情感倾向": [
              {
                "text": ""
              }
            ],
            "意图": [
              {
                "text": ""
              }
            ],
            "观点": [
              {
                "text": "屌爆了",
                "start": 6,
                "end": 9
              }
            ],
            "normalized_subject": {
              "id": "SUBJ_000123",
              "text": "大灯",
              "score": 0.92
            },
            "normalized_description": {
              "id": "DESC_100045",
              "text": "好看",
              "score": 0.90
            },
            "normalized_opinion": {
              "id": "OP_00000027",
              "text": "大灯-好看"
            },
            "std_viewpoint": {
              "id": "CA_STD_011234",
              "text": "大灯好看",
              "confidence": 0.87
            }
          },
          "text": "大灯",
          "start": 4,
          "end": 6
        },
        {
          "relations": {
            "segment": [
              {
                "text": ""
              }
            ],
            "品牌车系": [
              {
                "text": ""
              }
            ],
            "情感倾向": [
              {
                "text": ""
              }
            ],
            "意图": [
              {
                "text": ""
              }
            ],
            "观点": [
              {
                "text": "丑",
                "start": 6,
                "end": 7
              }
            ],
            "normalized_subject": {
              "id": "SUBJ_000210",
              "text": "内饰",
              "score": 0.94
            },
            "normalized_description": {
              "id": "DESC_200012",
              "text": "不好看",
              "score": 0.89
            },
            "normalized_opinion": {
              "id": "OP_00009001",
              "text": "内饰-不好看"
            },
            "std_viewpoint": {
              "id": "CA_STD_022678",
              "text": "内饰不好看",
              "confidence": 0.83
            }
          },
          "text": "内饰",
          "start": 2,
          "end": 4
        },
        {
          "relations": {
            "segment": [
              {
                "text": ""
              }
            ],
            "品牌车系": [
              {
                "text": ""
              }
            ],
            "情感倾向": [
              {
                "text": ""
              }
            ],
            "意图": [
              {
                "text": ""
              }
            ],
            "观点": [
              {
                "text": "好看",
                "start": 8,
                "end": 10
              }
            ],
            "normalized_subject": {
              "id": "SUBJ_000210",
              "text": "内饰",
              "score": 0.94
            },
            "normalized_description": {
              "id": "DESC_100045",
              "text": "好看",
              "score": 0.90
            },
            "normalized_opinion": {
              "id": "OP_00009002",
              "text": "内饰-好看"
            },
            "std_viewpoint": {
              "id": "CA_STD_022345",
              "text": "内饰好看",
              "confidence": 0.82
            }
          },
          "text": "内饰",
          "start": 6,
          "end": 8
        }
      ]
    }
  ]
}
```

输出：

```json
{
  "topic_id": "0000001",
  "source_data": {
    "dataSource": "汽车之家",
    "domainListJson": ["汽车","客服","售后"],
    "productName": "长安CS75 PLUS",
    "populationGroup": "25-35岁男性",
    "businessType": "产品评价",
    "businessScene": "购车前调研",
    "textType": 1,
    "title": "CS75 PLUS使用一个月感受",
    "topic_text": "这个车的大灯真的很亮，晚上开车很安全，但是内饰的塑料感有点强，奇瑞的要好点",
    "optionScore": {"overall":8,"appearance":9,"interior":6},
    "position": 1,
    "forumSeries": "CS75系列",
    "brand": "长安",
    "series": "CS75 PLUS",
    "create_time": "2025-08-06 12:00:00"
  },
  "cat_type": "uie",
  "retry_count": 1,
  "error_type": "model_timeout",
  "error_msg": "TimeoutError: inference exceeded 10s",
  "last_ts": "2025-07-31T17:20:00",
  "result": [
    {
      "评价对象": [
        {
          "relations": {
            "segment": [
              {
                "text": "这个车的大灯屌爆了"
              }
            ],
            "品牌车系": [
              {
                "text": ""
              }
            ],
            "情感倾向": [
              {
                "text": "正面"
              }
            ],
            "意图": [
              {
                "text": "表扬"
              }
            ],
            "观点": [
              {
                "text": "屌爆了",
                "start": 6,
                "end": 9
              }
            ],
            "normalized_subject": {
              "id": "SUBJ_000123",
              "text": "大灯",
              "score": 0.92
            },
            "normalized_description": {
              "id": "DESC_100045",
              "text": "好看",
              "score": 0.90
            },
            "normalized_opinion": {
              "id": "OP_00000027",
              "text": "大灯-好看"
            },
            "std_viewpoint": {
              "id": "CA_STD_011234",
              "text": "大灯好看",
              "confidence": 0.87
            }
          },
          "text": "大灯",
          "start": 4,
          "end": 6
        },
        {
          "relations": {
            "segment": [
              {
                "text": "就是内饰有点丑"
              }
            ],
            "品牌车系": [
              {
                "text": ""
              }
            ],
            "情感倾向": [
              {
                "text": "负面"
              }
            ],
            "意图": [
              {
                "text": "抱怨"
              }
            ],
            "观点": [
              {
                "text": "丑",
                "start": 6,
                "end": 7
              }
            ],
            "normalized_subject": {
              "id": "SUBJ_000210",
              "text": "内饰",
              "score": 0.94
            },
            "normalized_description": {
              "id": "DESC_200012",
              "text": "不好看",
              "score": 0.89
            },
            "normalized_opinion": {
              "id": "OP_00009001",
              "text": "内饰-不好看"
            },
            "std_viewpoint": {
              "id": "CA_STD_022678",
              "text": "内饰不好看",
              "confidence": 0.83
            }
          },
          "text": "内饰",
          "start": 2,
          "end": 4
        },
        {
          "relations": {
            "segment": [
              {
                "text": "还是吉利星越内饰好看"
              }
            ],
            "品牌车系": [
              {
                "text": ""
              }
            ],
            "情感倾向": [
              {
                "text": "正面"
              }
            ],
            "意图": [
              {
                "text": "表扬"
              }
            ],
            "观点": [
              {
                "text": "好看",
                "start": 8,
                "end": 10
              }
            ],
            "normalized_subject": {
              "id": "SUBJ_000210",
              "text": "内饰",
              "score": 0.94
            },
            "normalized_description": {
              "id": "DESC_100045",
              "text": "好看",
              "score": 0.90
            },
            "normalized_opinion": {
              "id": "OP_00009002",
              "text": "内饰-好看"
            },
            "std_viewpoint": {
              "id": "CA_STD_022345",
              "text": "内饰好看",
              "confidence": 0.82
            }
          },
          "text": "内饰",
          "start": 6,
          "end": 8
        }
      ]
    }
  ]
}
```

##### 品牌车系判断：根据影响域判断每个主体+描述对应的品牌车系是什么，读取品牌车系及其近义词进行判断
##### 意图到结果输出：消费意图情感识别 topic， 根据原文主体+原文描述找到对应的原文语句，例如主体向前搜最近的标点，然后描述向后搜最近的标点拼成一句话，如果前进了 20 个字符还没找到标点就截断等。对应的原句通过意图识别模型和情感识别模型，汇总结果，通过车系的影响域以及句式匹配判断这个主体属于哪个车系，从而完成输出，如果未注明品牌车系，则默认为长安无车系
输入：

```json
{
  "topic_id": "0000001",
  "source_data": {
    "dataSource": "汽车之家",
    "domainListJson": ["汽车","客服","售后"],
    "productName": "长安CS75 PLUS",
    "populationGroup": "25-35岁男性",
    "businessType": "产品评价",
    "businessScene": "购车前调研",
    "textType": 1,
    "title": "CS75 PLUS使用一个月感受",
    "topic_text": "这个车的大灯真的很亮，晚上开车很安全，但是内饰的塑料感有点强，奇瑞的要好点",
    "optionScore": {"overall":8,"appearance":9,"interior":6},
    "position": 1,
    "forumSeries": "CS75系列",
    "brand": "长安",
    "series": "CS75 PLUS",
    "create_time": "2025-08-06 12:00:00"
  },
  "cat_type": "uie",
  "retry_count": 1,
  "error_type": "model_timeout",
  "error_msg": "TimeoutError: inference exceeded 10s",
  "last_ts": "2025-07-31T17:20:00",
  "result": [
    {
      "评价对象": [
        {
          "relations": {
            "segment": [
              {
                "text": "这个车的大灯屌爆了"
              }
            ],
            "品牌车系": [
              {
                "text": ""
              }
            ],
            "情感倾向": [
              {
                "text": "正面"
              }
            ],
            "意图": [
              {
                "text": "表扬"
              }
            ],
            "观点": [
              {
                "text": "屌爆了",
                "start": 6,
                "end": 9
              }
            ],
            "normalized_subject": {
              "id": "SUBJ_000123",
              "text": "大灯",
              "score": 0.92
            },
            "normalized_description": {
              "id": "DESC_100045",
              "text": "好看",
              "score": 0.90
            },
            "normalized_opinion": {
              "id": "OP_00000027",
              "text": "大灯-好看"
            },
            "std_viewpoint": {
              "id": "CA_STD_011234",
              "text": "大灯好看",
              "confidence": 0.87
            }
          },
          "text": "大灯",
          "start": 4,
          "end": 6
        },
        {
          "relations": {
            "segment": [
              {
                "text": "就是内饰有点丑"
              }
            ],
            "品牌车系": [
              {
                "text": ""
              }
            ],
            "情感倾向": [
              {
                "text": "负面"
              }
            ],
            "意图": [
              {
                "text": "抱怨"
              }
            ],
            "观点": [
              {
                "text": "丑",
                "start": 6,
                "end": 7
              }
            ],
            "normalized_subject": {
              "id": "SUBJ_000210",
              "text": "内饰",
              "score": 0.94
            },
            "normalized_description": {
              "id": "DESC_200012",
              "text": "不好看",
              "score": 0.89
            },
            "normalized_opinion": {
              "id": "OP_00009001",
              "text": "内饰-不好看"
            },
            "std_viewpoint": {
              "id": "CA_STD_022678",
              "text": "内饰不好看",
              "confidence": 0.83
            }
          },
          "text": "内饰",
          "start": 2,
          "end": 4
        },
        {
          "relations": {
            "segment": [
              {
                "text": "还是吉利星越内饰好看"
              }
            ],
            "品牌车系": [
              {
                "text": ""
              }
            ],
            "情感倾向": [
              {
                "text": "正面"
              }
            ],
            "意图": [
              {
                "text": "表扬"
              }
            ],
            "观点": [
              {
                "text": "好看",
                "start": 8,
                "end": 10
              }
            ],
            "normalized_subject": {
              "id": "SUBJ_000210",
              "text": "内饰",
              "score": 0.94
            },
            "normalized_description": {
              "id": "DESC_100045",
              "text": "好看",
              "score": 0.90
            },
            "normalized_opinion": {
              "id": "OP_00009002",
              "text": "内饰-好看"
            },
            "std_viewpoint": {
              "id": "CA_STD_022345",
              "text": "内饰好看",
              "confidence": 0.82
            }
          },
          "text": "内饰",
          "start": 6,
          "end": 8
        }
      ]
    }
  ]
}
```

输出：

```json
{
  "topic_id": "0000001",
  "source_data": {
    "dataSource": "汽车之家",
    "domainListJson": ["汽车","客服","售后"],
    "productName": "长安CS75 PLUS",
    "populationGroup": "25-35岁男性",
    "businessType": "产品评价",
    "businessScene": "购车前调研",
    "textType": 1,
    "title": "CS75 PLUS使用一个月感受",
    "topic_text": "这个车的大灯真的很亮，晚上开车很安全，但是内饰的塑料感有点强，奇瑞的要好点",
    "optionScore": {"overall":8,"appearance":9,"interior":6},
    "position": 1,
    "forumSeries": "CS75系列",
    "brand": "长安",
    "series": "CS75 PLUS",
    "create_time": "2025-08-06 12:00:00"
  },
  "cat_type": "uie",
  "retry_count": 1,
  "error_type": "model_timeout",
  "error_msg": "TimeoutError: inference exceeded 10s",
  "last_ts": "2025-07-31T17:20:00",
  "result": [
    {
      "评价对象": [
        {
          "relations": {
            "segment": [
              {
                "text": "这个车的大灯屌爆了"
              }
            ],
            "品牌车系": [
              {
                "text": "长安无车系"
              }
            ],
            "情感倾向": [
              {
                "text": "正面"
              }
            ],
            "意图": [
              {
                "text": "表扬"
              }
            ],
            "观点": [
              {
                "text": "屌爆了",
                "start": 6,
                "end": 9
              }
            ],
            "normalized_subject": {
              "id": "SUBJ_000123",
              "text": "大灯",
              "score": 0.92
            },
            "normalized_description": {
              "id": "DESC_100045",
              "text": "好看",
              "score": 0.90
            },
            "normalized_opinion": {
              "id": "OP_00000027",
              "text": "大灯-好看"
            },
            "std_viewpoint": {
              "id": "CA_STD_011234",
              "text": "大灯好看",
              "confidence": 0.87
            }
          },
          "text": "大灯",
          "start": 4,
          "end": 6
        },
        {
          "relations": {
            "segment": [
              {
                "text": "就是内饰有点丑"
              }
            ],
            "品牌车系": [
              {
                "text": "长安无车系"
              }
            ],
            "情感倾向": [
              {
                "text": "负面"
              }
            ],
            "意图": [
              {
                "text": "抱怨"
              }
            ],
            "观点": [
              {
                "text": "丑",
                "start": 6,
                "end": 7
              }
            ],
            "normalized_subject": {
              "id": "SUBJ_000210",
              "text": "内饰",
              "score": 0.94
            },
            "normalized_description": {
              "id": "DESC_200012",
              "text": "不好看",
              "score": 0.89
            },
            "normalized_opinion": {
              "id": "OP_00009001",
              "text": "内饰-不好看"
            },
            "std_viewpoint": {
              "id": "CA_STD_022678",
              "text": "内饰不好看",
              "confidence": 0.83
            }
          },
          "text": "内饰",
          "start": 2,
          "end": 4
        },
        {
          "relations": {
            "segment": [
              {
                "text": "还是吉利星越内饰好看"
              }
            ],
            "品牌车系": [
              {
                "text": "吉利星越"
              }
            ],
            "情感倾向": [
              {
                "text": "正面"
              }
            ],
            "意图": [
              {
                "text": "表扬"
              }
            ],
            "观点": [
              {
                "text": "好看",
                "start": 8,
                "end": 10
              }
            ],
            "normalized_subject": {
              "id": "SUBJ_000210",
              "text": "内饰",
              "score": 0.94
            },
            "normalized_description": {
              "id": "DESC_100045",
              "text": "好看",
              "score": 0.90
            },
            "normalized_opinion": {
              "id": "OP_00009002",
              "text": "内饰-好看"
            },
            "std_viewpoint": {
              "id": "CA_STD_022345",
              "text": "内饰好看",
              "confidence": 0.82
            }
          },
          "text": "内饰",
          "start": 6,
          "end": 8
        }
      ]
    }
  ]
}
```

##### 错误数据回滚策略：开发一个服务专门消费 model_error_topic，处理逻辑为如果 retry_count 小于 5 就回滚到一个 model_retry_topic，并且retry_count + 1， 如果大于等于 5 了，就连接 starrocks 存进去，不再尝试了
各模块错误输出，该模块输入，<font style="color:#DF2A3F;">任何错误的统一为此 JSON</font>：

```json
{
  "topic_id": "0000001",
  "source_data":  {
        "dataSource": "汽车之家",
        "domainListJson": ["汽车","客服","售后"],
        "productName": "长安CS75 PLUS",
        "populationGroup": "25-35岁男性",
        "businessType": "产品评价",
        "businessScene": "购车前调研",
        "textType": 1,
        "title": "CS75 PLUS使用一个月感受",
        "topic_text": "这个车的大灯真的很亮，晚上开车很安全，但是内饰的塑料感有点强，奇瑞的要好点",
        "optionScore": {"overall":8,"appearance":9,"interior":6},
        "position": 1,
        "forumSeries": "CS75系列",
        "brand": "长安",
        "series": "CS75 PLUS",
        "create_time": "2025-08-06 12:00:00",
    },
  "retry_count":1,
  "error_type": "model_timeout",
  "error_msg":"TimeoutError: inference exceeded 10s",
  "last_ts":"2025-07-31T17:20:00"
}
```

```json
{
  "topic_id": "0000001",
  "source_data":  {
        "dataSource": "汽车之家",
        "domainListJson": ["汽车","客服","售后"],
        "productName": "长安CS75 PLUS",
        "populationGroup": "25-35岁男性",
        "businessType": "产品评价",
        "businessScene": "购车前调研",
        "textType": 1,
        "title": "CS75 PLUS使用一个月感受",
        "topic_text": "这个车的大灯真的很亮，晚上开车很安全，但是内饰的塑料感有点强，奇瑞的要好点",
        "optionScore": {"overall":8,"appearance":9,"interior":6},
        "position": 1,
        "forumSeries": "CS75系列",
        "brand": "长安",
        "series": "CS75 PLUS",
        "create_time": "2025-08-06 12:00:00",
    },
  "retry_count":0,
  "error_type": "",
  "error_msg":"",
  "last_ts":""
}
```

输出：

```json
{
  "topic_id": "0000001",
  "source_data":  {
        "dataSource": "汽车之家",
        "domainListJson": ["汽车","客服","售后"],
        "productName": "长安CS75 PLUS",
        "populationGroup": "25-35岁男性",
        "businessType": "产品评价",
        "businessScene": "购车前调研",
        "textType": 1,
        "title": "CS75 PLUS使用一个月感受",
        "topic_text": "这个车的大灯真的很亮，晚上开车很安全，但是内饰的塑料感有点强，奇瑞的要好点",
        "optionScore": {"overall":8,"appearance":9,"interior":6},
        "position": 1,
        "forumSeries": "CS75系列",
        "brand": "长安",
        "series": "CS75 PLUS",
        "create_time": "2025-08-06 12:00:00",
    },
  "retry_count":1,
  "error_type": "model_timeout",
  "error_msg":"TimeoutError: inference exceeded 10s",
  "last_ts":"2025-07-31T17:20:00"
}
```





