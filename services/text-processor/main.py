"""
文本预处理和分流服务
负责粗分句和根据品牌车系数量进行分流
"""
import asyncio
import signal
import sys
import time
from pathlib import Path
from typing import List, Dict, Any, Optional
import traceback
# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from fastapi import FastAPI, HTTPException
from contextlib import asynccontextmanager
import uvicorn

from shared.base import BaseService
from shared.utils.text_processor import TextProcessor
from shared.database.mysql_client import MySQLClient
from shared.models.schemas import (
    InitialMessage, CategoryMessage, ErrorMessage, APIResponse, SourceData
)
from typing import Union, List, Dict, Any, Tuple


class TextProcessorService(BaseService):
    """文本处理服务"""
    
    def __init__(self):
        super().__init__('text-processor')
        self.text_processor = None
        self.mysql_client = None  # 品牌车系数据库连接 (voc_ms_be)
        self.filter_mysql_client = None  # 过滤规则数据库连接 (voc_model)
        self.last_cuizong_message_time = time.time()  # 确保不为None
        self.cuizong_idle_threshold = 600  # 10分钟
        
        # 智能过滤规则缓存
        self._filter_rules_cache = None
        self._filter_rules_cache_time = 0
        self._cache_ttl = 300  # 5分钟缓存
    
    def _init_service_specific_config(self):
        """初始化服务特定配置"""
        pass
    
    async def _initialize_service_specific(self):
        """服务特定的初始化逻辑"""
        # 初始化Elasticsearch客户端（品牌车系数据在ES中）
        from shared.elasticsearch.es_client import ElasticsearchClient
        self.es_client = ElasticsearchClient(
            self.config_manager.get_elasticsearch_config(),
            self.config_manager.get_vector_config()
        )
        await self.es_client.initialize()
        
        # 初始化MySQL客户端（用于品牌车系数据查询）
        db_config = self.config_manager.get_database_config()
        # 覆盖为 MySQL 专用配置（优先环境变量，再读 configs/config.yaml 的 database.mysql.*）
        db_host = self.config_manager._get_env_or_config('MYSQL_HOST', 'database.mysql.host', self.config_manager._get_env_or_config('DB_HOST', 'database.mysql.host', db_config.host))
        db_port = self.config_manager._get_env_or_config('MYSQL_PORT', 'database.mysql.port', self.config_manager._get_env_or_config('DB_PORT', 'database.mysql.port', db_config.port))
        db_user = self.config_manager._get_env_or_config('MYSQL_USERNAME', 'database.mysql.username', self.config_manager._get_env_or_config('DB_USERNAME', 'database.mysql.username', db_config.username))
        db_pass = self.config_manager._get_env_or_config('MYSQL_PASSWORD', 'database.mysql.password', self.config_manager._get_env_or_config('DB_PASSWORD', 'database.mysql.password', db_config.password))
        # 品牌车系服务：数据库名使用 database1 (voc_ms_be)
        db_name = self.config_manager._get_env_or_config('MYSQL_DATABASE1', 'database.mysql.database1', self.config_manager._get_env_or_config('DB_DATABASE1', 'database.mysql.database1', 'voc_ms_be'))
        
        # 更新数据库配置
        db_config.host = db_host
        db_config.port = int(db_port)
        db_config.username = db_user
        db_config.password = db_pass
        db_config.database = db_name
        
        # 初始化品牌车系数据库客户端 (voc_ms_be)
        self.mysql_client = MySQLClient(db_config)
        await self.mysql_client.initialize()
        
        # 初始化过滤规则数据库客户端 (voc_model)
        from dataclasses import replace
        filter_db_name = self.config_manager._get_env_or_config('MYSQL_DATABASE', 'database.mysql.database', 'voc_model')
        filter_db_config = replace(db_config, database=filter_db_name)
        
        self.filter_mysql_client = MySQLClient(filter_db_config)
        await self.filter_mysql_client.initialize()
        
        self.logger.info(f"初始化数据库连接: 品牌车系数据库={db_config.database}, 过滤规则数据库={filter_db_config.database}")
        
        # 初始化文本处理器（包含内置否定词）
        self.text_processor = TextProcessor()
        await self.text_processor.initialize()
    
    async def _load_filter_rules(self) -> Dict[str, List[Dict[str, Any]]]:
        """加载过滤规则，带缓存机制"""
        current_time = time.time()
        
        # 检查缓存是否有效
        if (self._filter_rules_cache is not None and 
            current_time - self._filter_rules_cache_time < self._cache_ttl):
            return self._filter_rules_cache
        
        try:
            # 从数据库加载过滤规则
            sql = """
            SELECT rule_type, brand_name, series_name, pattern, 
                   required_context, blacklist_context, priority, description
            FROM brand_series_filter_rules 
            WHERE status = 1 
            ORDER BY priority ASC, id ASC
            """
            
            results = await self.filter_mysql_client.execute_query(sql)
            
            # 按规则类型分组
            rules = {
                'global_blacklist': [],
                'context_rule': [],
                'ambiguous_pattern': []
            }
            
            for row in results:
                rule_type = row.get('rule_type', '')
                if rule_type in rules:
                    rules[rule_type].append({
                        'brand_name': row.get('brand_name', ''),
                        'series_name': row.get('series_name', ''),
                        'pattern': row.get('pattern', ''),
                        'required_context': row.get('required_context', ''),
                        'blacklist_context': row.get('blacklist_context', ''),
                        'priority': row.get('priority', 100),
                        'description': row.get('description', '')
                    })
            
            # 更新缓存
            self._filter_rules_cache = rules
            self._filter_rules_cache_time = current_time
            
            self.logger.info(f"加载过滤规则成功: 全局黑名单{len(rules['global_blacklist'])}条, "
                           f"上下文规则{len(rules['context_rule'])}条, "
                           f"歧义模式{len(rules['ambiguous_pattern'])}条")
            
            return rules
            
        except Exception as e:
            self.logger.error(f"加载过滤规则失败: {e}")
            # 返回空规则集
            return {'global_blacklist': [], 'context_rule': [], 'ambiguous_pattern': []}
    
    def _check_context_match(self, text: str, pattern: str, start_pos: int, 
                           required_context: str = '', blacklist_context: str = '') -> bool:
        """检查上下文匹配"""
        # 获取匹配位置前后10个字符的上下文
        context_range = 10
        text_len = len(text)
        context_start = max(0, start_pos - context_range)
        context_end = min(text_len, start_pos + len(pattern) + context_range)
        context = text[context_start:context_end].lower()
        
        # 检查黑名单上下文
        if blacklist_context:
            blacklist_words = [word.strip().lower() for word in blacklist_context.split(',') if word.strip()]
            for blacklist_word in blacklist_words:
                if blacklist_word in context:
                    return False  # 命中黑名单，过滤掉
        
        # 检查必需上下文
        if required_context:
            required_words = [word.strip().lower() for word in required_context.split(',') if word.strip()]
            for required_word in required_words:
                if required_word in context:
                    return True  # 找到必需上下文，保留
            return False  # 没有找到必需上下文，过滤掉
        
        return True  # 没有上下文要求，保留
    
    async def _smart_filter_brand_series(self, brand_series_list: List[Dict[str, Any]], 
                                       text: str) -> List[Dict[str, Any]]:
        """智能过滤品牌车系匹配结果"""
        if not brand_series_list:
            return brand_series_list
        
        # 加载过滤规则
        filter_rules = await self._load_filter_rules()
        text_lower = text.lower()
        
        filtered_list = []
        
        for item in brand_series_list:
            brand = item.get('brand', '')
            series = item.get('series', '')
            should_keep = True
            filter_reason = ""
            
            # 1. 全局黑名单检查
            for rule in filter_rules['global_blacklist']:
                pattern = rule['pattern'].lower()
                if pattern in text_lower:
                    should_keep = False
                    filter_reason = f"全局黑名单: {rule['pattern']}"
                    break
            
            if not should_keep:
                self.logger.debug(f"过滤品牌车系 {brand}-{series}: {filter_reason}")
                continue
            
            # 2. 上下文规则检查
            for rule in filter_rules['context_rule']:
                rule_brand = rule['brand_name']
                rule_series = rule['series_name']
                pattern = rule['pattern'].lower()
                
                # 检查是否匹配当前品牌车系
                if ((rule_brand and brand.lower() == rule_brand.lower()) or 
                    (rule_series and series.lower() == rule_series.lower()) or
                    (not rule_brand and not rule_series and pattern in text_lower)):
                    
                    # 查找匹配位置
                    match_pos = text_lower.find(pattern)
                    if match_pos >= 0:
                        # 检查上下文
                        if not self._check_context_match(text_lower, pattern, match_pos,
                                                       rule['required_context'], 
                                                       rule['blacklist_context']):
                            should_keep = False
                            filter_reason = f"上下文规则: {rule['description']}"
                            break
            
            if not should_keep:
                self.logger.debug(f"过滤品牌车系 {brand}-{series}: {filter_reason}")
                continue
            
            # 3. 歧义模式检查
            for rule in filter_rules['ambiguous_pattern']:
                pattern = rule['pattern'].lower()
                if pattern in text_lower:
                    # 检查黑名单上下文
                    match_pos = text_lower.find(pattern)
                    if match_pos >= 0:
                        if not self._check_context_match(text_lower, pattern, match_pos,
                                                       '', rule['blacklist_context']):
                            should_keep = False
                            filter_reason = f"歧义模式: {rule['description']}"
                            break
            
            if should_keep:
                filtered_list.append(item)
            else:
                self.logger.debug(f"过滤品牌车系 {brand}-{series}: {filter_reason}")
        
        original_count = len(brand_series_list)
        filtered_count = len(filtered_list)
        
        if original_count != filtered_count:
            self.logger.info(f"智能过滤: {original_count} → {filtered_count} "
                           f"(过滤了{original_count - filtered_count}个误匹配)")
        
        return filtered_list
    
    async def _count_brand_series_mysql(self, text: str) -> Tuple[int, List[Dict[str, Any]]]:
        """查询文本中包含的所有品牌和车系列表及其数量（带智能过滤）"""
        try:
            # 查询所有活跃的品牌车系数据，然后在Python中匹配
            # 根据实际表结构：品牌表和车系表通过 brand_id 关联
            sql = """
            SELECT DISTINCT
                b.name as brand,
                b.alias as brand_alias,
                s.name as series,
                s.alias as series_alias
            FROM ins_brand_info b
            LEFT JOIN ins_car_series_info s ON b.id = s.brand_id
            WHERE b.del_flag = 0 AND (s.del_flag = 0 OR s.del_flag IS NULL)
            ORDER BY b.name, s.name
            """
            
            # 执行查询
            results = await self.mysql_client.execute_query(sql)
            
            # 在Python中匹配文本中包含的品牌和车系
            text_lower = text.lower()
            brand_series_list = []
            unique_matches = set()
            
            for row in results:
                brand = row.get('brand', '')
                series = row.get('series', '')
                brand_alias = row.get('brand_alias', '') or ''
                series_alias = row.get('series_alias', '') or ''
                
                # 检查品牌匹配
                brand_matched = False
                if brand and brand.lower() in text_lower:
                    brand_matched = True
                elif brand_alias:
                    # 检查品牌别名
                    aliases = [alias.strip().lower() for alias in brand_alias.split(',') if alias.strip()]
                    for alias in aliases:
                        if alias in text_lower:
                            brand_matched = True
                            break
                
                # 检查车系匹配
                series_matched = False
                if series and series.lower() in text_lower:
                    series_matched = True
                elif series_alias:
                    # 检查车系别名
                    aliases = [alias.strip().lower() for alias in series_alias.split(',') if alias.strip()]
                    for alias in aliases:
                        if alias in text_lower:
                            series_matched = True
                            break
                
                # 只添加真正匹配的项目
                if brand_matched and not series_matched:
                    # 只匹配了品牌，不匹配车系
                    unique_key = f"{brand}|brand_only"
                    if unique_key not in unique_matches:
                        unique_matches.add(unique_key)
                        brand_series_info = {
                            'brand': brand,
                            'series': ''
                        }
                        brand_series_list.append(brand_series_info)
                elif series_matched:
                    # 匹配了车系（可能也匹配了品牌）
                    unique_key = f"{brand}|{series}"
                    if unique_key not in unique_matches:
                        unique_matches.add(unique_key)
                        brand_series_info = {
                            'brand': brand,
                            'series': series
                        }
                        brand_series_list.append(brand_series_info)
            
            # 应用智能过滤
            filtered_brand_series_list = await self._smart_filter_brand_series(brand_series_list, text)
            
            total_count = len(filtered_brand_series_list)
            self.logger.debug(f"Brand series matches found: {total_count} for text: {text[:50]}...")
            return total_count, filtered_brand_series_list
            
        except Exception as e:
            self.logger.warning(f"品牌车系统计失败，使用默认值0: {e}")
            return 0, []
    
    async def _count_brand_series_mysql_without_filter(self, text: str) -> Tuple[int, List[Dict[str, Any]]]:
        """查询文本中包含的所有品牌和车系列表及其数量（不过滤，用于对比测试）"""
        try:
            # 查询所有活跃的品牌车系数据，然后在Python中匹配
            sql = """
            SELECT DISTINCT
                b.name as brand,
                b.alias as brand_alias,
                s.name as series,
                s.alias as series_alias
            FROM ins_brand_info b
            LEFT JOIN ins_car_series_info s ON b.id = s.brand_id
            WHERE b.del_flag = 0 AND (s.del_flag = 0 OR s.del_flag IS NULL)
            ORDER BY b.name, s.name
            """
            
            # 执行查询
            results = await self.mysql_client.execute_query(sql)
            
            # 在Python中匹配文本中包含的品牌和车系
            text_lower = text.lower()
            brand_series_list = []
            unique_matches = set()
            
            for row in results:
                brand = row.get('brand', '')
                series = row.get('series', '')
                brand_alias = row.get('brand_alias', '') or ''
                series_alias = row.get('series_alias', '') or ''
                
                # 检查品牌匹配
                brand_matched = False
                if brand and brand.lower() in text_lower:
                    brand_matched = True
                elif brand_alias:
                    # 检查品牌别名
                    aliases = [alias.strip().lower() for alias in brand_alias.split(',') if alias.strip()]
                    for alias in aliases:
                        if alias in text_lower:
                            brand_matched = True
                            break
                
                # 检查车系匹配
                series_matched = False
                if series and series.lower() in text_lower:
                    series_matched = True
                elif series_alias:
                    # 检查车系别名
                    aliases = [alias.strip().lower() for alias in series_alias.split(',') if alias.strip()]
                    for alias in aliases:
                        if alias in text_lower:
                            series_matched = True
                            break
                
                # 只添加真正匹配的项目
                if brand_matched and not series_matched:
                    # 只匹配了品牌，不匹配车系
                    unique_key = f"{brand}|brand_only"
                    if unique_key not in unique_matches:
                        unique_matches.add(unique_key)
                        brand_series_info = {
                            'brand': brand,
                            'series': ''
                        }
                        brand_series_list.append(brand_series_info)
                elif series_matched:
                    # 匹配了车系（可能也匹配了品牌）
                    unique_key = f"{brand}|{series}"
                    if unique_key not in unique_matches:
                        unique_matches.add(unique_key)
                        brand_series_info = {
                            'brand': brand,
                            'series': series
                        }
                        brand_series_list.append(brand_series_info)
            
            total_count = len(brand_series_list)
            return total_count, brand_series_list
            
        except Exception as e:
            self.logger.warning(f"品牌车系统计失败，使用默认值0: {e}")
            return 0, []
    
    async def _process_qa_contents(self, contents: List[str], brand: str, series: str) -> List[Dict[str, Any]]:
        """处理问卷内容，生成qaresult结果"""
        qaresult = []
        
        for i, content in enumerate(contents):
            self.logger.info(f"处理问卷内容: {content}")
            
            # 查询MySQL的opinion_synonym表
            standard_opinion_id = await self.mysql_client.get_standard_opinion_id_by_content(content)
            
            if standard_opinion_id:
                # 通过MySQL查找相应的标准观点信息
                db_opinion = await self.mysql_client.get_standard_opinion(standard_opinion_id)
                standard_text = (
                    getattr(db_opinion, 'standard_opinion', None)
                    if db_opinion is not None else None
                )
                if standard_text is None and isinstance(db_opinion, dict):
                    standard_text = db_opinion.get('standard_opinion', '')
                if standard_text is None:
                    standard_text = ''
                
                qaresult.append({
                    'segment': content,
                    "std_viewpoint":{
                        "id": standard_opinion_id,
                        "text": standard_text
                    },
                    "intent":"",
                    "sentiment":"",
                    "brand":brand,
                    "series":series
                })
                
                self.logger.info(f"问卷处理成功: content={content}, opinion_id={standard_opinion_id}")
            else:
                self.logger.warning(f"未找到对应的standard_opinion_id: {content}")
        
        self.logger.info(f"问卷处理完成，共处理 {len(qaresult)} 个内容项")
        return qaresult
    
    def _parse_topic_texts(self, data_source_type: str, topic_texts: List[Dict[str, str]], title: str) -> tuple[List[str], List[str]]:
        """解析topic_texts，根据数据源和内容特征分类
        
        Args:
            data_source_type: 数据来源类型
            topic_texts: 原始topic_text列表
            
        Returns:
            tuple: (contents, qa_contents)
                - contents: 所有content的列表
                - qa_contents: 包含#的问卷内容列表
        """
        contents = []
        qa_contents = []
        
        if data_source_type == "咨询":
            # 对话：按照role进行划分，每一组agent和customer为一个content
            current_content = []
            current_agent = None
            
            for text_item in topic_texts:
                if not isinstance(text_item, dict) or 'content' not in text_item or 'role' not in text_item:
                    self.logger.warning(f"跳过无效的topic_text项: {text_item}")
                    continue
                
                role = text_item['role']
                content = text_item['content']
                
                if role == 'agent':
                    # 如果遇到新的agent，先保存之前的内容
                    if current_content:
                        contents.append(' '.join(current_content))
                        current_content = []
                    current_agent = content
                    current_content.append(f"agent: {content}")
                elif role == 'customer':
                    # customer内容添加到当前组
                    current_content.append(f"customer: {content}")
            
            # 保存最后一组内容
            if current_content:
                contents.append(' '.join(current_content))
            
            self.logger.info(f"对话数据源: 共解析出 {len(contents)} 个对话组")
        
        elif data_source_type == "帖子回评":
            # 帖子回评：每一个topic_text是一个content
            for text_item in topic_texts:
                if isinstance(text_item, dict) and 'content' in text_item:
                    contents.append(title + " " + text_item['content'])
                else:
                    self.logger.warning(f"跳过无效的topic_text项: {text_item}")
                    continue
            
        elif data_source_type == "工单" or data_source_type == "意见反馈":
            # 评论：每一个topic_text是一个content
            for text_item in topic_texts:
                if isinstance(text_item, dict) and 'content' in text_item:
                    contents.append(text_item['content'])
                else:
                    self.logger.warning(f"跳过无效的topic_text项: {text_item}")
                    continue
            
            self.logger.info(f"评论数据源: 共解析出 {len(contents)} 个评论内容")
            
        elif data_source_type == "问卷":
            # 问卷：每个topic_text筛选是否存在#，#的放到qa_contents，不存在的放contents
            for text_item in topic_texts:
                if not isinstance(text_item, dict) or 'content' not in text_item:
                    self.logger.warning(f"跳过无效的topic_text项: {text_item}")
                    continue
                
                content = text_item['content']
                if '#' in content:
                    qa_contents.append(content)
                    self.logger.info(f"发现问卷内容: {content}")
                else:
                    contents.append(content)
            
            self.logger.info(f"问卷数据源: 普通内容 {len(contents)} 个, 问卷内容 {len(qa_contents)} 个")
            
        else:
            # 其他数据源：默认按评论方式处理
            self.logger.warning(f"未知数据源 {data_source_type}，使用默认处理方式")
            for text_item in topic_texts:
                if isinstance(text_item, dict) and 'content' in text_item:
                    contents.append(text_item['content'])
                else:
                    self.logger.warning(f"跳过无效的topic_text项: {text_item}")
                    continue
        
        self.logger.info(f"数据源: {data_source_type}, 总内容数: {len(contents)}, 问卷内容数: {len(qa_contents)}")
        return contents, qa_contents
    
    
    async def process_initial_message(self, message_data: dict):
        """处理初始消息"""
        try:
            message = InitialMessage(**message_data)
            # 更新cuizong消息时间
            self.last_cuizong_message_time = time.time()
            
            self.structured_logger.log_kafka_message(
                'received', self.kafka_client.TOPICS['VOC_TOMODEL_TOPIC'], message.topic_id
            )
            
            # 根据dataSource确定分流策略
            data_source_type = message.source_data.dataSource_type
            self.logger.info(f"数据来源: {data_source_type}")
            
            # 解析topic_texts，获取contents和qa_contents
            contents, qa_contents = self._parse_topic_texts(data_source_type, message.source_data.topic_text, message.source_data.title)
            
            # 处理问卷内容
            qaresult_data = []
            if qa_contents:
                qaresult_data = await self._process_qa_contents(qa_contents, message.source_data.brand, message.source_data.series)
            
            # 处理正常contents（走品牌车系数量分流）
            sentences = []
            filtered_contents = []
            for content in contents:
                # 检查文本长度，超过300字符的数据不处理
                if len(content) > 300:
                    self.logger.info(f"过滤超长文本（长度{len(content)}）: {content}")
                    continue
                
                filtered_contents.append(content)
                # 粗分句
                sentences += self.text_processor.split_sentences(content)
            
            # 更新contents为过滤后的内容
            contents = filtered_contents
            
            # 将所有正常content拼接成一个整体，统计品牌车系提及数量
            all_content = ' '.join(contents)  # 拼接所有正常content
            brand_series_count, brand_series_list = await self._count_brand_series_mysql(all_content)
            
            # 分流策略：完全基于品牌车系数量，不考虑data_source
            should_use_llm = brand_series_count >= 3

            # 根据策略分流
            if should_use_llm:
                # 发送到大模型处理（使用所有内容，包括问卷内容）
                category_message = CategoryMessage(
                    topic_id=message.topic_id,
                    source_data=message.source_data,
                    cat_type="llm",
                    raw_split_text=contents,  # 使用原始contents
                    retry_count=getattr(message, 'retry_count', 0),
                    error_type=getattr(message, 'error_type', ''),
                    error_msg=getattr(message, 'error_msg', ''),
                    last_ts=getattr(message, 'last_ts', ''),
                    brand_series_list=brand_series_list,
                    qaresult=qaresult_data
                )
                self.kafka_client.send_to_llm_topic(category_message)

                self.structured_logger.log_business_event(
                    'message_routed_to_llm',
                    topic_id=message.topic_id,
                    data_source=message.source_data,
                    brand_series_count=brand_series_count,
                    content_count=len(contents),
                    questionnaire_count=len(qa_contents)
                )
            else:
                # 发送到UIE处理（使用分句后的sentences，但包含问卷处理结果）
                category_message = CategoryMessage(
                    topic_id=message.topic_id,
                    source_data=message.source_data,
                    cat_type="uie",
                    raw_split_text=sentences,  # 使用分句后的sentences
                    retry_count=getattr(message, 'retry_count', 0),
                    error_type=getattr(message, 'error_type', ''),
                    error_msg=getattr(message, 'error_msg', ''),
                    last_ts=getattr(message, 'last_ts', ''),
                    qaresult=qaresult_data  # 传递问卷处理结果
                )
                self.kafka_client.send_to_uie_topic(category_message)

                self.structured_logger.log_business_event(
                    'message_routed_to_uie',
                    topic_id=message.topic_id,
                    data_source=message.source_data,
                    brand_series_count=brand_series_count,
                    sentences_count=len(sentences),
                    content_count=len(contents),
                    questionnaire_count=len(qa_contents)
                )
            
        except Exception as e:
            self.logger.error(f"Error processing initial message: {e}")
            
            # 发送错误消息  
            source_data = message_data.get('source_data', {})
            if isinstance(source_data, dict):
                source_data = SourceData(**source_data)
                
            error_message = ErrorMessage(
                topic_id=message_data.get('topic_id', 'unknown'),
                source_data=source_data,
                retry_count=message_data.get('retry_count', 0),
                error_type='text_processing_error',
                error_msg=str(e),
                last_ts=message_data.get('last_ts', '')
            )
            self.kafka_client.send_to_error_topic(error_message)
    
    async def process_retry_message(self, message_data: dict):
        """处理重试消息"""
        try:
            # 重试消息和初始消息处理逻辑相同
            await self.process_initial_message(message_data)
            
        except Exception as e:
            self.logger.error(f"Error processing retry message: {e}")
    
    async def start_consumers(self):
        """启动消费者 - 实现优先级消费逻辑"""
        # 启动优先级消费任务
        priority_consumer_task = asyncio.create_task(self.priority_consumer_loop())
        
        self.logger.info(f"Priority consumer started - {self.kafka_client.TOPICS['VOC_TOMODEL_TOPIC']} has priority, {self.kafka_client.TOPICS['MODEL_RETRY_TOPIC']} consumed after 10min idle")
    
    async def priority_consumer_loop(self):
        """优先级消费循环"""
        cuizong_consumer = None
        retry_consumer = None
        try:
            # 创建消费者
            cuizong_consumer = self.kafka_client.create_consumer(
                [self.kafka_client.TOPICS['VOC_TOMODEL_TOPIC']], 
                group_id='text-processor-cuizong-group',
                auto_offset_reset='earliest',
                enable_auto_commit=False  # 启用手动提交
            )
            retry_consumer = self.kafka_client.create_consumer(
                [self.kafka_client.TOPICS['MODEL_RETRY_TOPIC']], 
                group_id='text-processor-retry-group',
                auto_offset_reset='latest',
                enable_auto_commit=False  # 启用手动提交
            )
            
            self.logger.info("Created cuizong and retry consumers")
            
            while not self.shutdown_event.is_set():
                try:
                    # 优先检查VOC_TOMODEL_TOPIC
                    try:
                        cuizong_messages = await self._consume_batch(cuizong_consumer, timeout=1.0)
                    except Exception as consume_error:
                        self.logger.error(f"Error in _consume_batch for cuizong: {consume_error}")
                        cuizong_messages = []
                    
                    if cuizong_messages:
                        # 处理cuizong消息
                        for message in cuizong_messages:
                            try:
                                await self.process_initial_message(message.value)
                                # 消息处理成功后手动提交offset
                                cuizong_consumer.commit()
                                self.logger.debug(f"Manually committed offset for cuizong message")
                            except Exception as e:
                                self.logger.error(f"Error processing cuizong message: {e}")
                                # 处理失败时不提交offset，让消息重新被消费
                                continue
                        self.last_cuizong_message_time = time.time()
                    else:
                        # 检查是否超过10分钟没有cuizong消息
                        current_time = time.time()
                        # 确保last_cuizong_message_time和cuizong_idle_threshold不为None
                        if self.last_cuizong_message_time is None:
                            self.last_cuizong_message_time = current_time
                        if self.cuizong_idle_threshold is None:
                            self.cuizong_idle_threshold = 600  # 默认10分钟
                        if current_time - self.last_cuizong_message_time > self.cuizong_idle_threshold:
                            # 消费retry消息
                            try:
                                retry_messages = await self._consume_batch(retry_consumer, timeout=1.0)
                            except Exception as consume_error:
                                self.logger.error(f"Error in _consume_batch for retry: {consume_error}")
                                retry_messages = []
                            if retry_messages:
                                self.logger.info("Consuming retry messages due to cuizong idle > 10min")
                                for message in retry_messages:
                                    try:
                                        await self.process_retry_message(message.value)
                                        # 消息处理成功后手动提交offset
                                        retry_consumer.commit()
                                        self.logger.debug(f"Manually committed offset for retry message")
                                    except Exception as e:
                                        self.logger.error(f"Error processing retry message: {e}")
                                        # 处理失败时不提交offset，让消息重新被消费
                                        continue
                        
                        # 短暂等待避免空转
                        await asyncio.sleep(0.1)
                        
                except Exception as e:
                    self.logger.error(f"Error in priority consumer loop: {e}")
                    await asyncio.sleep(1)
                    
        except Exception as e:
            self.logger.error(f"Priority consumer loop failed: {e}")
            self.logger.error(f"Full traceback:\n{traceback.format_exc()}")
        finally:
            if cuizong_consumer:
                cuizong_consumer.close()
            if retry_consumer:
                retry_consumer.close()
    
    async def _consume_batch(self, consumer, timeout=1.0):
        """批量消费消息"""
        try:
            messages = []
            # 检查 consumer 是否为 None
            if consumer is None:
                self.logger.error("Consumer is None in _consume_batch")
                return []
            
            # 使用 poll 方法获取消息，timeout_ms 参数以毫秒为单位
            # 确保 timeout 不为 None 且为数字类型
            self.logger.debug(f"_consume_batch called with consumer={consumer}, timeout={timeout}, type={type(timeout)}")
            if timeout is None or not isinstance(timeout, (int, float)):
                self.logger.warning(f"Invalid timeout value: {timeout}, using default 1.0")
                timeout = 1.0
            
            try:
                timeout_ms = int(float(timeout) * 1000)
                self.logger.debug(f"Calculated timeout_ms: {timeout_ms}")
            except Exception as timeout_error:
                self.logger.error(f"Error calculating timeout_ms: {timeout_error}, timeout={timeout}")
                timeout_ms = 1000  # 默认1秒
            
            # 在异步环境中运行同步的 poll 方法
            def poll_messages():
                try:
                    self.logger.debug(f"Polling consumer with timeout_ms={timeout_ms}, max_records=10")
                    result = consumer.poll(timeout_ms=timeout_ms, max_records=10)
                    self.logger.debug(f"Poll result type: {type(result)}, keys: {list(result.keys()) if hasattr(result, 'keys') else 'N/A'}")
                    return result
                except Exception as poll_error:
                    self.logger.error(f"Error in consumer.poll: {poll_error}")
                    return {}
            
            # 在线程池中运行同步方法
            try:
                loop = asyncio.get_event_loop()
                self.logger.debug(f"Got event loop: {loop}")
                message_batch = await loop.run_in_executor(
                    None, poll_messages
                )
                self.logger.debug(f"Executor completed, message_batch type: {type(message_batch)}")
            except Exception as executor_error:
                self.logger.error(f"Error in run_in_executor: {executor_error}")
                return []
            
            # 处理返回的消息批次
            for topic_partition, records in message_batch.items():
                for record in records:
                    messages.append(record)
                    if len(messages) >= 10:  # 限制批次大小
                        break
                if len(messages) >= 10:
                    break
            
            return messages
        except Exception as e:
            self.logger.error(f"Error consuming batch: {e}")
            return []
    
    async def _service_specific_health_check(self) -> dict:
        """服务特定的健康检查"""
        return {
            'elasticsearch': 'connected',
            'text_processor': 'ready' if self.text_processor else 'not_ready'
        }
    
    def _add_custom_routes(self, app: FastAPI):
        """添加服务特定的路由"""
        @app.post("/process")
        async def process_text(message: InitialMessage):
            """手动处理文本（用于测试）"""
            try:
                await self.process_initial_message(message.model_dump())
                return APIResponse(success=True, message="Text processed successfully")
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @app.post("/count_brand_series")
        async def count_brand_series(request: dict):
            """统计品牌车系提及数量"""
            try:
                text = request.get('text', '')
                if not text:
                    raise HTTPException(status_code=400, detail="Text is required")

                result = await self._count_brand_series_mysql(text)
                return APIResponse(
                    success=True,
                    message="Brand series count calculated successfully",
                    data=result
                )
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @app.post("/test_smart_filter")
        async def test_smart_filter(request: dict):
            """测试智能过滤功能"""
            import time
            try:
                test_texts = request.get('test_texts', [])
                if not test_texts:
                    # 使用默认测试文本
                    test_texts = [
                        "9月8日，恒生电子涨1.91%，成交额14.23亿元，换手率2.15%，总市值665.90亿元。华夏沪深300ETF位居第十大流通股东",
                        "比亚迪汉EV真的很不错，动力强劲，内饰豪华",
                        "华泰柏瑞ETF基金表现良好，收益率达到7.02%",
                        "这个武汉的好汉开着比亚迪元去了大汉口",
                        "海豹突击队使用的装备很先进，海豹家族庞大"
                    ]
                
                results = []
                total_start_time = time.time()
                
                for i, text in enumerate(test_texts):
                    start_time = time.time()
                    
                    # 测试原始匹配（不过滤）
                    original_count, original_list = await self._count_brand_series_mysql_without_filter(text)
                    
                    # 测试智能过滤
                    filtered_count, filtered_list = await self._count_brand_series_mysql(text)
                    
                    end_time = time.time()
                    processing_time = (end_time - start_time) * 1000  # 毫秒
                    
                    results.append({
                        'text': text[:100] + "..." if len(text) > 100 else text,
                        'original_count': original_count,
                        'filtered_count': filtered_count,
                        'filtered_items': [f"{item['brand']}-{item['series']}" for item in filtered_list],
                        'processing_time_ms': round(processing_time, 2),
                        'filter_effectiveness': f"{original_count - filtered_count} items filtered"
                    })
                
                total_end_time = time.time()
                total_time = (total_end_time - total_start_time) * 1000
                
                return APIResponse(
                    success=True,
                    message="Smart filter test completed",
                    data={
                        'test_results': results,
                        'total_processing_time_ms': round(total_time, 2),
                        'average_time_per_text_ms': round(total_time / len(test_texts), 2),
                        'filter_rules_loaded': len(await self._load_filter_rules())
                    }
                )
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))
        
        @app.post("/reload_filter_rules")
        async def reload_filter_rules():
            """重新加载过滤规则"""
            try:
                # 清空缓存
                self._filter_rules_cache = None
                self._filter_rules_cache_time = 0
                
                # 重新加载
                rules = await self._load_filter_rules()
                
                return APIResponse(
                    success=True,
                    message="Filter rules reloaded successfully",
                    data={
                        'global_blacklist_count': len(rules['global_blacklist']),
                        'context_rule_count': len(rules['context_rule']),
                        'ambiguous_pattern_count': len(rules['ambiguous_pattern'])
                    }
                )
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))
    
    async def _shutdown_service_specific(self):
        """服务特定的关闭逻辑"""
        # 关闭 Elasticsearch 客户端
        if hasattr(self, 'es_client') and self.es_client:
            await self.es_client.close()
        
        # 关闭 MySQL 客户端
        if self.mysql_client:
            await self.mysql_client.close()
        
        # 关闭过滤规则 MySQL 客户端
        if self.filter_mysql_client:
            await self.filter_mysql_client.close()


# 创建服务实例
service = TextProcessorService()

# 创建FastAPI应用
app = service.create_fastapi_app(
    title="Text Processor Service",
    description="文本预处理和分流服务"
)


if __name__ == "__main__":
    service.run_with_app_string("main:app")