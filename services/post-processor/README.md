# Post-Processor 后处理服务

## 服务概述

Post-Processor 后处理服务是 VOC2.0 智能打标系统的重要组成部分，负责接收 `result_topic` 的消息，进行二级情感分析和用车场景分析，最终输出到 `final_topic`。

## 主要功能

### 1. 二级情感分析
- 基于基础情感（正面/负面/中性）进行细粒度分析
- 支持情感强度分级：
  - **正面**: 高/中/一般
  - **负面**: 高/中/一般

### 2. 用车场景分析
- 识别文本中隐含的用车场景
- 支持场景类型：
  - 日常通勤
  - 长途驾驶
  - 家庭出游
  - 商务出行
  - 越野探险
  - 高速行驶
  - 市区拥堵
  - 城市代步

## 技术架构

### 数据流
```
result_topic → Post-Processor → final_topic
```

### 数据库依赖
- **MySQL**: 存储情感和场景映射规则
- **Kafka**: 消息队列通信

### 核心表结构

#### 1. sentiment_mapping - 二级情感分析映射表
```sql
CREATE TABLE sentiment_mapping (
    id BIGINT NOT NULL AUTO_INCREMENT,
    sentiment VARCHAR(20) NOT NULL COMMENT '基础情感：正面/负面/中性',
    sub_sentiment VARCHAR(20) NOT NULL COMMENT '二级情感：高/中/一般',
    description_keywords TEXT COMMENT '描述关键词，用于匹配',
    aspect_keywords TEXT COMMENT '方面关键词，用于匹配',
    confidence FLOAT DEFAULT 0.8 COMMENT '置信度',
    is_active TINYINT DEFAULT 1 COMMENT '是否激活',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);
```

#### 2. usage_scenario_mapping - 用车场景分析映射表
```sql
CREATE TABLE usage_scenario_mapping (
    id BIGINT NOT NULL AUTO_INCREMENT,
    usage_scenario VARCHAR(50) NOT NULL COMMENT '用车场景',
    description_keywords TEXT COMMENT '描述关键词，用于匹配',
    aspect_keywords TEXT COMMENT '方面关键词，用于匹配',
    segment_keywords TEXT COMMENT '原文片段关键词，用于匹配',
    confidence FLOAT DEFAULT 0.8 COMMENT '置信度',
    is_active TINYINT DEFAULT 1 COMMENT '是否激活',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);
```

## 输入输出格式

### 输入格式 (result_topic)
```json
{
  "topic_id": "0000001",
  "source_data": {
    "dataSource": "汽车之家",
    "title": "CS75 PLUS使用一个月感受",
    "topic_text": [
      {
        "role": "customer",
        "content": "这个车的大灯真的很亮，晚上开车很安全"
      }
    ],
    "brand": "长安",
    "series": "CS75 PLUS",
    "create_time": "2025-01-15 12:00:00"
  },
  "result": [
    {
      "segment": "这个车的大灯真的很亮",
      "brand": "长安",
      "series": "CS75 PLUS",
      "sentiment": "正面",
      "intent": "表扬",
      "aspect": "大灯",
      "description": "很亮",
      "normalized_opinion": {
        "id": "OP_00000027",
        "text": "大灯-很亮"
      },
      "standard_keyword": {
        "id": "CA_STD_011234",
        "text": "大灯很亮"
      }
    }
  ]
}
```

### 输出格式 (final_topic)
```json
{
  "nlpParam": {
    "topic_id": "0000001",
    "source_data": {
      "dataSource": "汽车之家",
      "title": "CS75 PLUS使用一个月感受",
      "topic_text": [
        {
          "role": "customer",
          "content": "这个车的大灯真的很亮，晚上开车很安全"
        }
      ],
      "brand": "长安",
      "series": "CS75 PLUS",
      "create_time": "2025-01-15 12:00:00"
    }
  },
  "nlpResult": {
    "dimensions": [
      {
        "segment": "这个车的大灯真的很亮",
        "brand": "长安",
        "series": "CS75 PLUS",
        "sentiment": "正面",
        "sub_sentiment": "高",
        "usage_scenario": "日常通勤",
        "intent": "表扬",
        "aspect": "大灯",
        "description": "很亮",
        "normalized_opinion": {
          "id": "OP_00000027",
          "text": "大灯-很亮"
        },
        "standard_keyword": {
          "id": "CA_STD_011234",
          "text": "大灯很亮"
        }
      }
    ]
  }
}
```

## 部署配置

### 服务配置
```yaml
# configs/config.yaml
services:
  post-processor:
    host: 0.0.0.0
    port: 8180
    debug: false
    log_level: INFO
    version: 1.0.0
```

### Docker Compose
```yaml
post-processor:
  image: voc-services:latest
  container_name: voc-post-processor
  ports:
    - 8180:8180
  environment:
    - SERVICE_NAME=post-processor
    - SERVICE_PORT=8180
    - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
    - MYSQL_HOST=mysql
    - MYSQL_PORT=3306
    - MYSQL_USER=root
    - MYSQL_PASSWORD=
  volumes:
    - ./configs:/app/configs
    - ./logs/prod:/app/logs
  networks:
    - voc-network
  depends_on:
    - kafka
    - mysql
  restart: unless-stopped
```

## API 端点

### 健康检查
```http
GET /health
```

### 手动处理测试
```http
POST /process
Content-Type: application/json

{
  "dataSource": "汽车之家",
  "title": "测试标题",
  "topic_text": [
    {
      "role": "customer",
      "content": "这个车的大灯真的很亮"
    }
  ],
  "brand": "长安",
  "series": "CS75 PLUS",
  "create_time": "2025-01-15 12:00:00",
  "result": [
    {
      "segment": "这个车的大灯真的很亮",
      "sentiment": "正面",
      "aspect": "大灯",
      "description": "很亮"
    }
  ]
}
```

### 获取配置信息
```http
GET /sentiment-levels
GET /usage-scenarios
```

## 运行和测试

### 1. 创建数据库表
```bash
python scripts/test/create_post_processor_tables.py
```

### 2. 创建Kafka Topic
```bash
python scripts/test/create_kafka_topics_lightweight.py
```

### 3. 启动服务
```bash
# 开发环境
python services/post-processor/main.py

# 生产环境
docker-compose up post-processor
```

### 4. 运行测试
```bash
python test_post_processor.py
```

## 算法逻辑

### 二级情感分析算法
1. 从MySQL查询情感映射表
2. 基于描述关键词和方面关键词匹配
3. 如果数据库匹配失败，使用规则方法
4. 返回对应的二级情感

### 用车场景分析算法
1. 从MySQL查询场景映射表
2. 基于描述、方面、原文片段关键词匹配
3. 如果数据库匹配失败，使用关键词规则
4. 返回对应的用车场景

## 监控和日志

### 日志级别
- INFO: 正常处理流程
- WARNING: 数据库查询失败，使用规则方法
- ERROR: 处理异常，发送错误消息

### 关键指标
- 消息处理成功率
- 数据库查询响应时间
- 情感分析准确率
- 场景分析准确率

## 扩展和定制

### 添加新的情感级别
1. 更新 `sentiment_levels` 配置
2. 在数据库中添加对应映射规则
3. 更新规则匹配逻辑

### 添加新的用车场景
1. 更新 `usage_scenarios` 配置
2. 在数据库中添加对应映射规则
3. 更新关键词匹配逻辑

### 自定义分析规则
1. 修改 `_get_default_sub_sentiment` 方法
2. 修改 `_get_default_usage_scenario` 方法
3. 添加新的规则匹配逻辑
