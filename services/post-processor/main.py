"""
后处理服务
负责接收result_topic的消息，进行二级情感分析和用车场景分析，
从MySQL数据库获取数据，输出到final_topic
"""
import asyncio
import signal
import sys
from pathlib import Path
from typing import List, Dict, Any, Optional
import json
import time

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from fastapi import FastAPI, HTTPException, Request
from contextlib import asynccontextmanager
import uvicorn

from shared.base import BaseService
from shared.database.mysql_client import MySQLClient
from shared.models.schemas import (
    ModelResultMessage, ErrorMessage, SourceData, APIResponse
)
from shared.utils.config import get_config_manager


class PostProcessorService(BaseService):
    """后处理服务"""
    
    def __init__(self):
        super().__init__('post-processor')
        self.mysql_client = None
        
        # 二级情感分析配置
        self.sentiment_levels = {
            '正面': ['高', '中', '一般'],
            '负面': ['高', '中', '一般']
        }
        
        # 用车场景配置
        self.usage_scenarios = [
            '日常通勤', '长途驾驶', '城市代步', '商务出行', 
            '家庭出游', '越野探险', '高速行驶', '市区拥堵'
        ]
    
    def _init_service_specific_config(self):
        """初始化服务特定配置"""
        # 获取数据库配置（默认）
        self.database_config = self.config_manager.get_database_config()
        # 覆盖为 MySQL 专用配置（优先环境变量，再读 configs/config.yaml 的 database.mysql.*）
        db_host = self.config_manager._get_env_or_config('MYSQL_HOST', 'database.mysql.host', self.database_config.host)
        db_port = self.config_manager._get_env_or_config('MYSQL_PORT', 'database.mysql.port', self.database_config.port)
        db_user = self.config_manager._get_env_or_config('MYSQL_USERNAME', 'database.mysql.username', self.database_config.username)
        db_pass = self.config_manager._get_env_or_config('MYSQL_PASSWORD', 'database.mysql.password', self.database_config.password)
        # 非品牌车系服务：数据库名仅使用 MYSQL_DATABASE/DB_DATABASE → database.mysql.database
        db_name = self.config_manager._get_env_or_config('MYSQL_DATABASE', 'database.mysql.database', self.config_manager._get_env_or_config('DB_DATABASE', 'database.mysql.database', self.database_config.database))
        # 应用覆盖项
        self.database_config.host = db_host
        self.database_config.port = int(db_port)
        self.database_config.username = db_user
        self.database_config.password = db_pass
        self.database_config.database = db_name
        
    
    async def _initialize_service_specific(self):
        """初始化服务特定组件"""
        # 初始化MySQL客户端
        self.mysql_client = MySQLClient(self.database_config)
        await self.mysql_client.initialize()
        
        self.logger.info("Post-processor service initialized successfully")
    
    async def start_consumers(self):
        """启动消费者"""
        try:
            # 启动result_topic消费者
            self.kafka_client.start_consumer(
                [self.kafka_client.TOPICS['RESULT_TOPIC']], 
                self.process_result_message,
                group_id='post-processor-service-group',
                consumer_name=None,
                auto_offset_reset='latest',
                enable_auto_commit=False  # 启用手动提交
            )
            self.logger.info("Post-processor consumers started")
        except Exception as e:
            self.logger.error(f"启动消费者时发生异常: {str(e)}")
            try:
                error_message = ErrorMessage(
                    topic_id='unknown',
                    source_data=SourceData(
                        dataSource="post-processor",
                        title="",
                        topic_text=[],
                        brand="",
                        series="",
                        ext="",
                        create_time=""
                    ),
                    retry_count=0,
                    error_type="post_processor_consumer_error",
                    error_msg=f"Error starting post-processor consumers: {str(e)}",
                    last_ts=str(int(time.time()))
                )
                self.kafka_client.send_to_error_topic(error_message)
            except Exception as inner_e:
                self.logger.error(f"发送错误消息到Kafka时发生异常: {str(inner_e)}")
    
    async def process_result_message(self, message_data: dict):
        """处理result_topic消息"""
        try:
            self.logger.info(f"Processing result message: {message_data.get('topic_id', 'unknown')}")
            
            # 解析消息
            result_message = ModelResultMessage(**message_data)
            
            # 进行二级情感分析
            enhanced_results = await self.perform_secondary_sentiment_analysis(result_message.result, result_message.qaresult)
            
            # 进行用车场景分析
            enhanced_results = await self.perform_usage_scenario_analysis(enhanced_results, result_message.source_data.usage_scenario_src)

            
            
            # 格式化增强结果
            formatted_results = self.format_enhanced_results(enhanced_results)
            
            # 构建最终输出消息
            final_message = {
                "nlpParam": {
                    "topic_id": result_message.topic_id,
                    "source_data": result_message.source_data.model_dump()
                },
                "nlpResult": {
                    "dimensions": formatted_results
                }
            }
            
            # 发送到final_topic
            self.kafka_client.send_message(
                self.kafka_client.TOPICS.get('FINAL_TOPIC', 'final_topic'),
                final_message,
                key=result_message.topic_id
            )
            
            self.structured_logger.log_kafka_message(
                'sent', 'final_topic', result_message.topic_id
            )
            
            self.logger.info(f"Final result sent to final_topic for topic_id: {result_message.topic_id}")
            
        except Exception as e:
            self.logger.error(f"处理result消息时发生异常: {str(e)}")
            try:
                sd = message_data.get('source_data', {}) if isinstance(message_data, dict) else {}
                if isinstance(sd, dict):
                    source_data = SourceData(**sd)
                else:
                    source_data = SourceData(
                        dataSource="post-processor", title="", topic_text=[], brand="", series="", ext="", create_time=""
                    )
                error_message = ErrorMessage(
                    topic_id=message_data.get('topic_id', 'unknown') if isinstance(message_data, dict) else 'unknown',
                    source_data=source_data,
                    retry_count=message_data.get('retry_count', 0) if isinstance(message_data, dict) else 0,
                    error_type="post_processor_error",
                    error_msg=str(e),
                    last_ts=str(int(time.time()))
                )
                self.kafka_client.send_to_error_topic(error_message)
            except Exception as inner_e:
                self.logger.error(f"发送错误消息到Kafka时发生异常: {str(inner_e)}")
    
    async def perform_secondary_sentiment_analysis(self, results: List[Dict[str, Any]], qaresults: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """执行二级情感分析和意图审核"""
        enhanced_results = []
        
        for result in results:
            enhanced_result = result.copy()
            
            # 获取基础情感
            sentiment = result.get('sentiment', '中性')
            original_text = result.get('original_text', '') or ''
            segment = result.get('segment', '') or ''
            
            # 根据情感与原文片段segment确定二级情感（results 使用关键词规则）
            sub_sentiment = await self.determine_sub_sentiment(sentiment, original_text)
            enhanced_result['sub_sentiment'] = sub_sentiment
            
            # 意图审核机制：基于情感和文本内容修正意图
            corrected_intent = await self.review_and_correct_intent(
                sentiment, 
                result.get('intent', ''), 
                segment or original_text
            )
            enhanced_result['intent'] = corrected_intent
            
            enhanced_results.append(enhanced_result)
        
        for qaresult in qaresults:
            enhanced_qaresult = qaresult.copy()
            auto_primary, auto_secondary = await self.infer_sentiments_for_text(qaresult.get('segment', ''))
            if auto_primary:
                enhanced_qaresult['sentiment'] = auto_primary
            enhanced_qaresult['sub_sentiment'] = auto_secondary
            
            enhanced_results.append(enhanced_qaresult)
        
        return enhanced_results
    
    async def determine_sub_sentiment(self, sentiment: str, original_text: str, rule_type: str = '关键词') -> str:
        """确定二级情感（按规则类型）。"""
        try:
            # 中性没有二级情感
            if sentiment == '中性':
                return ''

            # 拉取规则（直接查询MySQL）
            rules = await self.mysql_client.fetch_secondary_sentiment_rules(sentiment, rule_type)
            if not rules:
                return '一般'

            # 规则匹配
            matched_levels: List[str] = []
            for rule in rules:
                keyword = (rule.get('rule_keywords') or '').strip()
                if not keyword:
                    continue
                if keyword in original_text:
                    level = rule.get('secondary_sentiment', '')
                    if level:
                        matched_levels.append(level)

            if not matched_levels:
                return '一般'

            # 优先级：高 > 中 > 一般
            priority = {'高': 3, '中': 2, '一般': 1}
            matched_levels.sort(key=lambda x: priority.get(x, 0), reverse=True)
            return matched_levels[0]

        except Exception as e:
            self.logger.warning(f"确定二级情感时发生异常: {str(e)}")
            return ''

    async def infer_sentiments_for_text(self, original_text: str, rule_type: str = '分值') -> (str, str):
        """基于 original_text 和指定的 rule_type 进行匹配并返回 (primary, secondary)。
        - 当 rule_type='分值' 时：一次性取出该类型的所有规则，然后用 original_text 做匹配，命中即返回对应的一级/二级情感。
        - 其他类型：逐个一级情感拉取并匹配（与之前一致）。
        """
        try:
            if not original_text:
                return '', ''

            if rule_type == '分值':
                rules = await self.mysql_client.fetch_all_secondary_sentiment_rules_by_type(rule_type)
                for rule in rules:
                    keyword = (rule.get('rule_keywords') or '').strip()
                    if not keyword:
                        continue
                    if keyword in original_text:
                        primary = (rule.get('primary_sentiment') or '').strip()
                        secondary = (rule.get('secondary_sentiment') or '').strip()
                        return primary, secondary
                return '', ''

            return '', ''
        except Exception:
            return '', ''
    
    async def review_and_correct_intent(self, sentiment: str, original_intent: str, text: str) -> str:
        """
        意图审核机制：基于情感和文本内容修正意图识别错误
        
        规则：
        1. 负面情感 → 一定是抱怨
        2. 正面情感 → 一定是表扬
        3. 中性情感 → 通过关键词规则区分咨询和建议
        
        Args:
            sentiment: 情感标签（正面/负面/中性）
            original_intent: 原始意图标签
            text: 文本内容（用于中性情感的进一步判断）
            
        Returns:
            修正后的意图标签
        """
        try:
            # 规则1：负面情感一定是抱怨
            if sentiment == '负面':
                if original_intent != '抱怨':
                    self.logger.info(f"意图审核：负面情感修正 '{original_intent}' → '抱怨'")
                return '抱怨'
            
            # 规则2：正面情感一定是表扬
            if sentiment == '正面':
                if original_intent != '表扬':
                    self.logger.info(f"意图审核：正面情感修正 '{original_intent}' → '表扬'")
                return '表扬'
            
            # 规则3：中性情感需要通过关键词区分咨询和建议
            if sentiment == '中性':
                corrected_intent = await self.distinguish_inquiry_vs_suggestion(text, original_intent)
                if corrected_intent != original_intent:
                    self.logger.info(f"意图审核：中性情感修正 '{original_intent}' → '{corrected_intent}'")
                return corrected_intent
            
            # 其他情况保持原意图
            return original_intent
            
        except Exception as e:
            self.logger.warning(f"意图审核时发生异常: {str(e)}")
            # 异常时返回原意图
            return original_intent
    
    async def distinguish_inquiry_vs_suggestion(self, text: str, original_intent: str) -> str:
        """
        区分咨询和建议（用于中性情感的意图判断）
        
        区分规则：
        - 咨询关键词：怎么样、如何、什么、哪个、多少、是否、能不能、可以吗、有没有、请问等
        - 建议关键词：应该、建议、推荐、最好、可以考虑、不如、要是、如果能、希望等
        - 优先级：建议 > 咨询（如果同时包含，优先判定为建议）
        
        Args:
            text: 文本内容
            original_intent: 原始意图
            
        Returns:
            修正后的意图（咨询或建议）
        """
        try:
            if not text:
                return original_intent
            
            # 建议关键词（优先级更高）
            suggestion_keywords = [
                '应该', '建议', '推荐', '最好', '可以考虑', '不如', '要是', '如果能', 
                '希望', '期望', '要求', '需要改进', '改善', '优化', '完善', '提升',
                '增加', '减少', '取消', '调整', '修改', '更换', '升级'
            ]
            
            # 咨询关键词
            inquiry_keywords = [
                '怎么样', '如何', '什么', '哪个', '哪种', '哪里', '多少', '几个',
                '是否', '能不能', '可以吗', '有没有', '请问', '想了解', '想知道',
                '咨询', '询问', '请教', '问一下', '了解一下', '知道吗', '清楚吗',
                '？', '?', '吗', '呢', '么'
            ]
            
            # 检查建议关键词（优先级高）
            for keyword in suggestion_keywords:
                if keyword in text:
                    return '建议'
            
            # 检查咨询关键词
            for keyword in inquiry_keywords:
                if keyword in text:
                    return '咨询'
            
            # 如果都没有匹配到关键词，处理逻辑：
            # 1. 如果原意图已经是咨询或建议，保持不变
            # 2. 如果原意图不是咨询或建议，则默认为咨询（中性情感下的保守选择）
            if original_intent in ['咨询', '建议']:
                return original_intent
            else:
                return '咨询'
            
        except Exception as e:
            self.logger.warning(f"区分咨询和建议时发生异常: {str(e)}")
            return original_intent
    
    # 说明：fetch_secondary_sentiment_rules 已移除，直接使用 mysql_client.fetch_secondary_sentiment_rules
    
    async def perform_usage_scenario_analysis(self, results: List[Dict[str, Any]], usage_scenario_src: str) -> List[Dict[str, Any]]:
        """执行用车场景分析"""
        enhanced_results = []
        
        for result in results:
            enhanced_result = result.copy()
            
            # 根据描述内容和方面确定用车场景
            usage_scenario = await self.determine_usage_scenario(result, usage_scenario_src)
            enhanced_result['usage_scenario'] = usage_scenario
            
            enhanced_results.append(enhanced_result)
        
        return enhanced_results
    
    async def determine_usage_scenario(self, result: Dict[str, Any], usage_scenario_src: str) -> str:
        """确定用车场景
        规则：
        - 从数据库取出全部用车场景规则
        - 若某条规则的 rule_keywords 出现在 segment 中，先收集所有命中
        - 若命中多个：先按一级场景优先级筛选（行车速度>操作情景>路况>里程数>用车时间>使用场景>车主画像>天气）
        - 若仍有多条（同一级场景不同二级场景），按关键词与观点的字符距离最小者
        - 若仍未唯一，则返回第一条
        - 未命中则返回空字符串
        """
        try:
            segment = result.get('segment', '') or ''
            aspect = result.get('aspect') or {}
            description = result.get('description') or {}

            # 读取全部规则
            rules = await self.fetch_usage_scenario_rules()

            # 若传入 usage_scenario_src，则优先用其与规则关键词匹配；匹配多条时仅按一级场景优先级选择最高者（不计算距离）
            if usage_scenario_src:
                simple_matches: List[Dict[str, Any]] = []
                for rule in rules:
                    keyword = (rule.get('rule_keywords') or '').strip()
                    if not keyword:
                        continue
                    if keyword in usage_scenario_src:
                        primary = (rule.get('primary_scenario') or '').strip()
                        secondary = (rule.get('secondary_scenario') or '').strip()
                        if primary and secondary:
                            simple_matches.append({'primary': primary, 'secondary': secondary})
                if simple_matches:
                    primary_priority = [
                        '行车速度', '操作情景', '路况', '里程数', '用车时间', '使用场景', '车主画像', '天气'
                    ]
                    priority_index = {name: idx for idx, name in enumerate(primary_priority)}
                    best_priority = min([priority_index.get(m['primary'], 10**6) for m in simple_matches])
                    top_matches = [m for m in simple_matches if priority_index.get(m['primary'], 10**6) == best_priority]
                    return f"{top_matches[0]['primary']}-{top_matches[0]['secondary']}"
            matches: List[Dict[str, Any]] = []
            for rule in rules:
                keyword = (rule.get('rule_keywords') or '').strip()
                if not keyword:
                    continue
                kw_pos = segment.find(keyword)
                if kw_pos == -1:
                    continue
                primary = (rule.get('primary_scenario') or '').strip()
                secondary = (rule.get('secondary_scenario') or '').strip()
                if not primary or not secondary:
                    continue
                # 计算与观点的距离
                distance = self._compute_keyword_opinion_distance(segment, keyword, aspect, description)
                matches.append({
                    'primary': primary,
                    'secondary': secondary,
                    'keyword': keyword,
                    'distance': distance
                })

            if not matches:
                return ''

            # 一级场景优先级
            primary_priority = [
                '行车速度', '操作情景', '路况', '里程数', '用车时间', '使用场景', '车主画像', '天气'
            ]
            priority_index = {name: idx for idx, name in enumerate(primary_priority)}

            # 找到最高优先级（最小索引）的一级场景集合
            best_priority = min([priority_index.get(m['primary'], 10**6) for m in matches])
            top_matches = [m for m in matches if priority_index.get(m['primary'], 10**6) == best_priority]

            if len(top_matches) == 1:
                return f"{top_matches[0]['primary']}-{top_matches[0]['secondary']}"

            # 同一级别下按距离最近者
            top_matches.sort(key=lambda x: (x['distance'], x['secondary']))
            return f"{top_matches[0]['primary']}-{top_matches[0]['secondary']}"
        except Exception as e:
            self.logger.warning(f"确定用车场景时发生异常: {str(e)}")
            return ''

    def _compute_keyword_opinion_distance(self, segment: str, keyword: str, aspect: Dict[str, Any], description: Dict[str, Any]) -> int:
        """计算关键词与观点(aspect/description)在 segment 中的最小字符距离。
        若均不存在或无法定位，则返回较大值以降低优先级。
        """
        try:
            if not segment or not keyword:
                return 10**6
            # 关键词位置
            kw_pos = segment.find(keyword)
            if kw_pos == -1:
                return 10**6
            kw_center = kw_pos + len(keyword) // 2

            distances: List[int] = []
            # aspect 距离
            try:
                a_start = int(aspect.get('start')) if isinstance(aspect.get('start'), (int, float)) else None
                a_end = int(aspect.get('end')) if isinstance(aspect.get('end'), (int, float)) else None
                if a_start is None or a_end is None:
                    a_text = (aspect.get('text') or '').strip()
                    if a_text:
                        a_pos = segment.find(a_text)
                        if a_pos != -1:
                            a_start = a_pos
                            a_end = a_pos + len(a_text)
                if a_start is not None and a_end is not None and a_end >= a_start:
                    a_center = a_start + (a_end - a_start) // 2
                    distances.append(abs(kw_center - a_center))
            except Exception:
                pass
            # description 距离
            try:
                d_start = int(description.get('start')) if isinstance(description.get('start'), (int, float)) else None
                d_end = int(description.get('end')) if isinstance(description.get('end'), (int, float)) else None
                if d_start is None or d_end is None:
                    d_text = (description.get('text') or '').strip()
                    if d_text:
                        d_pos = segment.find(d_text)
                        if d_pos != -1:
                            d_start = d_pos
                            d_end = d_pos + len(d_text)
                if d_start is not None and d_end is not None and d_end >= d_start:
                    d_center = d_start + (d_end - d_start) // 2
                    distances.append(abs(kw_center - d_center))
            except Exception:
                pass

            if distances:
                return min(distances)
            # 无有效观点位置
            return 10**5
        except Exception:
            return 10**6
    
    # 说明：query_scenario_mapping 已移除，请直接调用 self.mysql_client.query_scenario_mapping

    async def fetch_usage_scenario_rules(self) -> List[Dict[str, Any]]:
        """读取全部用车场景规则
        期望库表：scenario_rule_keywords(primary_scenario, secondary_scenario, rule_keywords)
        rule_keywords 为单个关键词
        """
        try:
            if not hasattr(self, '_usage_scenario_rules_cache'):
                self._usage_scenario_rules_cache = None
            if self._usage_scenario_rules_cache is not None:
                return self._usage_scenario_rules_cache

            rows = await self.mysql_client.fetch_all_usage_scenario_rules()
            self._usage_scenario_rules_cache = rows or []
            return self._usage_scenario_rules_cache
        except Exception as e:
            self.logger.warning(f"查询用车场景规则时发生异常: {str(e)}")
            return []
    
    def format_enhanced_results(self, enhanced_results: list) -> list:
        """
        格式化增强结果，确保输出结构的一致性和完整性
        
        Args:
            enhanced_results: 增强后的结果列表
            
        Returns:
            格式化后的结果列表，包含完整的字段结构
        """
        formatted_results = []
        
        try:
            for item in enhanced_results:
                if not isinstance(item, dict):
                    self.logger.warning(f"跳过非字典类型的结果项: {type(item)}")
                    continue
                
                # 提取aspect字段的文本内容
                aspect_text = ""
                if isinstance(item.get("aspect"), dict):
                    aspect_text = item["aspect"].get("text", "")
                elif isinstance(item.get("aspect"), str):
                    aspect_text = item["aspect"]
                
                # 提取description字段的文本内容
                description_text = ""
                if isinstance(item.get("description"), dict):
                    description_text = item["description"].get("text", "")
                elif isinstance(item.get("description"), str):
                    description_text = item["description"]
                
                # 构建标准化的结果对象
                formatted_item = {
                    # 基础信息
                    "segment": item.get("segment", ""),
                    "brand": item.get("brand", ""),
                    "series": item.get("series", ""),
                    "car_level1": item.get("car_level1", ""),
                    "car_level2": item.get("car_level2", ""),
                    
                    # 情感分析
                    "sentiment": item.get("sentiment", ""),
                    "sub_sentiment": item.get("sub_sentiment", ""),
                    
                    # 场景和意图
                    "usage_scenario": item.get("usage_scenario", ""),
                    "intent": item.get("intent", ""),
                    
                    # 观点信息
                    "aspect": aspect_text,
                    "description": description_text,
                    
                    # 标准化观点 - 使用normalized_opinion
                    "normalized_opinion": self._format_normalized_field(
                        item.get("normalized_opinion")
                    ),
                    
                    # 标准关键词 - 使用std_viewpoint作为standard_keyword
                    "standard_keyword": self._format_normalized_field(
                        item.get("std_viewpoint")
                    )
                }
                
                formatted_results.append(formatted_item)
                
        except Exception as e:
            self.logger.error(f"格式化增强结果时发生错误: {e}")
            # 发生错误时返回原始结果
            return enhanced_results if isinstance(enhanced_results, list) else []
        
        self.logger.info(f"成功格式化 {len(formatted_results)} 个结果项")
        return formatted_results
    
    def _format_normalized_field(self, field_value) -> dict:
        """
        格式化标准化字段（normalized_opinion 和 standard_keyword）
        
        Args:
            field_value: 字段值，可能是字典、字符串或None
            
        Returns:
            标准化的字典格式 {"id": "", "text": ""}
        """
        if isinstance(field_value, dict):
            return {
                "id": field_value.get("id", ""),
                "text": field_value.get("text", "")
            }
        elif isinstance(field_value, str):
            # 如果是字符串，作为text使用，id为空
            return {
                "id": "",
                "text": field_value
            }
        else:
            # 其他情况返回空结构
            return {
                "id": "",
                "text": ""
            }
    
    async def _service_specific_health_check(self) -> dict:
        """服务特定的健康检查"""
        # 检查MySQL连接
        mysql_status = "unknown"
        try:
            # 执行简单查询测试连接
            await self.mysql_client.execute_query("SELECT 1")
            mysql_status = "healthy"
        except Exception as e:
            mysql_status = "unhealthy"
            self.logger.warning(f"MySQL health check failed: {e}")
        
        return {
            'mysql': mysql_status,
            'sentiment_levels_configured': len(self.sentiment_levels),
            'usage_scenarios_configured': len(self.usage_scenarios)
        }
    
    def _add_custom_routes(self, app: FastAPI):
        """添加服务特定的路由"""
        @app.exception_handler(Exception)
        async def unhandled_exception_handler(request: Request, exc: Exception):
            """全局未捕获异常处理：发送到错误topic"""
            try:
                error_message = ErrorMessage(
                    topic_id='unknown',
                    source_data=SourceData(
                        dataSource="post-processor",
                        title="",
                        topic_text=[],
                        brand="",
                        series="",
                        ext="",
                        create_time=""
                    ),
                    retry_count=0,
                    error_type="post_processor_unhandled_error",
                    error_msg=str(exc),
                    last_ts=str(int(time.time()))
                )
                self.kafka_client.send_to_error_topic(error_message)
            except Exception as inner_e:
                self.logger.error(f"发送未捕获异常到Kafka时发生异常: {str(inner_e)}")

            return APIResponse(success=False, message=f"Unhandled exception: {exc}")

        @app.post("/process")
        async def process_text(request: dict):
            """手动处理文本（用于测试）"""
            try:
                # 构造测试消息
                test_message = {
                    'topic_id': 'test_' + str(int(time.time())),
                    'source_data': {
                        'dataSource': request.get('dataSource', 'test'),
                        'title': request.get('title', '测试标题'),
                        'topic_text': request.get('topic_text', []),
                        'brand': request.get('brand', '长安'),
                        'series': request.get('series', 'CS75 PLUS'),
                        'ext': '',
                        'create_time': request.get('create_time', '2024-01-01 00:00:00')
                    },
                    'cat_type': 'test',
                    'retry_count': 0,
                    'error_type': '',
                    'error_msg': '',
                    'last_ts': '',
                    'result': request.get('result', [])
                }
                
                await self.process_result_message(test_message)
                
                return APIResponse(
                    success=True, 
                    message="Processing completed", 
                    data={'topic_id': test_message['topic_id']}
                )
            except Exception as e:
                # 发送到错误topic
                try:
                    error_message = ErrorMessage(
                        topic_id='unknown',
                        source_data=SourceData(
                            dataSource=request.get('dataSource', 'test'),
                            title=request.get('title', ''),
                            topic_text=request.get('topic_text', []),
                            brand=request.get('brand', ''),
                            series=request.get('series', ''),
                            ext='',
                            create_time=request.get('create_time', '')
                        ),
                        retry_count=0,
                        error_type="post_processor_process_api_error",
                        error_msg=str(e),
                        last_ts=str(int(time.time()))
                    )
                    self.kafka_client.send_to_error_topic(error_message)
                except Exception as inner_e:
                    self.logger.error(f"发送错误消息到Kafka时发生异常: {str(inner_e)}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @app.get("/sentiment-levels")
        async def get_sentiment_levels():
            """获取配置的情感级别"""
            return APIResponse(
                success=True,
                message="Sentiment levels retrieved",
                data=self.sentiment_levels
            )
        
        @app.get("/usage-scenarios")
        async def get_usage_scenarios():
            """获取配置的用车场景"""
            return APIResponse(
                success=True,
                message="Usage scenarios retrieved",
                data=self.usage_scenarios
            )
        
        @app.post("/test-intent-review")
        async def test_intent_review(request: dict):
            """测试意图审核机制"""
            try:
                sentiment = request.get('sentiment', '中性')
                original_intent = request.get('intent', '咨询')
                text = request.get('text', '')
                
                # 调用意图审核方法
                corrected_intent = await self.review_and_correct_intent(
                    sentiment, original_intent, text
                )
                
                return APIResponse(
                    success=True,
                    message="Intent review completed",
                    data={
                        'original_sentiment': sentiment,
                        'original_intent': original_intent,
                        'corrected_intent': corrected_intent,
                        'was_corrected': corrected_intent != original_intent,
                        'text': text
                    }
                )
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))


# 创建服务实例
service = PostProcessorService()

# 创建FastAPI应用
app = service.create_fastapi_app(
    title="Post Processor Service",
    description="后处理服务 - 二级情感分析和用车场景分析"
)


@app.get("/health")
async def health_check():
    """健康检查端点"""
    try:
        health_data = await service.health_check()
        return APIResponse(success=True, message="Service is healthy", data=health_data)
    except Exception as e:
        return APIResponse(success=False, message=f"Health check failed: {e}")


if __name__ == "__main__":
    service.run_with_app_string("main:app")
