"""
API网关服务
统一入口，负责路由转发、负载均衡、认证授权等
"""
import asyncio
import signal
import sys
import os
from pathlib import Path
from typing import Dict, Any, Optional
import httpx
import time

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from fastapi import FastAPI, HTTPException, Request, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from contextlib import asynccontextmanager
import uvicorn

from shared.base import GatewayService as BaseGatewayService
from shared.models.schemas import (
    InitialMessage, APIResponse, HealthCheckResponse
)


class GatewayService(BaseGatewayService):
    """API网关服务"""
    
    def __init__(self):
        super().__init__()
        
        # 微服务地址映射（支持多实例）
        self.services = self._initialize_service_urls()
    
    def _initialize_service_urls(self):
        """初始化服务URL配置，支持多实例"""
        import os
        
        # 获取环境后缀
        env_suffix = os.environ.get('ENV_SUFFIX', 'env1')
        
        services = {}
        
        # 单实例服务
        services['text-processor'] = os.environ.get('TEXT_PROCESSOR_URL', f'http://text-processor-{env_suffix}:8200')
        services['rule-matcher'] = os.environ.get('RULE_MATCHER_URL', f'http://rule-matcher-{env_suffix}:8110')
        services['vector-matcher'] = os.environ.get('VECTOR_MATCHER_URL', f'http://vector-matcher-{env_suffix}:8120')
        services['brand-attribution'] = os.environ.get('BRAND_ATTRIBUTION_URL', f'http://brand-attribution-{env_suffix}:8170')
        services['error-handler'] = os.environ.get('ERROR_HANDLER_URL', f'http://error-handler-{env_suffix}:8130')
        
        # 多实例服务 - LLM
        llm_count = int(os.environ.get('LLM_INSTANCE_COUNT', '4'))
        if os.environ.get('LLM_URL'):
            services['llm'] = os.environ.get('LLM_URL')  # 单一URL兼容模式
        else:
            services['llm'] = [f'http://llm-{env_suffix}-{i+1}:8150' for i in range(llm_count)]
        
        # 多实例服务 - UIE
        uie_count = int(os.environ.get('UIE_INSTANCE_COUNT', '6'))
        if os.environ.get('UIE_URL'):
            services['uie'] = os.environ.get('UIE_URL')  # 单一URL兼容模式
        else:
            services['uie'] = [f'http://uie-{env_suffix}-{i+1}:8140' for i in range(uie_count)]
        
        # 多实例服务 - Intent
        intent_count = int(os.environ.get('INTENT_INSTANCE_COUNT', '6'))
        if os.environ.get('INTENT_URL'):
            services['intent'] = os.environ.get('INTENT_URL')  # 单一URL兼容模式
        else:
            services['intent'] = [f'http://intent-{env_suffix}-{i+1}:8160' for i in range(intent_count)]
        
        return services
    
    async def _initialize_service_specific(self):
        """网关特定的初始化逻辑"""
        # 网关服务的HTTP客户端需要特殊配置
        if self.http_client:
            await self.http_client.aclose()
        
        self.http_client = httpx.AsyncClient(
            timeout=30.0,
            limits=httpx.Limits(max_connections=100, max_keepalive_connections=20)
        )
        
        # 记录服务地址配置（现在logger已经初始化）
        self.logger.info("Gateway service addresses configured:")
        for service_name, urls in self.services.items():
            if isinstance(urls, list):
                self.logger.info(f"  {service_name}: {len(urls)} instances")
                for i, url in enumerate(urls):
                    self.logger.info(f"    [{i+1}] {url}")
            else:
                self.logger.info(f"  {service_name}: {urls}")
    
    async def start_consumers(self):
        """启动消费者（网关服务通常不需要消费者）"""
        # 网关服务通常不需要Kafka消费者，但需要实现这个抽象方法
        self.logger.info("Gateway service: no consumers to start")
    
    async def update_service_health_cache(self):
        """更新服务健康状态缓存"""
        current_time = time.time()
        if current_time - self.last_health_check < self.health_check_interval:
            return
        
        self.logger.debug("Updating service health cache...")
        
        for service_name, urls in self.services.items():
            if isinstance(urls, list):
                # 多实例服务，检查每个实例
                health_results = {}
                for i, url in enumerate(urls):
                    instance_name = f"{service_name}-{i+1}"
                    is_healthy = await self.check_service_health_by_url(url)
                    health_results[instance_name] = {
                        'healthy': is_healthy,
                        'url': url,
                        'last_check': current_time
                    }
                self.service_health_cache[service_name] = health_results
            else:
                # 单实例服务
                is_healthy = await self.check_service_health_by_url(urls)
                self.service_health_cache[service_name] = {
                    'healthy': is_healthy,
                    'url': urls,
                    'last_check': current_time
                }
        
        self.last_health_check = current_time
        
        # 记录健康状态摘要
        self._log_health_summary()
    
    def get_healthy_service_url(self, service_name: str) -> Optional[str]:
        """获取健康的服务URL（支持负载均衡）"""
        if service_name not in self.services:
            return None
        
        # 检查缓存
        health_info = self.service_health_cache.get(service_name)
        if not health_info:
            return None
        
        urls = self.services[service_name]
        
        if isinstance(urls, list):
            # 多实例服务，使用轮询负载均衡
            healthy_urls = []
            for i, url in enumerate(urls):
                instance_name = f"{service_name}-{i+1}"
                if instance_name in health_info and health_info[instance_name]['healthy']:
                    healthy_urls.append(url)
            
            if healthy_urls:
                # 简单的轮询负载均衡
                if not hasattr(self, '_round_robin_counters'):
                    self._round_robin_counters = {}
                
                counter = self._round_robin_counters.get(service_name, 0)
                selected_url = healthy_urls[counter % len(healthy_urls)]
                self._round_robin_counters[service_name] = counter + 1
                
                return selected_url
        else:
            # 单实例服务
            if health_info.get('healthy', False):
                return urls
        
        return None
    
    async def check_service_health_by_url(self, url: str) -> bool:
        """通过URL检查服务健康状态"""
        try:
            health_url = f"{url}/health"
            response = await self.http_client.get(health_url, timeout=5.0)
            return response.status_code == 200
        except Exception as e:
            self.logger.debug(f"Health check failed for {url}: {e}")
            return False
    
    def _log_health_summary(self):
        """记录健康状态摘要"""
        summary = []
        for service_name, health_info in self.service_health_cache.items():
            if isinstance(health_info, dict) and any(k.startswith(service_name + '-') for k in health_info.keys()):
                # 多实例服务
                healthy_count = sum(1 for k, v in health_info.items() if v.get('healthy', False))
                total_count = len(health_info)
                summary.append(f"{service_name}: {healthy_count}/{total_count}")
            else:
                # 单实例服务
                status = "healthy" if health_info.get('healthy', False) else "unhealthy"
                summary.append(f"{service_name}: {status}")
        
        if summary:
            self.logger.info(f"Service health summary: {', '.join(summary)}")
    
    async def forward_request(self, service_name: str, path: str, method: str, **kwargs) -> Dict[str, Any]:
        """转发请求到指定服务"""
        service_url = self.get_healthy_service_url(service_name)
        if not service_url:
            raise HTTPException(
                status_code=503, 
                detail=f"Service {service_name} is not available"
            )
        
        url = f"{service_url}{path}"
        
        try:
            if method.upper() == 'GET':
                response = await self.http_client.get(url, **kwargs)
            elif method.upper() == 'POST':
                response = await self.http_client.post(url, **kwargs)
            elif method.upper() == 'PUT':
                response = await self.http_client.put(url, **kwargs)
            elif method.upper() == 'DELETE':
                response = await self.http_client.delete(url, **kwargs)
            else:
                raise HTTPException(status_code=405, detail="Method not allowed")
            
            return {
                'status_code': response.status_code,
                'data': response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text,
                'headers': dict(response.headers)
            }
            
        except httpx.TimeoutException:
            raise HTTPException(status_code=504, detail="Service timeout")
        except httpx.RequestError as e:
            raise HTTPException(status_code=502, detail=f"Service request failed: {str(e)}")
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Internal error: {str(e)}")
    
    async def submit_processing_task(self, message: InitialMessage) -> Dict[str, Any]:
        """提交处理任务到系统"""
        try:
            # 验证输入数据
            topic_text_list = message.source_data.topic_text

            if not topic_text_list or not any(item.get('content', '').strip() for item in topic_text_list):
                raise HTTPException(status_code=400, detail="topic_text cannot be empty")
            
            # 发送到Kafka开始处理流程
            self.kafka_client.send_message(
                self.kafka_client.TOPICS['VOC_TOMODEL_TOPIC'],
                message.model_dump()
            )
            
            self.structured_logger.log_kafka_message(
                'sent', self.kafka_client.TOPICS['VOC_TOMODEL_TOPIC'], message.topic_id
            )
            
            return {
                'success': True,
                'message': 'Task submitted successfully',
                'topic_id': message.topic_id,
                'status': 'processing',
                'source_metadata': {
                    'dataSource': message.source_data.dataSource,
                    'brand': message.source_data.brand,
                    'series': message.source_data.series
                }
            }
            
        except Exception as e:
            self.logger.error(f"Failed to submit task: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to submit task: {str(e)}")
    
    async def get_processing_status(self, topic_id: str) -> Dict[str, Any]:
        """获取处理状态（这里需要实现状态查询逻辑）"""
        # TODO: 实现从数据库或缓存中查询处理状态
        return {
            'topic_id': topic_id,
            'status': 'processing',  # pending, processing, completed, failed
            'message': 'Task is being processed'
        }
    
    async def get_system_health(self) -> Dict[str, Any]:
        """获取系统整体健康状态"""
        await self.update_service_health_cache()
        
        total_services = len(self.services)
        healthy_services = sum(1 for info in self.service_health_cache.values() if info['healthy'])
        
        system_status = "healthy" if healthy_services == total_services else "degraded"
        if healthy_services < total_services * 0.5:
            system_status = "unhealthy"
        
        return {
            'system_status': system_status,
            'total_services': total_services,
            'healthy_services': healthy_services,
            'services': {
                name: info['healthy'] for name, info in self.service_health_cache.items()
            },
            'kafka_status': 'connected',  # TODO: 实际检查Kafka状态
            'timestamp': time.time()
        }
    
    async def _service_specific_health_check(self) -> dict:
        """网关特定的健康检查"""
        await self.update_service_health_cache()
        
        total_services = len(self.services)
        healthy_services = sum(1 for info in self.service_health_cache.values() if info['healthy'])
        
        system_status = "healthy" if healthy_services == total_services else "degraded"
        if healthy_services < total_services * 0.5:
            system_status = "unhealthy"
        
        return {
            'system_status': system_status,
            'total_services': total_services,
            'healthy_services': healthy_services,
            'services': {
                name: info['healthy'] for name, info in self.service_health_cache.items()
            }
        }
    
    def _add_custom_routes(self, app: FastAPI):
        """添加网关特定的路由"""
        # 添加中间件
        app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],  # 生产环境中应该限制具体域名
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=["*"]  # 生产环境中应该限制具体主机
        )

        # 请求日志中间件
        @app.middleware("http")
        async def log_requests(request: Request, call_next):
            start_time = time.time()
            response = await call_next(request)
            process_time = time.time() - start_time
            
            self.structured_logger.log_request(
                method=request.method,
                path=str(request.url.path),
                status_code=response.status_code,
                duration=process_time,
                client_ip=request.client.host if request.client else "unknown"
            )
            
            return response

        # API端点
        @app.get("/")
        async def root():
            """根路径"""
            return {
                "message": "VOC2.0 智能打标系统 API 网关",
                "version": "1.0.0",
                "timestamp": time.time()
            }

        @app.get("/system/health")
        async def system_health():
            """系统整体健康检查"""
            try:
                health_data = await self.get_system_health()
                return APIResponse(success=True, message="System health retrieved", data=health_data)
            except Exception as e:
                return APIResponse(success=False, message=f"System health check failed: {e}")

        @app.post("/api/v1/process")
        async def process_text(message: InitialMessage):
            """提交文本处理任务"""
            try:
                result = await self.submit_processing_task(message)
                return APIResponse(success=True, message="Task submitted", data=result)
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @app.get("/api/v1/status/{topic_id}")
        async def get_status(topic_id: str):
            """获取处理状态"""
            try:
                status_data = await self.get_processing_status(topic_id)
                return APIResponse(success=True, message="Status retrieved", data=status_data)
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        # 服务代理端点  
        self._add_proxy_routes(app)
    
    def _add_proxy_routes(self, app: FastAPI):
        """添加服务代理路由"""
        @app.api_route("/api/v1/text-processor/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
        async def proxy_text_processor(path: str, request: Request):
            """代理到文本处理服务"""
            try:
                kwargs = {}
                if request.method in ["POST", "PUT"]:
                    kwargs["json"] = await request.json()
                elif request.method == "GET":
                    kwargs["params"] = dict(request.query_params)
                
                result = await self.forward_request("text-processor", f"/{path}", request.method, **kwargs)
                return result["data"]
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @app.api_route("/api/v1/rule-matcher/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
        async def proxy_rule_matcher(path: str, request: Request):
            """代理到规则匹配服务"""
            try:
                kwargs = {}
                if request.method in ["POST", "PUT"]:
                    kwargs["json"] = await request.json()
                elif request.method == "GET":
                    kwargs["params"] = dict(request.query_params)
                
                result = await self.forward_request("rule-matcher", f"/{path}", request.method, **kwargs)
                return result["data"]
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @app.api_route("/api/v1/vector-matcher/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
        async def proxy_vector_matcher(path: str, request: Request):
            """代理到向量匹配服务"""
            try:
                kwargs = {}
                if request.method in ["POST", "PUT"]:
                    kwargs["json"] = await request.json()
                elif request.method == "GET":
                    kwargs["params"] = dict(request.query_params)
                
                result = await self.forward_request("vector-matcher", f"/{path}", request.method, **kwargs)
                return result["data"]
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @app.api_route("/api/v1/error-handler/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
        async def proxy_error_handler(path: str, request: Request):
            """代理到错误处理服务"""
            try:
                kwargs = {}
                if request.method in ["POST", "PUT"]:
                    kwargs["json"] = await request.json()
                elif request.method == "GET":
                    kwargs["params"] = dict(request.query_params)
                
                result = await self.forward_request("error-handler", f"/{path}", request.method, **kwargs)
                return result["data"]
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))


# 创建服务实例
service = GatewayService()


async def periodic_health_check():
    """周期性健康检查任务"""
    while True:
        try:
            await service.update_service_health_cache()
            await asyncio.sleep(service.health_check_interval)
        except asyncio.CancelledError:
            break
        except Exception as e:
            service.logger.error(f"Health check task error: {e}")
            await asyncio.sleep(service.health_check_interval)


# 创建FastAPI应用
app = service.create_fastapi_app(
    title="VOC2.0 API Gateway",
    description="VOC2.0智能打标系统API网关"
)


if __name__ == "__main__":
    service.run_with_app_string("main:app")