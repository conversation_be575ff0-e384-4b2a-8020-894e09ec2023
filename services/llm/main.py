"""
大模型服务
负责调用大模型进行观点抽取和结构化输出
"""
import asyncio
import signal
import sys
from pathlib import Path
from typing import List, Dict, Any, Optional
import json
import httpx
from fastapi.responses import JSONResponse
from qwen_agent.llm.schema import ContentItem
from qwen_agent.tools.base import BaseTool, register_tool
from qwen_agent.llm import get_chat_model
# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))
from fastapi import FastAPI, HTTPException
from contextlib import asynccontextmanager
import uvicorn
from shared.database.mysql_client import MySQLClient
from shared.base import BaseService
from shared.models.schemas import (
    SourceData, ErrorMessage, APIResponse,ModelResultMessage,CategoryMessage
)
from shared.utils.config import config_manager
from shared.utils.logger import StructuredLogger
import time
import re  # 添加这个import
#------------------------工具的封装------------------------------------


class CarReviewAnalysisTool(BaseTool):
    name = "car_review_analysis"  # 添加这一行
    description = '汽车评论分析工具，从汽车评论中抽取评价对象、情感倾向、意图、观点和原文信息。'
    parameters = [
        {
        'name': 'text',
        'type': 'string',
        'description': '需要分析的汽车评论文本内容',
        'required': True
    },
        {
            'name': 'car_dict',
            'type': 'object',
            'description': '已检测到的品牌到车系映射（可能不完整），车系可为空或缺失',
            'required': False
    },
        {
            'name': 'Opinion',
            'type': 'array',
            'description': '观点清洗结果，仅使用original_entity与original_description作为提示线索',
            'required': False
    }
    ]

    def __init__(self, llm_cfg=None, kafka_client=None, logger=None, prompts_config=None, mysql_client=None, max_retries=None):
        super().__init__()
        self.llm = get_chat_model(llm_cfg)
        # 设置最大重试次数 - 从配置文件读取，默认值为3
        self.max_retries = max_retries if max_retries is not None else 3
        #self.structured_logger = StructuredLogger('llm')
        # ✅ 保留Kafka客户端和logger引用
        self.logger = logger
        self.prompts_config = prompts_config or {}
        self.mysql_client = mysql_client

    def _parse_position_info(self, text: str, original_text: str = None) -> tuple:
        """
        在原文中查找指定文本的位置
        Args:
            text: 要查找的文本
            original_text: 原文
        Returns:
            (start, end): 文本在原文中的位置
        """
        try:
            if not text or not original_text:
                return 0, 0
            
            # 直接查找文本在原文中的位置
            start = original_text.find(text)
            if start != -1:
                end = start + len(text)
                return start, end
            
            # 如果找不到，返回0,0
            self.logger.warning(f"无法在原文中找到文本: {text}")
            return 0, 0
            
        except Exception as e:
            self.logger.error(f"解析位置信息失败: {str(e)}")
            return 0, 0

    #提取大模型是文本输出结果转换成Json格式
    async def parse_llm_output(self, content: str,original_text: str = None) -> dict:
        try:
            # 过滤掉</think>前面的所有文本
            if content and "</think>" in content:
                content = content.split("</think>", 1)[1].strip()
            
            result = {"result": []}
            sections = content.split("---")
            for section in sections:
                section = section.strip()
                if not section:
                    continue
                lines = section.split("\n")
                obj = {}
                for line in lines:
                    if ": " in line:
                        key, value = line.split(": ", 1)
                        obj[key.strip()] = value.strip()

                #检查是否包含必要的字段
                if "segment" in obj and "aspect" in obj and "brand" in obj and "series" in obj and "sentiment" in obj and "intent" in obj and "opinion" in obj:
                    #解析aspect字段，获得文本和位置信息
                    aspect_text = obj.get("aspect", "no_aspect")
                    aspect_start, aspect_end = self._parse_position_info(aspect_text,original_text)

                    #解析opinion字段，获得文本和位置信息
                    opinion_text = obj.get("opinion", "no_opinion")
                    opinion_start, opinion_end = self._parse_position_info(opinion_text,original_text)

                    # 查询brand和series的code
                    brand_name = obj.get("brand", "no_brand")
                    series_name = obj.get("series", "no_series")
                    
                    # 查询对应的code和car_level信息
                    code_info = {"brand_code": "", "series_code": "", "car_level1": "", "car_level2": ""}
                    if self.mysql_client and (brand_name != "no_brand" or series_name != "no_series"):
                        try:
                            code_info = await self.mysql_client.query_brand_series_codes(
                                brand_name=brand_name if brand_name != "no_brand" else None,
                                series_name=series_name if series_name != "no_series" else None
                            )
                        except Exception as e:
                            self.logger.error(f"查询brand/series code失败: {e}")
                    
                    # 当数据库查询失败时，使用原始名称作为fallback
                    final_brand = code_info.get("brand_code", "")
                    final_series = code_info.get("series_code", "")
                    
                    #构建结果对象
                    result_obj = {
                        "original_text": obj.get("original_text", "no_original_text"),
                        "segment": obj.get("segment", "no_segment"),
                        "aspect":{
                            "text": aspect_text.split(",")[0].strip() if aspect_text else "",
                            "start": aspect_start,
                            "end": aspect_end
                        },
                        "brand": final_brand,  # 使用fallback逻辑
                        "series": final_series,  # 使用fallback逻辑
                        "car_level1": code_info.get("car_level1", ""),
                        "car_level2": code_info.get("car_level2", ""),
                        "sentiment": obj.get("sentiment", "no_sentiment"),
                        "intent": obj.get("intent", "no_intent"),
                        "opinion":{
                            "text": opinion_text.split(",")[0].strip() if opinion_text else "",
                            "start": opinion_start,
                            "end": opinion_end
                        }
                    }
                    result["result"].append(result_obj)
            return result

        except Exception as e:
            error_msg = f"解析LLM输出失败: {str(e)}"
            self.logger.error(error_msg)            
            return {"result": []}
    
    def has_nan_values(self, result: dict) -> bool:
        """检查结果中是否包含特定的nan占位符"""
        try:
            #检查输出格式
            if 'result' not in result or not isinstance(result['result'], list):
                return True    
            #定义需要检查的占位符       
            field_placeholders = {
                'original_text': 'no_original_text',
                'segment': 'no_segment',
                'aspect': 'no_aspect',
                'brand': 'no_brand',
                'series': 'no_series',
                'sentiment': 'no_sentiment',
                'intent': 'no_intent',
                'opinion': 'no_opinion'
            }           

            for obj in result['result']:
                if not isinstance(obj, dict):
                    continue
                #检查original_text字段
                if obj.get('original_text') in ['no_original_text','']:
                    return True
                #检查segment字段
                if obj.get('segment') in ['no_segment','']:
                    return True
                aspect = obj.get('aspect', {})
                if not isinstance(aspect, dict) or not aspect.get('text'): 
                    return True               
                if obj.get('brand') in ['no_brand', '']:
                    return True
                if obj.get('series') in ['no_series', '']:
                    return True
                if obj.get('sentiment') in ['no_sentiment', '']:
                    return True
                if obj.get('intent') in ['no_intent', '']:
                    return True               
                opinion = obj.get('opinion', {})
                if not isinstance(opinion, dict) or not opinion.get('text'):
                    return True               
            return False
        except Exception as e:
            error_msg = f"检查nan值失败: {str(e)}"
            self.logger.error(error_msg)
            
            return True  # 出错时默认返回True，触发重试

    def validate_result(self, result: dict) -> bool:
        try:
            if 'result' not in result or not isinstance(result['result'], list):
                self.logger.warning("验证失败: 'result'缺失或不是列表")
                return False

            for i, obj in enumerate(result['result']):
                if not isinstance(obj, dict):
                    self.logger.warning(f"验证失败: result列表第{i}项不是字典，而是{type(obj)}")
                    return False

                #检查必需字段
                required_fields = ['original_text', 'segment', 'aspect', 'brand', 'series', 'sentiment', 'intent', 'opinion']
                for field in required_fields:
                    if field not in obj:
                        self.logger.warning(f"验证失败: result列表第{i}项缺少必需字段: {field}")
                        return False

                # 验证original_text字段
                if not isinstance(obj['original_text'], str) or not obj['original_text'].strip():
                    self.logger.warning(f"验证失败: 第{i}项original_text字段不是有效字符串")
                    return False

                # 验证segment字段
                if not isinstance(obj['segment'], str) or not obj['segment'].strip():
                    self.logger.warning(f"验证失败: 第{i}项segment字段不是有效字符串")
                    return False

                # 验证aspect字段
                aspect = obj['aspect']
                if not isinstance(aspect, dict):
                    self.logger.warning(f"验证失败: 第{i}项aspect不是字典")
                    return False

                # 移除relations字段检查，因为新输出结构中没有这个字段
                # if not isinstance(obj['relations'], dict):
                #     self.logger.warning(f"验证失败: 评价对象列表第{i}项的relations不是字典")
                #     return False

                if 'text' not in aspect or not isinstance(aspect['text'], str) or not aspect['text'].strip():
                    self.logger.warning(f"验证失败: 第{i}项aspect.text不是有效字符串")
                    return False

                if 'start' not in aspect or 'end' not in aspect:
                    self.logger.warning(f"验证失败: 第{i}项aspect缺少start或end字段")
                    return False
                
                if not isinstance(obj['brand'], str) or not obj['brand'].strip():
                    self.logger.warning(f"验证失败: 第{i}项brand字段不是有效字符串")
                    return False
                
                if not isinstance(obj['series'], str) or not obj['series'].strip():
                    self.logger.warning(f"验证失败: 第{i}项series字段不是有效字符串")
                    return False

                valid_sentiments = ['正面', '负面', '中性']
                if obj['sentiment'] not in valid_sentiments:
                    self.logger.warning(f"验证失败: 第{i}项sentiment字段值无效: {obj['sentiment']}")
                    return False

                valid_intents = ['抱怨', '表扬', '咨询', '建议']
                if obj['intent'] not in valid_intents:
                    self.logger.warning(f"验证失败: 第{i}项intent字段值无效: {obj['intent']}")
                    return False
                
                opinion = obj['opinion']
                if not isinstance(opinion, dict):
                    self.logger.warning(f"验证失败: 第{i}项opinion不是字典")
                    return False

                if 'text' not in opinion or not isinstance(opinion['text'], str) or not opinion['text'].strip():
                    self.logger.warning(f"验证失败: 第{i}项opinion.text不是有效字符串")
                    return False

                if 'start' not in opinion or 'end' not in opinion:
                    self.logger.warning(f"验证失败: 第{i}项opinion缺少start或end字段")
                    return False

            return True
        except Exception as e:
            error_msg = f"验证结果失败: {str(e)}"
            self.logger.error(error_msg)
            return False  # 出错时默认返回False，触发重试

    async def call(self, params: str, **kwargs) -> str:
        try:
            params = json.loads(params)
            text = params['text']
            car_dict = params.get('car_dict', {})
            opinion_list = params.get('Opinion', [])
        except Exception as e:
            error_msg = f"参数解析错误: {str(e)}"
            self.logger.error(error_msg)
            return json.dumps({"result": []}, ensure_ascii=False)

        #处理品牌车系信息
        car_info_text = ""
        if car_dict:
            car_info_text = "已检测到的品牌车系： \n"
            for brand, info in car_dict.items():
                series = info['series']
                synonyms = info['synonyms']
                synonyms_text = ", ".join(synonyms) if synonyms else "无"
                car_info_text += f"-{brand}: {series} (同义词: {synonyms_text})\n"
            car_info_text += "注意：如果原文中还有其他未检测到的品牌车系，请补充识别。\n\n"

        #处理观点清洗信息
        opinion_info_text = ""
        if opinion_list:
            opinion_info_text = "观点清洗提示线索（仅供参考）: \n"
            for opinion in opinion_list:
                if isinstance(opinion, dict):
                    original_entity = opinion.get('original_entity','')
                    original_description = opinion.get('original_description','')
                    if original_entity and original_description:
                        opinion_info_text += f"-{original_entity}: {original_description}\n"
            opinion_info_text += "注意：如果原文中还有其他未检测到的观点，请补充识别。\n\n"

        base_prompt = self.prompts_config.get('llm', {}).get('car_review_analysis', {}).get('system_prompt', '')

        system_prompt = base_prompt.format(
            car_info_text = car_info_text,
            opinion_info_text = opinion_info_text
        )
        self.logger.info(f"system_prompt: {system_prompt}")

        messages = [
            {'role': 'system', 'content': system_prompt},
            {'role': 'user', 'content': f'/no_think 请分析以下汽车评论：{text}'}
        ]
        
        # 修改：使用新的输出格式初始化result
        result = {"result": []}
        
        for attempt in range(self.max_retries):
            self.logger.info(f"尝试第{attempt + 1}次尝试调用LLM处理文本：{text}")
            try:
                response = self.llm.chat(
                    messages=messages,
                    functions=None,
                    stream=False
                )

                self.logger.info(f"LLM原始响应: {json.dumps(response, ensure_ascii=False)}")

                if isinstance(response, list) and len(response) > 0:
                    last_msg = response[-1]
                    if 'content' in last_msg:
                        content = last_msg['content']
                        
                        result = await self.parse_llm_output(content, text)  # 传递text参数作为original_text
                        if self.validate_result(result):
                            if not self.has_nan_values(result):
                                self.logger.info("格式校验成功，且无nan值")
                                return json.dumps(result, ensure_ascii=False)
                            else:
                                self.logger.warning(f"检测到nan值，进行{attempt + 1}次重试")

                                #在消息中添加提醒
                                retry_reminder = self.prompts_config.get('llm', {}).get('car_review_analysis', {}).get('retry_reminder', '')
                                messages.append({'role': 'user', 'content': retry_reminder})                    
                        else:
                            self.logger.error("格式校验失败")
                    else:
                        self.logger.error("LLM响应中没有content字段")                     
                else:
                    self.logger.error("LLM响应格式不正确")                             
            except Exception as e:
                error_msg = f"调用LLM时发生异常: {str(e)}"
                self.logger.error(error_msg)
        
        # 修改：返回新的输出格式
        return json.dumps(result, ensure_ascii=False)


#----------------------------初始化大模型服务-------------------------

class LLMService(BaseService):
    """大模型服务"""
    
    def __init__(self):
        super().__init__('llm')
        self.vector_config = self.config_manager.get_vector_config()
        # 通用服务地址
        self.rule_matcher_url = "http://rule-matcher:8110"
        self.vector_matcher_url = "http://vector-matcher:8120"
        self.text_processor_url = "http://text-processor:8100"
        self.mysql_client = None
        # LLM配置
        self.model_config = {
            'api_url': 'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions'
        }
    
    def _init_service_specific_config(self):
        """初始化服务特定配置"""
        # LLM服务不需要额外的配置初始化
        pass
    async def _initialize_service_specific(self):
        """初始化服务"""
        #获取提示词
        prompts_config = self.config_manager.load_prompts()
        
        # 从配置文件获取LLM配置
        llm_cfg = {
            'model': self.config_manager._get_env_or_config('LLM_MODEL', 'models.llm.model', 'qwen3-14b'),
            'api_key': self.config_manager._get_env_or_config('LLM_API_KEY', 'models.llm.api_key', ''),
            'model_server': self.config_manager._get_env_or_config('LLM_MODEL_SERVER', 'models.llm.model_server', 'http://localhost:8001/v1'),
            'generate_cfg': {
                'extra_body': {
                    "chat_template_kwargs": {
                        'enable_thinking': False
                    }
                },
            },
            # 'generate_cfg': {
            #     'extra_body': {
            #         'enable_thinking': False
            #     },
            # },
        }
        
        # 更新model_config使用配置文件中的api_url
        self.model_config = {
            'api_url': self.config_manager._get_env_or_config('LLM_API_URL', 'models.llm.api_url', 'http://localhost:8001/v1/chat/completions')
        }
        
        # 输出配置信息用于调试
        self.logger.info(f"LLM配置加载完成:")
        self.logger.info(f"  - Model: {llm_cfg['model']}")
        self.logger.info(f"  - API Key: {'***' + llm_cfg['api_key'][-4:] if llm_cfg['api_key'] else 'Empty'}")
        self.logger.info(f"  - Model Server: {llm_cfg['model_server']}")
        self.logger.info(f"  - API URL: {self.model_config['api_url']}")
        
        # 初始化MySQL客户端（用于品牌车系数据查询）
        db_config = self.config_manager.get_database_config()
        # 覆盖为 MySQL 专用配置（优先环境变量，再读 configs/config.yaml 的 database.mysql.*）
        db_host = self.config_manager._get_env_or_config('MYSQL_HOST', 'database.mysql.host', self.config_manager._get_env_or_config('DB_HOST', 'database.mysql.host', db_config.host))
        db_port = self.config_manager._get_env_or_config('MYSQL_PORT', 'database.mysql.port', self.config_manager._get_env_or_config('DB_PORT', 'database.mysql.port', db_config.port))
        db_user = self.config_manager._get_env_or_config('MYSQL_USERNAME', 'database.mysql.username', self.config_manager._get_env_or_config('DB_USERNAME', 'database.mysql.username', db_config.username))
        db_pass = self.config_manager._get_env_or_config('MYSQL_PASSWORD', 'database.mysql.password', self.config_manager._get_env_or_config('DB_PASSWORD', 'database.mysql.password', db_config.password))
        # LLM服务：数据库名使用 database1 (voc_ms_be)
        db_name = self.config_manager._get_env_or_config('MYSQL_DATABASE1', 'database.mysql.database1', self.config_manager._get_env_or_config('DB_DATABASE1', 'database.mysql.database1', 'voc_ms_be'))
        
        from shared.models.schemas import DatabaseConfig
        mysql_db_config = DatabaseConfig(
            host=db_host,
            port=int(db_port),
            username=db_user,
            password=db_pass,
            database=db_name
        )
        
        # 调试：输出数据库配置信息
        self.logger.info(f"MySQL数据库配置:")
        self.logger.info(f"  - Host: {mysql_db_config.host}")
        self.logger.info(f"  - Port: {mysql_db_config.port}")
        self.logger.info(f"  - Username: {mysql_db_config.username}")
        self.logger.info(f"  - Password: {'***' + mysql_db_config.password[-4:] if mysql_db_config.password else 'Empty'}")
        self.logger.info(f"  - Database: {mysql_db_config.database}")
        
        self.mysql_client = MySQLClient(mysql_db_config)
        await self.mysql_client.initialize()
        
        # 初始化结构化日志记录器
        self.structured_logger = StructuredLogger('llm')
        
        # 从配置文件读取max_retries
        max_retries = self.config_manager._get_env_or_config('LLM_MAX_RETRIES', 'models.llm.max_retries', 3)
        self.logger.info(f"  - Max Retries: {max_retries}")
        
        self.analysis_tool = CarReviewAnalysisTool(
            llm_cfg=llm_cfg,
            logger=self.logger,
            prompts_config=prompts_config,
            mysql_client=self.mysql_client,
            max_retries=max_retries
            )
        self.logger.info("LLM service initialized successfully")
    
    async def _shutdown_service_specific(self):
        """服务特定的关闭逻辑"""
        if self.mysql_client:
            await self.mysql_client.close()

    async def initialize(self):
        """初始化服务"""
        # 调用父类的通用初始化
        await super().initialize()


    async def call_rule_matcher_text(self, text: str) -> List[Dict]:
        """调用规则匹配服务"""
        start = time.perf_counter()
        text_len = len(text) if isinstance(text, str) else 0
        try:
            self.logger.info(f"RuleMatcher|request start text_len={text_len} preview={text[:100] if text_len else ''}")
            response = await self.http_client.post(
                f"{self.rule_matcher_url}/match/text",
                json={"text": text},
                timeout=10
            )
            elapsed = int((time.perf_counter() - start) * 1000)
            if response.status_code == 200:
                result = response.json()
                matches = result.get('data', {}).get('matches', [])
                self.logger.info(f"RuleMatcher|response ok status=200 elapsed_ms={elapsed} matches={len(matches)}")
                if matches:
                    self.logger.debug(f"RuleMatcher|sample first={matches[0]}")  # 仅DEBUG级别展示样例，避免刷屏
                return matches
            else:
                body = response.text[:500]
                self.logger.warning(f"RuleMatcher|response bad status={response.status_code} elapsed_ms={elapsed} body={body}")
                return []
        except Exception as e:
            elapsed = int((time.perf_counter() - start) * 1000)
            self.logger.error(f"RuleMatcher|exception elapsed_ms={elapsed} error={e}")
            return []
    
    async def call_vector_matcher(self, entity_description_pairs: List[Dict]) -> List[Dict]:
        """调用向量匹配服务"""
        try:
            # 将实体-描述对转换为向量匹配服务期望的格式
            pairs = []
            for pair in entity_description_pairs:
                entity_text = pair['entity']['text']
                opinion_text = pair['opinion']['text']
                original_text = pair.get('original_text', '')
                pairs.append([original_text, entity_text, opinion_text]) 
            
            response = await self.http_client.post(
                f"{self.vector_matcher_url}/match/pairs",
                json={"pairs": pairs},
                timeout=10
            )
            if response.status_code == 200:
                result = response.json()
                return result.get('data', {}).get('matches', [])
            else:
                self.logger.warning(f"Vector matcher returned {response.status_code}")
                return []
        except Exception as e:
            self.logger.error(f"Error calling vector matcher: {e}")
            return []

    def _extract_entity_description_pairs(self, llm_result: dict, original_texts: List[str] = None) -> List[Dict]:
        """从LLM结果中提取主体-描述对，适配新的输出结构"""
        pairs = []
        
        try:
            # 适配新的输出结构：从result数组中提取数据
            result_list = llm_result.get('result', [])
            
            for result in result_list:
                if not isinstance(result, dict):
                    continue
                    
                # 提取aspect和opinion信息
                aspect = result.get('aspect', {})
                opinion = result.get('opinion', {})
                
                aspect_text = aspect.get('text', '').strip()
                opinion_text = opinion.get('text', '').strip()
                
                if not aspect_text or not opinion_text:
                    continue
                
                # 获取原始文本
                original_text = result.get('original_text', '')
                
                # 添加有效的实体-观点对
                pairs.append({
                    'original_text': original_text,
                    'entity': {
                        'text': aspect_text,
                        'start': aspect.get('start', 0),
                        'end': aspect.get('end', 0)
                    },
                    'opinion': {
                        'text': opinion_text,
                        'start': opinion.get('start', 0),
                        'end': opinion.get('end', 0)
                    }
                })
                
        except Exception as e:
            self.logger.error(f"提取实体-观点对时发生异常: {str(e)}")
            
        return pairs


    def Combine_the_results_of_multi_turn_dialogues_from_llm(self, result: dict) -> dict:
        """
        组合多轮对话的LLM结果，按照新格式去重
        
        Args:
            result: LLM返回的结果，格式为 {"result": [...]}
            
        Returns:
            dict: 去重后的结果，格式为 {"result": [...]}
        """
        try:
            # 确保输入格式正确
            if not isinstance(result, dict) or 'result' not in result:
                self.logger.warning("输入结果格式不正确，返回空结果")
                return {"result": []}
            
            result_list = result.get('result', [])
            
            # 用于去重的集合，基于关键字段组合
            seen_combinations = set()
            unique_results = []
            
            for item in result_list:
                try:                                    
                    # 提取用于去重的关键字段
                    original_text = item.get('original_text', '').strip()
                    segment = item.get('segment', '').strip()
                    aspect_text = item.get('aspect', {}).get('text', '').strip() if isinstance(item.get('aspect'), dict) else ''
                    brand = item.get('brand', '').strip()
                    series = item.get('series', '').strip()
                    sentiment = item.get('sentiment', '').strip()
                    intent = item.get('intent', '').strip()
                    opinion_text = item.get('opinion', {}).get('text', '').strip() if isinstance(item.get('opinion'), dict) else ''
                                       
                    # 创建去重键
                    dedup_key = (
                        original_text,
                        segment,
                        aspect_text,
                        brand,
                        series,
                        sentiment,
                        intent,
                        opinion_text
                    )
                    
                    # 如果这个组合还没见过，则添加到结果中
                    if dedup_key not in seen_combinations:
                        seen_combinations.add(dedup_key)
                        unique_results.append(item)
                        self.logger.info(f"添加新结果项: {segment} - {aspect_text} - {sentiment}")
                    else:
                        self.logger.info(f"跳过重复项: {segment} - {aspect_text} - {sentiment}")
                        
                except Exception as e:
                    self.logger.warning(f"处理结果项时出错: {str(e)}, 跳过该项: {item}")
                    continue
            
            self.logger.info(f"去重完成: 原始{len(result_list)}项 -> 去重后{len(unique_results)}项")
            
            return {"result": unique_results}
            
        except Exception as e:
            self.logger.error(f"组合多轮对话结果时发生错误: {str(e)}")
            return {"result": []}

    async def process_llm_message(self, message_data: dict):
        """处理LLM消息"""
        try:
            message = CategoryMessage(**message_data)
            #拿车系品牌
            brand_series_list = message.brand_series_list
            car_dict = {}
            for item in brand_series_list:
                brand = item.get('brand', '')
                series = item.get('series', '')
                model = item.get('model', '')
                synonyms = item.get('synonyms', [])
                if not brand :
                    continue
                car_dict[brand]  = {
                'series': series,
                'model': model,
                'synonyms': synonyms
            } 
            #进行多轮对话处理   
            text_list = message.raw_split_text
            for text in text_list:
            #调用规则匹配
                rule_matches = await self.call_rule_matcher_text(text)
           
                all_opinions = rule_matches

                self.structured_logger.log_kafka_message(
                'received', 'llm_topic', message.topic_id
                )
                llm_input = {
                "text": text,
                "car_dict": car_dict,
                "Opinion": all_opinions
                }
            
                llm_params = json.dumps(llm_input)
                #调用大模型分析工具
                llm_result_json = await self.analysis_tool.call(llm_params)
                llm_result = json.loads(llm_result_json)
                #调用组合多轮对话函数
                llm_final_result = self.Combine_the_results_of_multi_turn_dialogues_from_llm(llm_result)
            #调用向量匹配
            final_result = await self.process_vector_search(llm_final_result)

            result_message = ModelResultMessage(
                topic_id=message.topic_id,
                source_data=message.source_data,
                retry_count=message.retry_count,
                error_type=message.error_type,
                error_msg=message.error_msg,
                last_ts=message.last_ts,
                cat_type=message.cat_type,
                result=final_result.get('result', []),
                qaresult=message.qaresult
            )
            
            self.kafka_client.send_to_result_topic(result_message.model_dump())
            self.logger.info(f"最终结果已发送至结果topic")           
            return result_message.model_dump()
                    
        except Exception as e:
            # 发送错误消息
            source_data = message_data.get('source_data', {})
            if isinstance(source_data, dict):
                source_data = SourceData(**source_data)
                
            error_message = ErrorMessage(
                topic_id=message_data.get('topic_id', 'unknown'),
                source_data=source_data,
                retry_count=message_data.get('retry_count', 0),
                error_type='llm_processing_error',
                error_msg=str(e),
                last_ts=message_data.get('last_ts', '')
            )
            self.kafka_client.send_to_error_topic(error_message)
            self.logger.exception("LLM processing error")
            return {}

    async def process_vector_search(self, llm_result: dict) -> Dict[str, Any]:
        """
        从LLM结果中提取评价对象和观点，进行向量匹配
    
        Args:
            llm_result: LLM处理的结果，包含评价对象信息
        
        Returns:
            Dict[str, Any]: 向量匹配结果列表
        """
        try:
            # 使用与UIE服务一致的提取方法
            entity_description_pairs = self._extract_entity_description_pairs(llm_result)
            self.logger.info(f"entity_description_pairs:{entity_description_pairs}")
            self.logger.info(f"准备进行向量匹配，共 {len(entity_description_pairs)} 个实体-观点对")
            
            # 调用向量匹配服务，传入符合格式的 pairs
            vector_matches = await self.call_vector_matcher(entity_description_pairs)
            self.logger.info(f'具体匹配结果：{vector_matches}')
            if vector_matches:
                self.logger.info(f"向量匹配完成，返回 {len(vector_matches)} 个匹配结果")
            else:
                self.logger.info("向量匹配服务不可用或无匹配结果，将返回原始LLM结果")
            
            # 处理向量匹配结果，适配新的输出结构
            matched_count = 0
            removed_count = 0
            # 修改：适配新的输出结构，从result数组中提取数据
            evaluation_objects = llm_result.get('result', [])
            matched_objects = []  # 存储匹配成功的对象
            
            for obj in evaluation_objects:
                # 修改：适配新的输出结构，直接使用aspect和opinion字段
                aspect_text = obj.get('aspect', {}).get('text', '')
                opinion_text = obj.get('opinion', {}).get('text', '')
                
                # 找到对应的向量匹配结果
                matched = False
                for match in vector_matches:
                    match_entity = match.get('original_entity', '')
                    match_description = match.get('original_description', '')
                    
                    # 检查是否匹配：aspect_text对应original_entity，opinion_text对应original_description
                    if aspect_text == match_entity and opinion_text == match_description:
                        self.logger.info(f'找到匹配: aspect="{aspect_text}" <-> entity="{match_entity}", opinion="{opinion_text}" <-> description="{match_description}"')
                        self.logger.info(f'匹配结果:{match}')
                        
                        # 更新标准化信息，使用向量匹配服务返回的标准化字段
                        obj['normalized_subject'] = match.get('normalized_subject', {})
                        obj['normalized_description'] = match.get('normalized_description', {})
                        obj['normalized_opinion'] = match.get('normalized_opinion', {})
                        obj['std_viewpoint'] = match.get('std_viewpoint', {})
                        matched_objects.append(obj)  # 添加到匹配成功的列表
                        matched_count += 1
                        matched = True
                        break  # 找到匹配后跳出循环
                
                if not matched:
                    removed_count += 1
                    self.logger.info(f'未找到匹配，删除对象: aspect="{aspect_text}", opinion="{opinion_text}"')
            
            # 更新llm_result，只保留匹配成功的对象
            llm_result['result'] = matched_objects
                                    
            self.logger.info(f"向量匹配结果处理完成: 成功匹配 {matched_count} 个, 删除未匹配 {removed_count} 个实体-观点对")
            
            return llm_result
        except Exception as e:
            self.logger.error(f"处理向量匹配时发生异常: {str(e)}")
            # 发生异常时返回原始结果，不中断流程
            return llm_result


    async def start_consumers(self):
        """启动消费者"""
        # 启动LLM消息消费者
        try:
            self.kafka_client.start_consumer(
                [self.kafka_client.TOPICS['LLM_TOPIC']], 
                self.process_llm_message,
                group_id='llm-service-group',
                consumer_name=None,
                auto_offset_reset='latest',
                enable_auto_commit=False  # 启用手动提交
            )
            self.logger.info("LLM consumers started")
        except Exception as e:
            self.logger.error(f"启动消费者时发生异常: {str(e)}")
            # 系统级错误，没有原始消息数据，只记录日志
    
    async def _service_specific_health_check(self) -> dict:
        """LLM服务特定的健康检查"""
        # 测试LLM API连通性
        api_status = "unknown"
        try:
            response = await self.http_client.get(
                self.model_config['api_url'].replace('/v1/chat/completions', '/health'),
                timeout=5
            )
            api_status = "healthy" if response.status_code == 200 else "unhealthy"

        except Exception as e:
            api_status = "unhealthy"
            # 健康检查错误，没有原始消息数据，只记录日志
            self.logger.error(f"LLM API健康检查失败: {str(e)}")
        
        # 检查向量匹配服务健康状态
        vector_matcher_status = "unknown"
        try:
            vector_matcher_status = "healthy" if await self.check_vector_matcher_health() else "unhealthy"
        except:
            vector_matcher_status = "unhealthy"
        
        return {
            'llm_api': api_status,
            'vector_matcher': vector_matcher_status
        }
    
    # ✅ 修复：将函数移到类内部
    def _add_custom_routes(self, app: FastAPI):
        """添加LLM服务特定的路由"""
        @app.post("/process")
        async def process_text(request: dict):
            """手动处理文本（用于测试）"""
            try:
                
                # 将文本转换为topic_text格式（List[Dict[str, str]]）
                topic_text = [{"role": "user", "content": text}]
                # 使用请求中传递的参数创建SourceData
                source_data = SourceData(
                    dataSource=request.get('dataSource', 'test'),
                    domainListJson=request.get('domainListJson', ['test']),
                    productName=request.get('productName', 'test_product'),
                    populationGroup=request.get('populationGroup', 'test_group'),
                    businessType=request.get('businessType', 'test_business'),
                    businessScene=request.get('businessScene', 'test_scene'),
                    textType=request.get('textType', 1),
                    title=request.get('title', '测试标题'),
                    topic_text=text,  # 使用传递的text
                    optionScore=request.get('optionScore', {'score': 0.5}),
                    position=request.get('position', 1),
                    forumSeries=request.get('forumSeries', 'test_series'),
                    brand=request.get('brand', 'test_brand'),
                    series=request.get('series', 'test_series'),
                    ext=request.get('ext'),
                    create_time=request.get('create_time', '2024-01-01 00:00:00')
                )

                # 构造完整的 CategoryMessage 数据
                category_data = {
                    'topic_id': 'manual_test',
                    'source_data': source_data.dict(),
                    'cat_type': 'llm',
                    'retry_count': 0,
                    'error_type': '',
                    'error_msg': '',
                    'last_ts': '',
                    'brand_series_list': request.get('brand_series_list', [])
                }
                model_result = await self.process_llm_message(category_data)
                
                return APIResponse(
                    success=True, 
                    message="Complete processing completed", 
                    data=model_result or {}
                )
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))


# 创建服务实例
service = LLMService()

# 创建FastAPI应用
app = service.create_fastapi_app(
    title="LLM Service",
    description="大模型服务"
)


@app.get("/health")
async def health_check():
    """健康检查端点"""
    try:
        health_data = await service.health_check()
        return APIResponse(success=True, message="Service is healthy", data=health_data)
    except Exception as e:
        return APIResponse(success=False, message=f"Health check failed: {e}")

if __name__ == "__main__":
    service.run_with_app_string("main:app")