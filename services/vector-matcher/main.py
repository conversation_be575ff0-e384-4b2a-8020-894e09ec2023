"""
向量匹配服务
负责模糊匹配主体和描述
"""
import asyncio
import signal
import sys
from pathlib import Path
from typing import List, Tuple, Optional, Dict, Any

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from fastapi import FastAPI, HTTPException
from contextlib import asynccontextmanager
import uvicorn

from shared.base import BaseService
from shared.utils.text_processor import TextProcessor
from shared.models.schemas import (
    OpinionMatch, APIResponse, VectorSearchResult
)


class VectorMatcherService(BaseService):
    """向量匹配服务"""
    
    def __init__(self):
        super().__init__('vector-matcher')
        self.text_processor = None
        self.vector_config = self.config_manager.get_vector_config()
        
        # 新词库插入任务队列和信号量
        self.new_word_semaphore = asyncio.Semaphore(5)  # 最多同时5个新词库插入任务
        self.new_word_queue = asyncio.Queue(maxsize=1000)  # 使用队列确保不丢失数据
        self.new_word_workers = []  # 后台工作线程
        self.new_word_worker_count = 3  # 后台工作线程数量
        
        # 新词去重缓存（内存中记录已处理的新词，避免重复插入）
        self.processed_new_words = set()  # 存储已处理的新词键值
        self.cache_max_size = 10000  # 缓存最大大小
    
    def _init_service_specific_config(self):
        """初始化服务特定配置"""
        pass
    
    async def _initialize_service_specific(self):
        """服务特定的初始化逻辑"""
        # 初始化数据库和Elasticsearch客户端
        from shared.database.mysql_client import MySQLClient
        from shared.elasticsearch.es_client import ElasticsearchClient
        
        db_config = self.config_manager.get_database_config()
        # 覆盖为 MySQL 专用配置（优先 MYSQL_* 环境变量，回退到 configs/config.yaml 的 database.mysql.*）
        db_host = self.config_manager._get_env_or_config('MYSQL_HOST', 'database.mysql.host', db_config.host)
        db_port = self.config_manager._get_env_or_config('MYSQL_PORT', 'database.mysql.port', db_config.port)
        db_user = self.config_manager._get_env_or_config('MYSQL_USERNAME', 'database.mysql.username', db_config.username)
        db_pass = self.config_manager._get_env_or_config('MYSQL_PASSWORD', 'database.mysql.password', db_config.password)
        # 数据库名仅使用 MYSQL_DATABASE → database.mysql.database（非品牌车系服务不读取 database1）
        db_name = self.config_manager._get_env_or_config('MYSQL_DATABASE', 'database.mysql.database', db_config.database)
        db_config.host = db_host
        db_config.port = int(db_port)
        db_config.username = db_user
        db_config.password = db_pass
        db_config.database = db_name
        self.logger.info(f"MySQL config: {db_config}")
        self.mysql_client = MySQLClient(db_config)
        await self.mysql_client.initialize()
        
        # 初始化Elasticsearch客户端（品牌车系数据在ES中）
        self.es_client = ElasticsearchClient(
            self.config_manager.get_elasticsearch_config(),
            self.config_manager.get_vector_config()
        )
        await self.es_client.initialize()
        
        # 初始化文本处理器（仅文本处理）
        self.text_processor = TextProcessor()
        await self.text_processor.initialize()
        
        # 初始化向量化客户端（调用外部API）
        from shared.utils.embedding_client import get_embedding_client
        self.embedding_client = get_embedding_client()
        
        # 启动新词库插入后台工作线程
        await self._start_new_word_workers()
    
    async def match_entity_vector(self, entity: str) -> Optional[VectorSearchResult]:
        """向量匹配主体"""
        try:
            # 1. 对主体进行向量化（调用外部API）
            entity_vector = await self.embedding_client.embed_single(entity)
            
            # 2. 在ES中搜索相似主体
            results = await self.es_client.search_similar_entities(
                query_vector=entity_vector,
                threshold=self.vector_config['similarity_threshold'],
                size=1  # 只取TOP 1
            )
            
            self.structured_logger.log_elasticsearch_operation(
                'vector_search', 'entity_synonyms', len(results), 0.1,
                entity=entity, threshold=self.vector_config['similarity_threshold']
            )
            
            return results[0] if results else None
            
        except Exception as e:
            self.logger.error(f"Error in entity vector matching: {e}")
            return None
    
    async def match_description_vector(
        self, 
        description: str, 
        standard_opinion_ids: List[str]
    ) -> Optional[VectorSearchResult]:
        """在指定标准观点下向量匹配描述"""
        try:
            # 1. 对描述进行向量化（调用外部API）
            desc_vector = await self.embedding_client.embed_single(description)
            
            # 2. 在ES中搜索相似描述
            results = await self.es_client.search_similar_descriptions(
                query_vector=desc_vector,
                standard_opinion_ids=standard_opinion_ids,
                threshold=self.vector_config['similarity_threshold'],
                size=1  # 只取TOP 1
            )
            
            self.structured_logger.log_elasticsearch_operation(
                'vector_search', 'description_synonyms', len(results), 0.1,
                description=description, opinion_ids_count=len(standard_opinion_ids)
            )
            
            return results[0] if results else None
            
        except Exception as e:
            self.logger.error(f"Error in description vector matching: {e}")
            return None
    
    async def fuzzy_match_single(
        self, 
        original_text: str,
        entity: str, 
        description: str
    ) -> Optional[OpinionMatch]:
        """单个主体-描述对的模糊匹配"""
        try:
            # 1. 向量匹配主体
            entity_result = await self.match_entity_vector(entity)
            self.logger.info(f"entity_result: {entity_result}")
            if not entity_result:
                # 主体匹配失败，写入MySQL新词库
                suggestions = await self.get_match_suggestions(entity, description, top_k=5)
                await self.mysql_client.insert_new_word_record(
                    original_text=original_text,
                    segment="",
                    new_entity=entity,
                    new_description=description,
                    recommended_label="|".join(suggestions) if suggestions else "",
                    status_flag=0
                )
                return None
            
            # 2. 获取标准观点ID列表
            entity_source = entity_result.source
            standard_opinion_ids = entity_source.get('standard_opinion_id_list', [])
            
            if not standard_opinion_ids:
                suggestions = await self.get_match_suggestions(entity, description, top_k=5)
                await self.mysql_client.insert_new_word_record(
                    original_text=original_text,
                    segment="",
                    new_entity=entity,
                    new_description=description,
                    recommended_label="|".join(suggestions) if suggestions else "",
                    status_flag=0
                )
                return None
            
            # 3. 向量匹配描述
            desc_result = await self.match_description_vector(description, standard_opinion_ids)
            self.logger.info(f"desc_result: {desc_result}")
            if not desc_result:
                # 描述匹配失败，写入MySQL新词库
                suggestions = await self.get_match_suggestions(entity, description, top_k=5)
                await self.mysql_client.insert_new_word_record(
                    original_text=original_text,
                    segment="",
                    new_entity=entity,
                    new_description=description,
                    recommended_label="|".join(suggestions) if suggestions else "",
                    status_flag=0
                )
                return None
            
            # 4. 获取标准观点信息
            desc_source = desc_result.source
            standard_opinion_id = desc_source['standard_opinion_id']  # 保持字符串格式

            # 从检索结果中提取标准化文本
            subject_text = entity_result.source.get('sim_entity') or ''
            description_text = desc_result.source.get('sim_description') or ''
            
            # 改为从 MySQL 的 opinion_synonym 表获取标准观点信息
            standard_opinion_row = await self.mysql_client.get_opinion_synonym(
                subject_text=subject_text,
                description_text=description_text,
                standard_opinion_id=standard_opinion_id
            )
            self.logger.info(f"standard_opinion_row: {standard_opinion_row}")
            if not standard_opinion_row:
                suggestions = await self.get_match_suggestions(entity, description, top_k=5)
                await self.mysql_client.insert_new_word_record(
                    original_text=original_text,
                    segment="",
                    new_entity=entity,
                    new_description=description,
                    recommended_label="|".join(suggestions) if suggestions else "",
                    status_flag=0
                )
                return None
            
            # 5. 构建匹配结果
            # 计算综合置信度（主体和描述相似度的平均值）
            combined_confidence = (entity_result.score + desc_result.score) / 2
            
            # 构建标准化结果
            from shared.models.schemas import NormalizedSubject, NormalizedDescription, StandardViewpoint, NormalizedOpinion
            
            
            
            normalized_subject = NormalizedSubject(
                id=str(entity_source.get('entity_id', '')),
                text=subject_text,
                score=entity_result.score
            )
            
            normalized_description = NormalizedDescription(
                id=str(desc_source.get('description_id', '')),
                text=description_text,
                score=desc_result.score
            )
            
            normalized_opinion = NormalizedOpinion(
                id=str(standard_opinion_row.get('opinion_id', '')),
                text=standard_opinion_row.get('opinion', ''),
                score=combined_confidence
            )
            
            std_viewpoint = StandardViewpoint(
                id=str(standard_opinion_id),
                text=standard_opinion_row.get('standard_opinion', '')
            )
            
            match = OpinionMatch(
                original_text=original_text,
                original_entity=entity,
                original_description=description,
                normalized_subject=normalized_subject,
                normalized_description=normalized_description,
                normalized_opinion=normalized_opinion,
                std_viewpoint=std_viewpoint,
                match_type="vector"
            )
            
            return match
            
        except Exception as e:
            self.logger.error(f"Error in fuzzy matching: {e}")
            # 发生错误时也写入MySQL新词库
            suggestions = await self.get_match_suggestions(entity, description, top_k=5)
            await self.mysql_client.insert_new_word_record(
                original_text=original_text,
                segment="",
                new_entity=entity,
                new_description=description,
                recommended_label="|".join(suggestions) if suggestions else "",
                status_flag=0
            )
            return None
    
    async def fuzzy_match_pairs(
        self, 
        entity_desc_pairs: List[Tuple[str, str]]
    ) -> List[Dict]:
        """模糊匹配主体-描述对列表"""
        try:
            matches = []
            
            for original_text, entity, description in entity_desc_pairs:
                match = await self.fuzzy_match_single(original_text, entity, description)
                if match:
                    # 转换为字典格式，保持与规则匹配一致的输出格式
                    # match_dict = {
                    #     "original_entity": match.original_entity,
                    #     "original_description": match.original_description,
                    #     "standard_entity": match.normalized_subject.text if match.normalized_subject else entity,
                    #     "standard_description": match.normalized_description.text if match.normalized_description else description,
                    #     "standard_opinion_id": match.std_viewpoint.id if match.std_viewpoint else None,
                    #     "confidence": match.std_viewpoint.confidence if match.std_viewpoint else 0.7,
                    #     "match_type": "vector"
                    # }
                    # matches.append(match_dict)
                    matches.append(match)
            self.logger.info(f"matches: {matches}")
            self.structured_logger.log_business_event(
                'fuzzy_matching_completed',
                pairs_count=len(entity_desc_pairs),
                matches_found=len(matches)
            )
            
            return matches
            
        except Exception as e:
            self.logger.error(f"Error in fuzzy matching pairs: {e}")
            return []

    async def fuzzy_match_pairs_with_metadata(
        self, 
        entity_desc_pairs: List[Tuple[str, str, str, Any]]
    ) -> List[Dict]:
        """模糊匹配主体-描述对列表，保留元数据 - 真正的批量处理"""
        try:
            if not entity_desc_pairs:
                return []
            
            # 1. 提取所有唯一的实体和描述，保持索引映射
            entities = []
            descriptions = []
            pair_mapping = []  # 记录每个pair的信息和索引
            
            for idx, (original_text, entity, description, message_index) in enumerate(entity_desc_pairs):
                entities.append(entity)
                descriptions.append(description)
                pair_mapping.append({
                    'index': idx,
                    'original_text': original_text,
                    'entity': entity,
                    'description': description,
                    'message_index': message_index
                })
            
            # 2. 先进行主体批量匹配
            self.logger.info(f"Batch querying {len(entities)} entities")
            entity_results = await self.batch_match_subjects(entities)
            
            # 处理可能的异常
            if isinstance(entity_results, Exception):
                self.logger.error(f"Batch entity matching failed: {entity_results}")
                entity_results = [None] * len(entities)
            
            # 3. 基于主体匹配结果进行描述匹配
            self.logger.info(f"Batch querying {len(descriptions)} descriptions with entity constraints")
            description_results = await self.batch_match_descriptions_with_constraints(
                descriptions, entity_results
            )
            
            # 处理可能的异常
            if isinstance(description_results, Exception):
                self.logger.error(f"Batch description matching failed: {description_results}")
                description_results = [None] * len(descriptions)
            
            # 4. 组合结果
            matches = []
            for idx, pair_info in enumerate(pair_mapping):
                entity_match = entity_results[idx] if idx < len(entity_results) else None
                desc_match = description_results[idx] if idx < len(description_results) else None
                
                # 检查是否需要插入新词库
                if not entity_match or not desc_match:
                    # 如果实体或描述匹配失败，异步插入MySQL新词库（不阻塞主流程）
                    self._schedule_new_word_insertion(
                        pair_info['original_text'],
                        pair_info['entity'],
                        pair_info['description'],
                        "entity_or_desc_match_failed"
                    )
                    
                    # 跳过这个匹配对，不创建匹配对象
                    continue
                
                # 只有当实体和描述都有匹配结果时，才创建匹配对象
                if entity_match and desc_match:
                    # 构建normalized_subject
                    normalized_subject = {
                        'id': entity_match.get('id'),
                        'text': entity_match.get('text'),
                        'score': entity_match.get('score', 0.0)
                    }
                    
                    # 构建normalized_description
                    normalized_description = {
                        'id': desc_match.get('id'),
                        'text': desc_match.get('text'),
                        'score': desc_match.get('score', 0.0)
                    }
                    
                    # 通过MySQL查询获取真正的观点信息
                    normalized_opinion = None
                    std_viewpoint = None
                    if entity_match and desc_match:
                        # 获取standard_opinion_id用于数据库查询
                        standard_opinion_id = desc_match.get('standard_opinion_id')
                        if standard_opinion_id:
                            try:
                                # 使用MySQL客户端查询观点同义词表
                                opinion_data = await self.mysql_client.get_opinion_synonym(
                                    subject_text=entity_match.get('text', pair_info['entity']),
                                    description_text=desc_match.get('text', pair_info['description']),
                                    standard_opinion_id=standard_opinion_id
                                )
                                
                                if opinion_data:
                                    normalized_opinion = {
                                        'id': opinion_data.get('opinion_id'),
                                        'text': opinion_data.get('opinion')
                                    }
                                    std_viewpoint = {
                                        'id': standard_opinion_id,
                                        'text': opinion_data.get('standard_opinion'),
                                        'confidence': self._calculate_combined_confidence(entity_match, desc_match)
                                    }
                                else:
                                    # 如果数据库中没有找到对应记录，异步插入新词库
                                    self.logger.warning(f"No opinion synonym found for entity: {entity_match.get('text')}, description: {desc_match.get('text')}, standard_opinion_id: {standard_opinion_id}")
                                    self._schedule_new_word_insertion(
                                        pair_info['original_text'],
                                        pair_info['entity'],
                                        pair_info['description'],
                                        "opinion_synonym_not_found"
                                    )
                                    
                            except Exception as e:
                                self.logger.error(f"Error querying opinion synonym: {e}")
                                # 查询出错时也异步插入新词库
                                self._schedule_new_word_insertion(
                                    pair_info['original_text'],
                                    pair_info['entity'],
                                    pair_info['description'],
                                    "mysql_query_error"
                                )
                    
                    # 创建OpinionMatch对象
                    match = OpinionMatch(
                        original_text=pair_info['original_text'],
                        original_entity=pair_info['entity'],
                        original_description=pair_info['description'],
                        normalized_subject=normalized_subject,
                        normalized_description=normalized_description,
                        normalized_opinion=normalized_opinion,
                        std_viewpoint=std_viewpoint,
                        match_type="vector"
                    )
                    
                    # 将OpinionMatch转换为字典并添加message_index
                    match_dict = match.model_dump()
                    match_dict['message_index'] = pair_info['message_index']
                    match_dict['original_text'] = pair_info['original_text']
                    
                    matches.append(match_dict)
            
            self.logger.info(f"Batch matching completed: {len(matches)} matches found from {len(entity_desc_pairs)} pairs")
            self.structured_logger.log_business_event(
                'batch_fuzzy_matching_completed',
                pairs_count=len(entity_desc_pairs),
                matches_found=len(matches),
                entities_queried=len(entities),
                descriptions_queried=len(descriptions)
            )
            
            return matches
            
        except Exception as e:
            self.logger.error(f"Error in batch fuzzy matching with metadata: {e}")
            return []

    async def batch_match_subjects(self, entities: List[str]) -> List[Optional[Dict]]:
        """批量匹配主体"""
        try:
            if not entities:
                return []
            
            # 使用ES批量查询所有实体
            results = await self.es_client.batch_search_entities(entities)
            
            # 转换结果格式
            normalized_results = []
            for i, entity in enumerate(entities):
                if i < len(results) and results[i] and len(results[i]) > 0:
                    best_match = results[i][0]
                    normalized_results.append({
                        'id': best_match.get('id'),
                        'text': best_match.get('text', ''),
                        'standard_opinion_id_list': best_match.get('source', {}).get('standard_opinion_id_list', []),
                        'score': best_match.get('score', 0.0),
                        'source': best_match.get('source', {})  # 保留source信息用于获取standard_opinion_id_list
                    })
                else:
                    normalized_results.append(None)
            
            self.logger.info(f"Batch entity matching: {len(entities)} entities, {sum(1 for r in normalized_results if r)} matches")
            return normalized_results
            
        except Exception as e:
            self.logger.error(f"Error in batch subject matching: {e}")
            return [None] * len(entities)

    async def batch_match_descriptions(self, descriptions: List[str]) -> List[Optional[Dict]]:
        """批量匹配描述（无约束）"""
        try:
            if not descriptions:
                return []
            
            # 使用ES批量查询所有描述
            results = await self.es_client.batch_search_descriptions(descriptions)
            
            # 转换结果格式
            normalized_results = []
            for i, description in enumerate(descriptions):
                if i < len(results) and results[i] and len(results[i]) > 0:
                    best_match = results[i][0]
                    normalized_results.append({
                        'id': best_match.get('id'),
                        'text': best_match.get('text', ''),
                        'score': best_match.get('score', 0.0)
                    })
                else:
                    normalized_results.append(None)
            
            self.logger.info(f"Batch description matching: {len(descriptions)} descriptions, {sum(1 for r in normalized_results if r)} matches")
            return normalized_results
            
        except Exception as e:
            self.logger.error(f"Error in batch description matching: {e}")
            return [None] * len(descriptions)

    async def batch_match_descriptions_with_constraints(
        self, 
        descriptions: List[str], 
        entity_results: List[Optional[Dict]]
    ) -> List[Optional[Dict]]:
        """基于主体匹配结果批量匹配描述"""
        try:
            if not descriptions:
                return []
            
            # 为每个描述构建约束条件
            description_constraints = []
            for i, description in enumerate(descriptions):
                entity_match = entity_results[i] if i < len(entity_results) else None
                standard_opinion_ids = []
                
                if entity_match:
                    # 从主体匹配结果中获取standard_opinion_id_list
                    standard_opinion_ids = entity_match.get('standard_opinion_id_list', [])
                
                description_constraints.append({
                    'description': description,
                    'standard_opinion_ids': standard_opinion_ids
                })
            
            # 使用ES批量查询带约束的描述
            results = await self.es_client.batch_search_descriptions_with_constraints(
                description_constraints
            )
            
            # 转换结果格式
            normalized_results = []
            for i, description in enumerate(descriptions):
                if i < len(results) and results[i] and len(results[i]) > 0:
                    best_match = results[i][0]
                    normalized_results.append({
                        'id': best_match.get('id'),
                        'text': best_match.get('text', ''),
                        'standard_opinion_id': best_match.get('standard_opinion_id', ''),
                        'score': best_match.get('score', 0.0)
                    })
                else:
                    normalized_results.append(None)
            
            self.logger.info(f"Batch constrained description matching: {len(descriptions)} descriptions, {sum(1 for r in normalized_results if r)} matches")
            return normalized_results
            
        except Exception as e:
            self.logger.error(f"Error in batch constrained description matching: {e}")
            return [None] * len(descriptions)

    def _generate_new_word_key(self, entity: str, description: str) -> str:
        """生成新词的唯一键值（用于去重）"""
        # 使用entity和description的组合作为唯一标识
        # 去除空格并转小写，提高去重效果
        clean_entity = entity.strip().lower() if entity else ""
        clean_description = description.strip().lower() if description else ""
        return f"{clean_entity}|{clean_description}"

    def _schedule_new_word_insertion(
        self, 
        original_text: str, 
        entity: str, 
        description: str, 
        reason: str
    ):
        """调度新词库插入任务（使用队列确保不丢失，带去重机制）"""
        # 生成去重键值
        new_word_key = self._generate_new_word_key(entity, description)
        
        # 检查是否已经处理过这个新词
        if new_word_key in self.processed_new_words:
            self.logger.debug(f"Skipping duplicate new word insertion: entity={entity}, description={description}")
            return
        
        # 添加到已处理缓存
        self.processed_new_words.add(new_word_key)
        
        # 检查缓存大小，防止内存无限增长
        if len(self.processed_new_words) > self.cache_max_size:
            # 清理一半的缓存（简单的LRU策略）
            old_size = len(self.processed_new_words)
            # 将set转为list，保留后一半
            cache_list = list(self.processed_new_words)
            self.processed_new_words = set(cache_list[old_size // 2:])
            self.logger.info(f"Cleaned new word cache: {old_size} -> {len(self.processed_new_words)}")
        
        task_data = {
            'original_text': original_text,
            'entity': entity,
            'description': description,
            'reason': reason,
            'new_word_key': new_word_key,
            'timestamp': asyncio.get_event_loop().time()
        }
        
        try:
            # 非阻塞地将任务放入队列
            self.new_word_queue.put_nowait(task_data)
            self.logger.debug(f"Queued new word insertion: entity={entity}, reason={reason}, queue_size={self.new_word_queue.qsize()}")
        except asyncio.QueueFull:
            # 如果队列满了，从缓存中移除（因为任务没有成功入队）
            self.processed_new_words.discard(new_word_key)
            self.logger.error(f"New word insertion queue is full, task will be retried: entity={entity}, reason={reason}")
            # 创建一个重试任务
            asyncio.create_task(self._retry_queue_insertion(task_data))

    async def _start_new_word_workers(self):
        """启动新词库插入后台工作线程"""
        for i in range(self.new_word_worker_count):
            worker = asyncio.create_task(self._new_word_worker(f"worker-{i}"))
            self.new_word_workers.append(worker)
        self.logger.info(f"Started {self.new_word_worker_count} new word insertion workers")

    async def _new_word_worker(self, worker_name: str):
        """新词库插入工作线程"""
        self.logger.info(f"New word worker {worker_name} started")
        
        while True:
            try:
                # 从队列中获取任务
                task_data = await self.new_word_queue.get()
                
                # 执行插入任务
                await self._process_new_word_task(task_data, worker_name)
                
                # 标记任务完成
                self.new_word_queue.task_done()
                
            except asyncio.CancelledError:
                self.logger.info(f"New word worker {worker_name} cancelled")
                break
            except Exception as e:
                self.logger.error(f"Error in new word worker {worker_name}: {e}")
                # 工作线程出错不应该停止，继续处理下一个任务
                continue

    async def _process_new_word_task(self, task_data: dict, worker_name: str):
        """处理单个新词库插入任务"""
        original_text = task_data['original_text']
        entity = task_data['entity']
        description = task_data['description']
        reason = task_data['reason']
        
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                self.logger.info(f"[{worker_name}] Processing new word insertion: entity={entity}, reason={reason}, attempt={retry_count + 1}")
                
                # 获取匹配建议（这是耗时操作）
                suggestions = await self.get_match_suggestions(entity, description, top_k=5)
                
                # 插入新词库记录
                await self.mysql_client.insert_new_word_record(
                    original_text=original_text,
                    segment="",
                    new_entity=entity,
                    new_description=description,
                    recommended_label="|".join(suggestions) if suggestions else "",
                    status_flag=0
                )
                
                self.logger.info(f"[{worker_name}] Successfully inserted new word record: entity={entity}, reason={reason}, suggestions_count={len(suggestions)}")
                return  # 成功，退出重试循环
                
            except Exception as e:
                retry_count += 1
                self.logger.error(f"[{worker_name}] Error in new word insertion (attempt {retry_count}/{max_retries}): entity={entity}, reason={reason}, error={e}")
                
                if retry_count < max_retries:
                    # 等待一段时间后重试
                    await asyncio.sleep(2 ** retry_count)  # 指数退避
                else:
                    # 达到最大重试次数，记录到失败日志
                    self.logger.error(f"[{worker_name}] Failed to insert new word after {max_retries} attempts: entity={entity}, reason={reason}")
                    # TODO: 可以考虑写入到文件或其他持久化存储

    async def _retry_queue_insertion(self, task_data: dict):
        """重试将任务放入队列"""
        max_retries = 5
        retry_count = 0
        new_word_key = task_data.get('new_word_key')
        
        while retry_count < max_retries:
            await asyncio.sleep(1)  # 等待1秒
            try:
                # 重试前再次检查是否已经被处理（可能在重试期间被其他请求处理了）
                if new_word_key and new_word_key in self.processed_new_words:
                    self.logger.debug(f"New word already processed during retry: entity={task_data['entity']}")
                    return
                
                # 如果还没有在缓存中，重新添加
                if new_word_key:
                    self.processed_new_words.add(new_word_key)
                
                self.new_word_queue.put_nowait(task_data)
                self.logger.info(f"Successfully queued new word task after retry: entity={task_data['entity']}")
                return
            except asyncio.QueueFull:
                # 重试失败，从缓存中移除
                if new_word_key:
                    self.processed_new_words.discard(new_word_key)
                retry_count += 1
                self.logger.warning(f"Queue still full, retry {retry_count}/{max_retries}: entity={task_data['entity']}")
        
        # 如果仍然无法放入队列，从缓存中移除并记录严重错误
        if new_word_key:
            self.processed_new_words.discard(new_word_key)
        self.logger.critical(f"Failed to queue new word task after {max_retries} retries: entity={task_data['entity']}, reason={task_data['reason']}")
        # TODO: 可以考虑写入到紧急备份文件

    def _calculate_combined_confidence(self, entity_match: Optional[Dict], desc_match: Optional[Dict]) -> float:
        """计算组合置信度"""
        entity_score = entity_match.get('score', 0.0) if entity_match else 0.0
        desc_score = desc_match.get('score', 0.0) if desc_match else 0.0
        
        if entity_match and desc_match:
            # 两个都匹配，取平均值
            return (entity_score + desc_score) / 2.0
        elif entity_match or desc_match:
            # 只有一个匹配，使用该分数但打折扣
            return max(entity_score, desc_score) * 0.8
        else:
            return 0.0
    
    async def fuzzy_match_text(self, text: str) -> List[OpinionMatch]:
        """对整个文本进行模糊匹配"""
        try:
            # 1. 提取主体-描述对
            entity_desc_pairs = self.text_processor.extract_entity_description_pairs(text)
            
            # 2. 模糊匹配
            matches = await self.fuzzy_match_pairs(entity_desc_pairs)
            
            return matches
            
        except Exception as e:
            self.logger.error(f"Error in text fuzzy matching: {e}")
            return []
    
    async def get_match_suggestions(
        self, 
        entity: str, 
        description: str,
        top_k: int = 5
    ) -> List[str]:
        """基于主体与描述的组合，返回Top-K标准观点列表（按综合分数排序）。
        综合分数 = (主体匹配分数 + 描述匹配分数) / 2
        """
        try:
            # 1) 主体向量与候选实体
            entity_vector = await self.embedding_client.embed_single(entity)
            entity_results = await self.es_client.search_similar_entities(
                query_vector=entity_vector,
                threshold=0.3,  # 降低阈值获取更多建议
                size=top_k
            )

            if not entity_results:
                return []

            # 2) 针对每个主体候选，获取其标准观点ID下的描述候选，计算综合分数
            candidates: List[Tuple[str, float]] = []  # (standard_opinion_text, combined_score)
            desc_vector = await self.embedding_client.embed_single(description)

            for e_res in entity_results:
                standard_opinion_ids = e_res.source.get('standard_opinion_id_list', [])
                if not standard_opinion_ids:
                    continue

                desc_results = await self.es_client.search_similar_descriptions(
                    query_vector=desc_vector,
                    standard_opinion_ids=standard_opinion_ids,
                    threshold=0.3,
                    size=top_k
                )

                subject_text = e_res.source.get('sim_entity') or ''
                for d_res in desc_results:
                    description_text = d_res.source.get('sim_description') or ''
                    standard_opinion_id = d_res.source.get('standard_opinion_id')
                    if not standard_opinion_id:
                        continue

                    combined_score = (e_res.score + d_res.score) / 2

                    # 查询标准观点文本
                    row = await self.mysql_client.get_opinion_synonym(
                        subject_text=subject_text,
                        description_text=description_text,
                        standard_opinion_id=standard_opinion_id
                    )
                    std_text = row.get('standard_opinion', '') if row else ''
                    if std_text:
                        candidates.append((std_text, combined_score))

            if not candidates:
                return []

            # 3) 按综合分数排序并去重（同名标准观点仅保留最高分）
            candidates.sort(key=lambda x: x[1], reverse=True)
            seen: set = set()
            top_std: List[str] = []
            for std_text, _ in candidates:
                if std_text in seen:
                    continue
                seen.add(std_text)
                top_std.append(std_text)
                if len(top_std) >= top_k:
                    break

            return top_std
            
        except Exception as e:
            self.logger.error(f"Error getting match suggestions: {e}")
            return []
    
    async def start_consumers(self):
        """启动消费者（向量匹配服务通常不需要消费者）"""
        self.logger.info("Vector matcher service: no consumers to start")
    
    async def _service_specific_health_check(self) -> dict:
        """服务特定的健康检查"""
        queue_size = self.new_word_queue.qsize()
        max_size = self.new_word_queue.maxsize
        cache_size = len(self.processed_new_words)
        
        return {
            'database': 'connected',
            'elasticsearch': 'connected',
            'text_processor': 'ready' if self.text_processor else 'not_ready',
            'new_word_queue': {
                'current_size': queue_size,
                'max_size': max_size,
                'utilization': f"{(queue_size / max_size * 100):.1f}%",
                'available_slots': max_size - queue_size,
                'active_workers': len([w for w in self.new_word_workers if not w.done()]),
                'total_workers': len(self.new_word_workers),
                'status': 'healthy' if queue_size < max_size * 0.8 else 'warning'
            },
            'deduplication_cache': {
                'current_size': cache_size,
                'max_size': self.cache_max_size,
                'utilization': f"{(cache_size / self.cache_max_size * 100):.1f}%",
                'status': 'healthy' if cache_size < self.cache_max_size * 0.9 else 'warning'
            }
        }
    
    async def _shutdown_service_specific(self):
        """服务特定的关闭逻辑"""
        # 优雅关闭新词库工作线程
        self.logger.info("Shutting down new word insertion workers...")
        
        # 等待队列中的任务完成
        if hasattr(self, 'new_word_queue'):
            try:
                # 等待最多30秒让队列中的任务完成
                await asyncio.wait_for(self.new_word_queue.join(), timeout=30.0)
                self.logger.info("All new word insertion tasks completed")
            except asyncio.TimeoutError:
                self.logger.warning("Timeout waiting for new word insertion tasks to complete")
        
        # 取消所有工作线程
        for worker in self.new_word_workers:
            worker.cancel()
        
        # 等待工作线程结束
        if self.new_word_workers:
            await asyncio.gather(*self.new_word_workers, return_exceptions=True)
            self.logger.info("All new word workers stopped")
        
        # 关闭其他客户端
        if hasattr(self, 'es_client') and self.es_client:
            await self.es_client.close()
        
        if hasattr(self, 'mysql_client') and self.mysql_client:
            await self.mysql_client.close()


    def _add_custom_routes(self, app: FastAPI):
        """添加服务特定的路由"""
        @app.post("/match/fuzzy")
        async def fuzzy_match(request: dict):
            """模糊匹配文本"""
            try:
                text = request.get('text', '')
                if not text:
                    raise HTTPException(status_code=400, detail="Text is required")
                
                matches = await self.fuzzy_match_text(text)
                return APIResponse(
                    success=True, 
                    message="Fuzzy matching completed", 
                    data={'matches': [match.model_dump() for match in matches]}
                )
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @app.post("/match/pairs")
        async def match_pairs(request: dict):
            """匹配主体-描述对"""
            try:
                pairs = request.get('pairs', [])
                if not pairs:
                    raise HTTPException(status_code=400, detail="Pairs are required")
                
                # 支持扩展格式：[original_text, entity, description, message_index]
                entity_desc_pairs = []
                for pair in pairs:
                    if len(pair) >= 3:
                        original_text, entity, description = pair[0], pair[1], pair[2]
                        message_index = pair[3] if len(pair) > 3 else None
                        entity_desc_pairs.append([original_text, entity, description, message_index])
                
                matches = await self.fuzzy_match_pairs_with_metadata(entity_desc_pairs)
                return APIResponse(
                    success=True, 
                    message="Pairs matching completed", 
                    data={'matches': matches}
                )
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @app.post("/suggestions")
        async def get_suggestions(request: dict):
            """获取匹配建议"""
            try:
                entity = request.get('entity', '')
                description = request.get('description', '')
                top_k = request.get('top_k', 5)
                
                if not entity or not description:
                    raise HTTPException(status_code=400, detail="Entity and description are required")
                
                suggestions = await self.get_match_suggestions(entity, description, top_k)
                return APIResponse(
                    success=True, 
                    message="Suggestions retrieved", 
                    data=suggestions
                )
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))


# 创建服务实例
service = VectorMatcherService()

# 创建FastAPI应用
app = service.create_fastapi_app(
    title="Vector Matcher Service",
    description="向量匹配服务"
)


if __name__ == "__main__":
    service.run_with_app_string("main:app")