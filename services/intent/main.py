"""
意图和情感分析服务
负责识别文本的意图和情感倾向，扩充UIE提取的主体+描述到完整语境
合并了原sentiment服务的功能，统一提供意图和情感分析
"""
import asyncio
import signal
import sys
from pathlib import Path
from typing import List, Dict, Any, Optional
import json
import httpx

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from fastapi import FastAPI, HTTPException
from contextlib import asynccontextmanager
import uvicorn

from shared.base import ModelService
from shared.models.schemas import (
    ModelResultMessage, APIResponse, IntentMessage, SourceData, BrandAttributionMessage, ErrorMessage
)


class IntentSentimentService(ModelService):
    """意图和情感分析服务"""
    
    def __init__(self):
        super().__init__('intent')
        
        # 定义有效的分类标准（用于API输出验证）
        self.valid_sentiments = {'正面', '负面', '中性'}
        self.valid_intents = {'抱怨', '表扬', '咨询', '建议'}
        
        # 批量处理相关变量
        self.message_batch = []  # 消息批量缓存
        self.current_batch_text_count = 0  # 当前批次中的文本数量
        self.batch_lock = asyncio.Lock()  # 批量处理锁
        self.batch_start_time = None  # 批量开始时间
        
        # 后台超时检查任务
        self.timeout_check_task = None
    
    def _init_service_specific_config(self):
        """初始化Intent服务特定配置"""
        # 获取批量处理配置
        if self.model_config:
            self.batch_size = self.model_config.get('batch_size', 32)
            self.batch_timeout = self.model_config.get('batch_timeout', 3.0)
            self.enable_batch_processing = self.model_config.get('enable_batch_processing', True)
        else:
            self.batch_size = 32
            self.batch_timeout = 3.0
            self.enable_batch_processing = True
        
        # 注意：此时logger还未初始化，延迟到_initialize_service_specific中记录日志

    async def _initialize_service_specific(self):
        """Intent服务特定的初始化逻辑"""
        # 记录批量处理配置（此时logger已初始化）
        self.logger.info(f"Intent batch processing config: batch_size={self.batch_size}, batch_timeout={self.batch_timeout}, enabled={self.enable_batch_processing}")
        
        # 启动后台超时检查任务
        if self.enable_batch_processing:
            self.timeout_check_task = asyncio.create_task(self._batch_timeout_checker())
            self.logger.info("Intent batch timeout checker started")
        
        # Intent服务特定的初始化已在基类中完成
        pass
    
    def _validate_and_normalize_sentiment(self, sentiment: str) -> str:
        """验证并规范化情感分析结果"""
        if sentiment in self.valid_sentiments:
            return sentiment
        else:
            self.logger.warning(f"无效的情感分类: {sentiment}, 默认为中性")
            return "中性"
    
    def _validate_and_normalize_intent(self, intent: str) -> str:
        """验证并规范化意图识别结果"""
        if intent in self.valid_intents:
            return intent
        else:
            self.logger.warning(f"无效的意图分类: {intent}, 默认为空字符串")
            return ""
    
    # 删除了基于规则的情感分析方法，因为API故障时直接返回空字符串
    
    def _expand_entity_description_to_context(self, entity: str, description: str, full_text: str) -> str:
        """将UIE提取的主体+描述扩充到完整语境
        
        例如：大灯-好看 → 请问这个车的大灯好看吗？
        这样可以更准确地分析情感和意图
        """
        import re
        
        # 使用多种标点符号分割句子，包括中英文标点
        # 注意：冒号不用于分割，因为它通常用于引出解释或说明
        sentence_delimiters = r'[。！？；\n\r.!?;\s]{1,2}'
        sentences = re.split(sentence_delimiters, full_text)
        
        # 过滤掉空句子并清理空白
        sentences = [s.strip() for s in sentences if s.strip()]
        
        # 优先级1: 找到同时包含entity和description的句子
        for sentence in sentences:
            if entity in sentence and description in sentence:
                self.logger.debug(f"找到包含主体和描述的完整句子: {sentence}")
                return sentence
        
        # 优先级2: 尝试部分匹配，但仍要求主体和描述都存在
        entity_parts = entity.split() if len(entity) > 2 else [entity]
        description_parts = description.split() if len(description) > 2 else [description]
        
        for sentence in sentences:
            # 检查是否包含主体和描述的任何部分组合
            has_entity_part = any(part in sentence for part in entity_parts)
            has_description_part = any(part in sentence for part in description_parts)
            
            if has_entity_part and has_description_part:
                self.logger.debug(f"找到包含主体和描述部分的句子: {sentence}")
                return sentence
        
        # 最后回退：返回原始组合
        self.logger.debug(f"未找到相关句子，返回原始组合: {entity}{description}")
        return f"{entity}{description}"
    
    # 删除了基于规则的意图分析方法，因为API故障时直接返回空字符串
    
    async def _batch_timeout_checker(self):
        """后台任务：定期检查批量超时"""
        while True:
            try:
                # 每1秒检查一次
                await asyncio.sleep(1.0)
                
                # 检查批量是否超时
                if self.message_batch:
                    # 计算批次中第一个消息的等待时间
                    import time
                    current_time = time.time()
                    
                    # 检查批量是否超时
                    if self.batch_start_time:
                        elapsed_time = current_time - self.batch_start_time
                        if elapsed_time >= self.batch_timeout:
                            self.logger.info(f"Background timeout check: batch timeout triggered ({elapsed_time:.1f}s >= {self.batch_timeout}s), processing {len(self.message_batch)} messages")
                            async with self.batch_lock:
                                if self.message_batch:  # 再次检查，防止竞态条件
                                    await self._process_message_batch()
                            
            except asyncio.CancelledError:
                self.logger.info("Intent batch timeout checker task cancelled")
                break
            except Exception as e:
                self.logger.error(f"Error in intent batch timeout checker: {e}")
                # 继续运行，不要因为单次错误停止检查

    async def call_unified_analysis_api(self, texts: List[str]) -> List[Dict[str, Any]]:
        """调用统一的意图和情感分析API"""
        try:
            # 构建请求数据，只需要texts参数
            request_data = {
                "texts": texts
            }
            
            headers = {
                "Content-Type": "application/json"
            }
            
            if self.model_config.get('api_key'):
                headers["Authorization"] = f"Bearer {self.model_config['api_key']}"
            
            # 使用配置优化的httpx客户端，与UIE API调用保持一致
            async with httpx.AsyncClient(
                timeout=httpx.Timeout(30.0, connect=10.0),
                limits=httpx.Limits(
                    max_connections=1,
                    max_keepalive_connections=0
                ),
                http2=False,
                follow_redirects=True,
                trust_env=False,
                verify=False
            ) as client:
                response = await client.post(
                    self.model_config['api_url'],
                    json=request_data,
                    headers=headers
                )
            
            if response.status_code == 200:
                response_data = response.json()
                results = []
                
                # 检查响应格式是否正确
                if response_data.get('code') == 200 and 'data' in response_data:
                    api_results = response_data['data']
                    
                    for i, text in enumerate(texts):
                        if i < len(api_results) and api_results[i]:
                            # 从嵌套数组中提取第一个结果对象
                            api_result = api_results[i][0] if api_results[i] else {}
                            
                            # 提取意图和情感信息
                            intent_info = api_result.get('意图', [{}])
                            sentiment_info = api_result.get('情感倾向', [{}])
                            
                            # 提取原始结果
                            raw_intent = intent_info[0].get('text', '咨询') if intent_info else '咨询'
                            raw_sentiment = sentiment_info[0].get('text', '中性') if sentiment_info else '中性'
                            
                            # 验证并规范化结果
                            validated_intent = self._validate_and_normalize_intent(raw_intent)
                            validated_sentiment = self._validate_and_normalize_sentiment(raw_sentiment)
                            
                            results.append({
                                "text": text,
                                "intent": validated_intent,
                                "sentiment": validated_sentiment,
                                "intent_confidence": 0.8,  # API没有返回置信度，使用默认值
                                "sentiment_confidence": 0.8
                            })
                        else:
                            # API结果不足，直接返回空字符串
                            results.append({
                                "text": text,
                                "intent": "",
                                "sentiment": "",
                                "intent_confidence": 0.0,
                                "sentiment_confidence": 0.0
                            })
                else:
                    raise ValueError(f"API响应格式错误: {response_data}")
                
                return results
            else:
                raise httpx.HTTPStatusError(f"Analysis API请求失败: {response.status_code}", request=response.request, response=response)
                
        except Exception as e:
            self.logger.warning(f"Analysis API调用失败，返回空字符串: {e}")
            # fallback: 直接返回空字符串
            results = []
            for text in texts:
                results.append({
                    "text": text,
                    "intent": "",
                    "sentiment": "",
                    "intent_confidence": 0.0,
                    "sentiment_confidence": 0.0
                })
            return results
    
    # 删除了 _update_result_with_intent 方法，因为不再使用规则分析方法
    
    def _update_result_with_analysis(
        self, 
        result_data: List[Dict[str, Any]], 
        analysis_results: List[Dict[str, Any]], 
        entity_contexts: List[Dict[str, str]]
    ) -> Dict[str, Any]:
        """将意图和情感分析结果更新到原始结果中"""
        
        # 为每个评价对象分配意图和情感
        for i, eval_obj in enumerate(result_data):
            entity = eval_obj.get('aspect')
            entity = entity['text'] if isinstance(entity, dict) else ''
            
            # 找到对应的分析结果
            best_analysis = None
            for j, context_info in enumerate(entity_contexts):
                if context_info['entity'] == entity and j < len(analysis_results):
                    best_analysis = analysis_results[j]
                    break
            
            # 如果没找到对应的分析结果，使用第一个
            if not best_analysis and analysis_results:
                best_analysis = analysis_results[0]
            
            # 更新意图和情感信息（使用英文字段名）
            if best_analysis:
                eval_obj['intent'] = best_analysis['intent']
                eval_obj['sentiment'] = best_analysis['sentiment']
                
                # 更新segment为扩充后的完整语境
                corresponding_context = next(
                    (ctx for ctx in entity_contexts if ctx['entity'] == entity), 
                    None
                )
                if corresponding_context:
                    eval_obj['segment'] = corresponding_context['context']
            else:
                # 如果没有分析结果，使用空字符串（保持与API故障时的行为一致）
                eval_obj['intent'] = ""
                eval_obj['sentiment'] = ""
        
        return result_data
    
    def _extract_texts_for_analysis(self, message: ModelResultMessage) -> tuple:
        """提取需要分析的文本
        
        Returns:
            entity_contexts: List[Dict] - 包含entity, description, context的完整信息
            original_text: str - 原始文本，用于发送给下游的topic_text字段
        """
        entity_contexts = []  # 存储实体和对应的上下文
        original_text = ""
        
        if message.result:
            # 获取原始文本（所有eval_obj应该有相同的original_text）
            first_eval_obj = message.result[0] if message.result else {}
            original_text = first_eval_obj.get('original_text', '')
            
            for eval_obj in message.result:
                entity = eval_obj.get('aspect')
                entity = entity.get('text') if isinstance(entity, dict) else ''
                description = eval_obj.get('description')
                description = description.get('text') if isinstance(description, dict) else ''
                
                if entity and description:
                    # 将UIE的主体+描述扩充到完整语境
                    expanded_context = self._expand_entity_description_to_context(
                        entity, description, original_text
                    )
                    entity_contexts.append({
                        'entity': entity,
                        'description': description,
                        'context': expanded_context  # 这就是需要分析的文本
                    })
        
        # 如果没有提取到文本，使用原始文本
        if not entity_contexts:
            entity_contexts = [{'entity': '', 'description': '', 'context': original_text}]
        
        return entity_contexts, original_text

    async def _process_single_message(self, message_data: dict):
        """处理单个消息（非批量模式）"""
        try:
            message = ModelResultMessage(**message_data)
            
            # 提取需要分析的文本
            entity_contexts, original_text = self._extract_texts_for_analysis(message)
            
            # 从entity_contexts中提取需要分析的文本
            texts_to_analyze = [ctx['context'] for ctx in entity_contexts]
            
            # 调用统一的意图和情感分析API
            start_time = asyncio.get_event_loop().time()
            analysis_results = await self.call_unified_analysis_api(texts_to_analyze)
            duration = asyncio.get_event_loop().time() - start_time
            
            self.structured_logger.log_model_inference(
                'intent_sentiment', len(texts_to_analyze), duration, True,
                topic_id=message.topic_id
            )
            
            # 更新结果中的意图和情感信息
            updated_results = self._update_result_with_analysis(
                message.result, analysis_results, entity_contexts
            )
            
            # 发送到brand_attribution_topic
            brand_message = {
                "topic_id": message.topic_id,
                "source_data": message.source_data.model_dump() if hasattr(message, 'source_data') and hasattr(message.source_data, 'model_dump') else getattr(message, 'source_data', {}),
                "topic_text": original_text,
                "raw_split_text": getattr(message, 'raw_split_text', []),
                "cat_type": message.cat_type,
                "retry_count": message.retry_count,
                "error_type": message.error_type,
                "error_msg": message.error_msg,
                "last_ts": message.last_ts,
                "result": updated_results,
                "qaresult": message.qaresult
            }
            
            # 发送到brand_attribution_topic
            self.kafka_client.send_message(
                self.kafka_client.TOPICS.get('BRAND_ATTRIBUTION_TOPIC', 'brand_attribution_topic'),
                brand_message
            )
            
            self.structured_logger.log_business_event(
                'intent_sentiment_analysis_completed',
                topic_id=message.topic_id,
                texts_analyzed=len(texts_to_analyze),
                processing_duration=duration
            )
            
        except Exception as e:
            self.logger.error(f"Error processing single intent message: {e}")
            
            # 发送错误消息
            source_data = message_data.get('source_data', {})
            if isinstance(source_data, dict):
                source_data = SourceData(**source_data)
                
            error_message = ErrorMessage(
                topic_id=message_data.get('topic_id', 'unknown'),
                source_data=source_data,
                retry_count=message_data.get('retry_count', 0),
                error_type='intent_single_processing_error',
                error_msg=str(e),
                last_ts=message_data.get('last_ts', '')
            )
            self.kafka_client.send_to_error_topic(error_message)
    
    async def _process_message_batch(self):
        """处理消息批次"""
        if not self.message_batch:
            return
        
        try:
            batch_messages = self.message_batch.copy()
            batch_text_count = self.current_batch_text_count
            
            # 清空批量缓存和计数器
            self.message_batch.clear()
            self.current_batch_text_count = 0
            self.batch_start_time = None  # 重置开始时间
            
            self.logger.info(f"Processing batch of {len(batch_messages)} messages with {batch_text_count} total texts")
            
            # 收集所有需要分析的文本和对应信息
            all_texts = []
            text_to_message_mapping = []  # 记录每个文本在all_texts中的索引对应的消息和文本索引
            
            for msg_idx, (message, entity_contexts, original_text) in enumerate(batch_messages):
                for text_idx, context_info in enumerate(entity_contexts):
                    all_texts.append(context_info['context'])
                    text_to_message_mapping.append((msg_idx, text_idx))
            
            # 批量调用API
            start_time = asyncio.get_event_loop().time()
            all_analysis_results = await self.call_unified_analysis_api(all_texts)
            duration = asyncio.get_event_loop().time() - start_time
            
            self.structured_logger.log_model_inference(
                'intent_sentiment_batch', len(all_texts), duration, True,
                batch_size=len(batch_messages)
            )
            
            # 将结果分配回各个消息
            for msg_idx, (message, entity_contexts, original_text) in enumerate(batch_messages):
                # 获取属于这个消息的分析结果
                message_analysis_results = []
                for global_text_idx, (mapped_msg_idx, mapped_text_idx) in enumerate(text_to_message_mapping):
                    if mapped_msg_idx == msg_idx and global_text_idx < len(all_analysis_results):
                        message_analysis_results.append(all_analysis_results[global_text_idx])
                
                # 更新结果中的意图和情感信息
                updated_results = self._update_result_with_analysis(
                    message.result, message_analysis_results, entity_contexts
                )
                
                # 发送到brand_attribution_topic
                brand_message = {
                    "topic_id": message.topic_id,
                    "source_data": message.source_data.model_dump() if hasattr(message, 'source_data') and hasattr(message.source_data, 'model_dump') else getattr(message, 'source_data', {}),
                    "topic_text": original_text,
                    "raw_split_text": getattr(message, 'raw_split_text', []),
                    "cat_type": message.cat_type,
                    "retry_count": message.retry_count,
                    "error_type": message.error_type,
                    "error_msg": message.error_msg,
                    "last_ts": message.last_ts,
                    "result": updated_results,
                    "qaresult": message.qaresult
                }
                
                # 发送到brand_attribution_topic
                self.kafka_client.send_message(
                    self.kafka_client.TOPICS.get('BRAND_ATTRIBUTION_TOPIC', 'brand_attribution_topic'),
                    brand_message
                )
                
                self.structured_logger.log_business_event(
                    'intent_sentiment_analysis_completed',
                    topic_id=message.topic_id,
                    texts_analyzed=len(entity_contexts),
                    processing_duration=duration / len(batch_messages)  # 平均处理时间
                )
            
            self.logger.info(f"Batch processing completed: {len(batch_messages)} messages, {len(all_texts)} texts")
            
        except Exception as e:
            self.logger.error(f"Error processing message batch: {e}")
            # 对批次中的每个消息发送错误
            for message, _, _ in batch_messages:
                try:
                    source_data = message.source_data if hasattr(message, 'source_data') else {}
                    if isinstance(source_data, dict):
                        source_data = SourceData(**source_data)
                        
                    error_message = ErrorMessage(
                        topic_id=message.topic_id,
                        source_data=source_data,
                        retry_count=message.retry_count,
                        error_type='intent_batch_processing_error',
                        error_msg=str(e),
                        last_ts=message.last_ts
                    )
                    self.kafka_client.send_to_error_topic(error_message)
                except Exception as send_error:
                    self.logger.error(f"Error sending error message for topic {message.topic_id}: {send_error}")
        
        finally:
            # 清理批次状态（已经在try开始时清理了）
            pass

    async def process_intent_message(self, message_data: dict):
        """处理意图和情感分析消息（支持批量处理）"""
        try:
            message = ModelResultMessage(**message_data)
            self.structured_logger.log_kafka_message(
                'received', 'intent_topic', message.topic_id
            )
            
            # 如果不启用批量处理，直接处理
            if not self.enable_batch_processing:
                await self._process_single_message(message_data)
                return
            
            # 提取需要分析的文本
            entity_contexts, original_text = self._extract_texts_for_analysis(message)
            
            # 批量处理逻辑
            async with self.batch_lock:
                # 检查添加这个消息是否会超过batch_size
                new_text_count = len(entity_contexts)
                if self.current_batch_text_count + new_text_count > self.batch_size:
                    # 先处理当前批次
                    self.logger.info(f"Current batch would exceed batch_size ({self.current_batch_text_count} + {new_text_count} > {self.batch_size}), processing current batch first")
                    await self._process_message_batch()
                
                # 将消息添加到批次中
                self.message_batch.append((message, entity_contexts, original_text))
                self.current_batch_text_count += new_text_count
                
                self.logger.debug(f"Added message to batch: topic_id={message.topic_id}, texts={new_text_count}, batch_size={len(self.message_batch)}, total_texts={self.current_batch_text_count}")
                
                # 如果达到批次大小，立即处理
                if self.current_batch_text_count >= self.batch_size:
                    self.logger.info(f"Batch size reached ({self.current_batch_text_count} >= {self.batch_size}), processing batch")
                    await self._process_message_batch()
                else:
                    # 如果这是批次中的第一个消息，记录开始时间
                    if len(self.message_batch) == 1:
                        import time
                        self.batch_start_time = time.time()  # 记录批次开始时间
                        self.logger.info(f"Started batch timer, current batch: {len(self.message_batch)} messages, {self.current_batch_text_count} texts")
                    else:
                        self.logger.info(f"Message added to existing batch, batch: {len(self.message_batch)} messages, {self.current_batch_text_count} texts")
            
        except Exception as e:
            self.logger.error(f"Error processing intent message: {e}")
            
            # 使用标准的ErrorMessage格式，确保不修改源数据
            source_data = message_data.get('source_data', {})
            if isinstance(source_data, dict):
                source_data = SourceData(**source_data)
                
            error_message = ErrorMessage(
                topic_id=message_data.get('topic_id', 'unknown'),
                source_data=source_data,
                retry_count=message_data.get('retry_count', 0),
                error_type='intent_analysis_error',
                error_msg=str(e),
                last_ts=message_data.get('last_ts', '')
            )
            self.kafka_client.send_to_error_topic(error_message)

    async def _cleanup_batch_processing(self):
        """清理批量处理资源"""
        # 停止后台超时检查任务
        if self.timeout_check_task:
            self.logger.info("Stopping intent batch timeout checker")
            self.timeout_check_task.cancel()
            try:
                await self.timeout_check_task
            except asyncio.CancelledError:
                self.logger.info("Intent batch timeout checker stopped successfully")
            except Exception as e:
                self.logger.error(f"Error stopping intent batch timeout checker: {e}")
            finally:
                self.timeout_check_task = None
        
        async with self.batch_lock:
            if self.message_batch:
                self.logger.info(f"Processing remaining {len(self.message_batch)} messages ({self.current_batch_text_count} texts) in batch during cleanup")
                await self._process_message_batch()
            
            # 重置状态
            self.batch_start_time = None
            
            self.logger.info("Intent batch processing cleanup completed")

    async def _shutdown_service_specific(self):
        """Intent服务特定的关闭逻辑"""
        # 清理批量处理资源
        await self._cleanup_batch_processing()
    
    async def start_consumers(self):
        """启动消费者"""
        # 启动意图识别消息消费者
        self.kafka_client.start_consumer(
            [self.kafka_client.TOPICS['INTENT_TOPIC']], 
            self.process_intent_message,
            group_id='intent-service-group',
            consumer_name=None,
            auto_offset_reset='latest',
            enable_auto_commit=False  # 启用手动提交
        )
        
        self.logger.info("Intent consumers started")
    
    async def _service_specific_health_check(self) -> dict:
        """Intent服务特定的健康检查"""
        import time
        
        # 使用缓存机制减少频繁的API调用
        # 只在5分钟间隔检查一次外部API，其他时候返回缓存状态
        current_time = time.time()
        
        # 初始化缓存
        if not hasattr(self, '_api_status_cache'):
            self._api_status_cache = {
                'status': 'unknown',
                'last_check': 0
            }
        
        # 如果距离上次检查超过5分钟，才进行实际API检查
        if current_time - self._api_status_cache['last_check'] > 300:  # 5分钟
            try:
                # 使用轻量级测试：发送空数据到API测试连通性
                test_data = {"texts": []}
                headers = {
                    "Content-Type": "application/json",
                    "Accept": "application/json"
                }
                
                if self.model_config.get('api_key'):
                    headers["Authorization"] = f"Bearer {self.model_config['api_key']}"
                
                response = await self.http_client.post(
                    self.model_config['api_url'],
                    json=test_data,
                    headers=headers,
                    timeout=5
                )
                
                # 更新缓存 - 只有200才是真正健康的
                if response.status_code == 200:
                    self._api_status_cache['status'] = "healthy"
                elif response.status_code in [400, 422]:
                    # 400/422表示API能响应但参数有问题，说明API服务正常但我们的测试数据不合适
                    self._api_status_cache['status'] = "api_reachable_bad_request"
                else:
                    # 其他状态码表示服务异常
                    self._api_status_cache['status'] = "unhealthy"
                
                self._api_status_cache['last_check'] = current_time
                
            except Exception as e:
                # API不可用时返回空字符串作为fallback
                self._api_status_cache['status'] = "empty_fallback"
                self._api_status_cache['last_check'] = current_time
                self.logger.debug(f"Intent API health check failed: {e}")
        
        # 添加批量处理状态
        batch_status = {
            'enabled': self.enable_batch_processing,
            'batch_size': self.batch_size,
            'batch_timeout': self.batch_timeout,
            'current_batch_messages': len(self.message_batch),
            'current_batch_texts': self.current_batch_text_count,
            'has_pending_timer': self.batch_start_time is not None
        }
        
        return {
            'intent_sentiment_api': self._api_status_cache['status'],
            'fallback_method': 'empty_strings',
            'validation_enabled': True,
            'last_api_check': time.strftime('%H:%M:%S', time.localtime(self._api_status_cache['last_check'])),
            'check_interval': '5min',
            'batch_processing': batch_status
        }
    
    def _add_custom_routes(self, app: FastAPI):
        """添加Intent服务特定的路由"""
        @app.post("/analyze")
        async def analyze_intent(request: dict):
            """手动分析意图（用于测试）"""
            try:
                texts = request.get('texts', [])
                if not texts:
                    raise HTTPException(status_code=400, detail="Texts are required")
                
                results = await self.call_unified_analysis_api(texts)
                
                return APIResponse(
                    success=True, 
                    message="Intent analysis completed", 
                    data=results
                )
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @app.get("/batch/status")
        async def get_batch_status():
            """获取批量处理状态"""
            try:
                return APIResponse(
                    success=True,
                    message="Batch status retrieved",
                    data={
                        'enabled': self.enable_batch_processing,
                        'batch_size': self.batch_size,
                        'batch_timeout': self.batch_timeout,
                        'current_batch_messages': len(self.message_batch),
                        'current_batch_texts': self.current_batch_text_count,
                        'has_pending_timer': self.batch_start_time is not None
                    }
                )
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @app.post("/batch/force_process")
        async def force_process_batch():
            """强制处理当前批次（用于测试）"""
            try:
                async with self.batch_lock:
                    if self.message_batch:
                        batch_count = len(self.message_batch)
                        text_count = self.current_batch_text_count
                        await self._process_message_batch()
                        return APIResponse(
                            success=True,
                            message=f"Forced processing of {batch_count} messages with {text_count} texts"
                        )
                    else:
                        return APIResponse(
                            success=True,
                            message="No messages in current batch to process"
                        )
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))


# 创建服务实例
service = IntentSentimentService()

# 创建FastAPI应用
app = service.create_fastapi_app(
    title="Intent and Sentiment Service",
    description="意图和情感分析服务 - 扩充UIE主体+描述到完整语境，合并原sentiment服务功能"
)


if __name__ == "__main__":
    service.run_with_app_string("main:app")