"""
错误处理服务
负责处理系统错误和重试逻辑
"""
import asyncio
import signal
import sys
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from fastapi import FastAPI, HTTPException
from contextlib import asynccontextmanager
import uvicorn

from shared.base import BaseService
from shared.models.schemas import (
    ErrorMessage, BaseKafkaMessage, ErrorRecord, APIResponse
)


class ErrorHandlerService(BaseService):
    """错误处理服务"""
    
    def __init__(self):
        super().__init__('error-handler')
        self.retry_config = self.config_manager.get_retry_config()
    
    def _init_service_specific_config(self):
        """初始化服务特定配置"""
        pass
    
    async def _initialize_service_specific(self):
        """服务特定的初始化逻辑"""
        # 初始化数据库客户端
        from shared.database.mysql_client import MySQLClient
        db_config = self.config_manager.get_database_config()
        # 覆盖为 MySQL 专用配置（优先 MYSQL_*，其次 DB_*，再读 configs/config.yaml 的 database.mysql.*）
        db_host = self.config_manager._get_env_or_config('MYSQL_HOST', 'database.mysql.host', self.config_manager._get_env_or_config('DB_HOST', 'database.mysql.host', db_config.host))
        db_port = self.config_manager._get_env_or_config('MYSQL_PORT', 'database.mysql.port', self.config_manager._get_env_or_config('DB_PORT', 'database.mysql.port', db_config.port))
        db_user = self.config_manager._get_env_or_config('MYSQL_USERNAME', 'database.mysql.username', self.config_manager._get_env_or_config('DB_USERNAME', 'database.mysql.username', db_config.username))
        db_pass = self.config_manager._get_env_or_config('MYSQL_PASSWORD', 'database.mysql.password', self.config_manager._get_env_or_config('DB_PASSWORD', 'database.mysql.password', db_config.password))
        # 非品牌车系服务：数据库名仅使用 MYSQL_DATABASE/DB_DATABASE → database.mysql.database
        db_name = self.config_manager._get_env_or_config('MYSQL_DATABASE', 'database.mysql.database', self.config_manager._get_env_or_config('DB_DATABASE', 'database.mysql.database', db_config.database))
        db_config.host = db_host
        db_config.port = int(db_port)
        db_config.username = db_user
        db_config.password = db_pass
        db_config.database = db_name
        self.mysql_client = MySQLClient(db_config)
        await self.mysql_client.initialize()
    
    async def process_error_message(self, message_data: dict):
        """处理错误消息"""
        try:
            error_message = ErrorMessage(**message_data)
            self.structured_logger.log_kafka_message(
                'received', 'model_error_topic', error_message.topic_id
            )
            
            # 检查重试次数
            if error_message.retry_count < self.retry_config['max_retries']:
                # 还可以重试
                await self._schedule_retry(error_message)
            else:
                # 重试次数用完，存入错误库
                await self._save_to_error_database(error_message)
            
        except Exception as e:
            self.logger.error(f"Error processing error message: {e}")
            # 避免循环错误，直接记录日志
            self.structured_logger.log_error(
                'error_processing_failed',
                f"Failed to process error message: {e}",
                original_message=message_data
            )
    
    async def _schedule_retry(self, error_message: ErrorMessage):
        """安排重试"""
        try:
            # 增加重试次数
            retry_message = BaseKafkaMessage(
                topic_id=error_message.topic_id,
                source_data=error_message.source_data,
                retry_count=error_message.retry_count + 1,
                error_type="",
                error_msg="",
                last_ts=""
            )
            
            # 计算延迟时间
            delay = self._calculate_retry_delay(error_message.retry_count)
            
            # 记录重试信息
            self.structured_logger.log_business_event(
                'retry_scheduled',
                topic_id=error_message.topic_id,
                retry_count=retry_message.retry_count,
                delay_seconds=delay,
                error_type=error_message.error_type
            )
            
            # 延迟后发送到重试topic
            if delay > 0:
                await asyncio.sleep(delay)
            
            self.kafka_client.send_to_retry_topic(retry_message)
            
            self.logger.info(f"Retry scheduled for topic_id: {error_message.topic_id}, attempt: {retry_message.retry_count}")
            
        except Exception as e:
            self.logger.error(f"Failed to schedule retry: {e}")
            # 如果重试调度失败，直接存入错误库
            await self._save_to_error_database(error_message)
    
    async def _save_to_error_database(self, error_message: ErrorMessage):
        """保存到错误数据库并发送空消息到final_topic"""
        try:
            error_record = ErrorRecord(
                topic_id=error_message.topic_id,
                topic_text=error_message.source_data.topic_text,
                retry_count=error_message.retry_count,
                error_type=error_message.error_type,
                last_ts=datetime.now().isoformat(),
                final_status="pending"
            )
            
            record_id = await self.mysql_client.insert_error_record(error_record)
            
            self.structured_logger.log_business_event(
                'error_saved_to_database',
                topic_id=error_message.topic_id,
                record_id=record_id,
                retry_count=error_message.retry_count,
                error_type=error_message.error_type
            )
            
            self.logger.info(f"Error record saved to database: {record_id} for topic_id: {error_message.topic_id}")
            
            # 发送空消息到final_topic
            await self._send_empty_final_message(error_message)
            
        except Exception as e:
            self.logger.error(f"Failed to save error to database: {e}")
            # 记录到文件作为最后的保障
            self._log_error_to_file(error_message, str(e))
    
    async def _send_empty_final_message(self, error_message: ErrorMessage):
        """发送空消息到final_topic"""
        try:
            # 构建空的final消息
            empty_final_message = {
                "nlpParam": {
                    "topic_id": error_message.topic_id,
                    "source_data": error_message.source_data.model_dump()
                },
                "nlpResult": {
                    "dimensions": []  # 空数组表示处理失败，没有结果
                }
            }
            
            # 发送到final_topic
            self.kafka_client.send_to_final_topic(empty_final_message)
            
            self.structured_logger.log_business_event(
                'empty_final_message_sent',
                topic_id=error_message.topic_id,
                retry_count=error_message.retry_count,
                error_type=error_message.error_type
            )
            
            self.logger.info(f"Empty final message sent to final_topic for topic_id: {error_message.topic_id}")
            
        except Exception as e:
            self.logger.error(f"Failed to send empty final message: {e}")
            # 不抛出异常，避免影响错误记录的保存
    
    def _calculate_retry_delay(self, retry_count: int) -> int:
        """计算重试延迟时间"""
        base_delay = self.retry_config.get('retry_delay', 60)  # 基础延迟60秒
        
        if self.retry_config.get('exponential_backoff', True):
            # 指数退避：2^retry_count * base_delay
            delay = base_delay * (2 ** retry_count)
            # 最大延迟不超过1小时
            return min(delay, 3600)
        else:
            # 固定延迟
            return base_delay
    
    def _log_error_to_file(self, error_message: ErrorMessage, additional_error: str):
        """记录错误到文件（最后的保障）"""
        try:
            error_log_path = Path("logs/critical_errors.log")
            error_log_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(error_log_path, 'a', encoding='utf-8') as f:
                timestamp = datetime.now().isoformat()
                log_entry = {
                    'timestamp': timestamp,
                    'topic_id': error_message.topic_id,
                    'topic_text': error_message.source_data.topic_text[:100] + '...' if len(error_message.source_data.topic_text) > 100 else error_message.source_data.topic_text,
                    'retry_count': error_message.retry_count,
                    'error_type': error_message.error_type,
                    'error_msg': error_message.error_msg,
                    'additional_error': additional_error
                }
                f.write(f"{log_entry}\n")
                
        except Exception as e:
            # 如果连文件写入都失败，只能打印到控制台
            print(f"CRITICAL: Failed to log error to file: {e}")
            print(f"Original error: {error_message.model_dump()}")
    
    async def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        try:
            # 这里可以扩展更复杂的统计查询
            sql = """
            SELECT 
                error_type,
                COUNT(*) as count,
                AVG(retry_count) as avg_retry_count
            FROM error_records 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)
            GROUP BY error_type
            ORDER BY count DESC
            """
            
            results = await self.mysql_client.execute_query(sql)
            
            # 获取总体统计
            total_sql = """
            SELECT 
                COUNT(*) as total_errors,
                COUNT(CASE WHEN final_status = 'pending' THEN 1 END) as pending_errors,
                COUNT(CASE WHEN final_status = 'fixed' THEN 1 END) as fixed_errors
            FROM error_records
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)
            """
            
            total_results = await self.mysql_client.execute_query(total_sql)
            
            return {
                'error_types': results,
                'summary': total_results[0] if total_results else {},
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get error statistics: {e}")
            return {}
    
    async def update_error_status(self, topic_id: str, status: str) -> bool:
        """更新错误状态"""
        try:
            sql = "UPDATE error_records SET final_status = %s WHERE topic_id = %s"
            affected_rows = await self.mysql_client.execute_update(sql, (status, topic_id))
            
            if affected_rows > 0:
                self.structured_logger.log_business_event(
                    'error_status_updated',
                    topic_id=topic_id,
                    new_status=status
                )
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to update error status: {e}")
            return False
    
    async def start_consumers(self):
        """启动消费者"""
        # 启动错误消息消费者
        self.kafka_client.start_consumer(
            ['model_error_topic'], 
            self.process_error_message,
            group_id='error-handler-service-group',
            consumer_name=None,
            auto_offset_reset='latest',
            enable_auto_commit=False  # 启用手动提交
        )
        
        self.logger.info("Error handler consumers started")
    
    async def _service_specific_health_check(self) -> dict:
        """服务特定的健康检查"""
        return {
            'database': 'connected',
            'max_retries': self.retry_config['max_retries'],
            'retry_delay': self.retry_config['retry_delay']
        }
    
    async def _shutdown_service_specific(self):
        """服务特定的关闭逻辑"""
        if hasattr(self, 'mysql_client') and self.mysql_client:
            await self.mysql_client.close()


    def _add_custom_routes(self, app: FastAPI):
        """添加服务特定的路由"""
        @app.get("/statistics")
        async def get_statistics():
            """获取错误统计"""
            try:
                stats = await self.get_error_statistics()
                return APIResponse(success=True, message="Statistics retrieved", data=stats)
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @app.put("/error/{topic_id}/status")
        async def update_error_status(topic_id: str, request: dict):
            """更新错误状态"""
            try:
                status = request.get('status', '')
                if status not in ['pending', 'fixed', 'skipped', 'manual_required']:
                    raise HTTPException(status_code=400, detail="Invalid status")
                
                success = await self.update_error_status(topic_id, status)
                if success:
                    return APIResponse(success=True, message="Error status updated")
                else:
                    return APIResponse(success=False, message="Error record not found")
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))


# 创建服务实例
service = ErrorHandlerService()

# 创建FastAPI应用
app = service.create_fastapi_app(
    title="Error Handler Service",
    description="错误处理服务"
)


if __name__ == "__main__":
    service.run_with_app_string("main:app")