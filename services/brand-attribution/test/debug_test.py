#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试测试 - 检查具体处理过程
"""

import json
import time
from kafka import KafkaProducer, KafkaConsumer

KAFKA_SERVER = '172.16.76.178:5084'

def send_debug_test():
    """发送调试测试数据"""
    # 原有的两车系对比测试数据
    test_data_comparison = {
        "topic_id": "debug_test_001",
        "source_data": {
            "dataSource": "汽车之家",
            "productName": "长安CS75 PLUS",
            "topic_text": "吉利星越的内饰很好看，比长安CS75的内饰强多了",
            "brand": "长安",
            "series": "CS75 PLUS",
            "create_time": "2025-09-03 19:00:00"
        },
        "cat_type": "uie", 
        "retry_count": 0,
        "result": [{
            "评价对象": [
                {
                    "relations": {
                        "segment": [{"text": "吉利星越的内饰很好看"}],
                        "品牌车系": [{"text": ""}],  # 空的，需要服务填充
                        "情感倾向": [{"text": "正向"}],
                        "意图": [{"text": "表扬"}],
                        "观点": [{"text": "很好看", "start": 6, "end": 9}]
                    },
                    "text": "内饰",
                    "start": 3,
                    "end": 5
                },
                {
                    "relations": {
                        "segment": [{"text": "比长安CS75的内饰强多了"}],
                        "品牌车系": [{"text": ""}],  # 空的，需要服务填充
                        "情感倾向": [{"text": "对比"}],
                        "意图": [{"text": "对比"}],
                        "观点": [{"text": "强多了", "start": 8, "end": 11}]
                    },
                    "text": "内饰",
                    "start": 7,
                    "end": 9
                }
            ]
        }]
    }
    
    # 新增的多车系测试数据（4个车系）
    test_data_multi_series = {
        "topic_id": "debug_test_002",
        "source_data": {
            "dataSource": "汽车之家",
            "productName": "长安CS75 PLUS",
            "topic_text": "长安CS75的动力表现不错，吉利博越的空间很宽敞，比亚迪汉的科技配置丰富，奇瑞瑞虎8的性价比很高",
            "brand": "长安",
            "series": "CS75 PLUS",
            "create_time": "2025-09-10 15:00:00"
        },
        "cat_type": "llm",  # 多车系应该走LLM分流
        "retry_count": 0,
        "result": [{
            "评价对象": [
                {
                    "relations": {
                        "segment": [{"text": "长安CS75的动力表现不错"}],
                        "品牌车系": [{"text": ""}],  # 空的，需要服务填充
                        "情感倾向": [{"text": "正向"}],
                        "意图": [{"text": "表扬"}],
                        "观点": [{"text": "不错", "start": 8, "end": 10}]
                    },
                    "text": "动力",
                    "start": 7,
                    "end": 9
                },
                {
                    "relations": {
                        "segment": [{"text": "吉利博越的空间很宽敞"}],
                        "品牌车系": [{"text": ""}],  # 空的，需要服务填充
                        "情感倾向": [{"text": "正向"}],
                        "意图": [{"text": "表扬"}],
                        "观点": [{"text": "很宽敞", "start": 23, "end": 26}]
                    },
                    "text": "空间",
                    "start": 20,
                    "end": 22
                },
                {
                    "relations": {
                        "segment": [{"text": "比亚迪汉的科技配置丰富"}],
                        "品牌车系": [{"text": ""}],  # 空的，需要服务填充
                        "情感倾向": [{"text": "正向"}],
                        "意图": [{"text": "表扬"}],
                        "观点": [{"text": "丰富", "start": 37, "end": 39}]
                    },
                    "text": "科技配置",
                    "start": 32,
                    "end": 36
                },
                {
                    "relations": {
                        "segment": [{"text": "奇瑞瑞虎8的性价比很高"}],
                        "品牌车系": [{"text": ""}],  # 空的，需要服务填充
                        "情感倾向": [{"text": "正向"}],
                        "意图": [{"text": "表扬"}],
                        "观点": [{"text": "很高", "start": 46, "end": 48}]
                    },
                    "text": "性价比",
                    "start": 43,
                    "end": 46
                }
            ]
        }]
    }
    
    # 选择要发送的测试数据
    test_data = test_data_comparison  # 默认发送多车系测试
    
    try:
        producer = KafkaProducer(
            bootstrap_servers=[KAFKA_SERVER],
            value_serializer=lambda v: json.dumps(v, ensure_ascii=False).encode('utf-8')
        )
        
        producer.send('brand_attribution_topic', test_data)
        producer.flush()
        print("✅ 调试测试消息发送成功!")
        print(f"   原文: {test_data['source_data']['topic_text']}")
        
        if test_data['topic_id'] == 'debug_test_001':
            print(f"   预期结果: 第一个内饰应该归属'吉利星越'，第二个内饰应该归属'长安CS75'")
        elif test_data['topic_id'] == 'debug_test_002':
            print(f"   预期结果: 包含4个车系的品牌归属判断")
            print(f"     - 动力 -> 长安CS75")
            print(f"     - 空间 -> 吉利博越")
            print(f"     - 科技配置 -> 比亚迪汉")
            print(f"     - 性价比 -> 奇瑞瑞虎8")
        producer.close()
        return True
        
    except Exception as e:
        print(f"❌ 发送失败: {e}")
        return False

def listen_debug_results():
    """监听调试结果"""
    try:
        consumer = KafkaConsumer(
            'result_topic',
            bootstrap_servers=[KAFKA_SERVER], 
            value_deserializer=lambda m: json.loads(m.decode('utf-8')),
            auto_offset_reset='latest',
            group_id='debug-test-group',
            consumer_timeout_ms=30000
        )
        
        print("📥 监听调试结果中...")
        
        for message in consumer:
            data = message.value
            topic_id = data.get('topic_id', '')
            
            if topic_id in ['debug_test_001', 'debug_test_002']:
                print(f"\n🎉 收到调试处理结果:")
                print("=" * 80)
                
                # 显示详细结果
                objects = data.get('result', [{}])[0].get('评价对象', [])
                for i, obj in enumerate(objects, 1):
                    entity = obj.get('text', '')
                    relations = obj.get('relations', {})
                    
                    segment = relations.get('segment', [{}])[0].get('text', '')
                    brand_series = relations.get('品牌车系', [{}])[0].get('text', '【未处理】')
                    sentiment = relations.get('情感倾向', [{}])[0].get('text', '')
                    opinion = relations.get('观点', [{}])[0].get('text', '')
                    
                    print(f"\n对象 {i}: {entity}")
                    print(f"  原始片段: {segment}")
                    print(f"  品牌车系: {brand_series}")
                    print(f"  情感: {sentiment}")
                    print(f"  观点: {opinion}")
                    
                    # 判断处理是否正确
                    if topic_id == 'debug_test_001':
                        # 原有的两车系比较测试
                        if '吉利星越' in segment and '吉利' in brand_series:
                            print(f"  ✅ 正确！识别出吉利星越")
                        elif '长安CS75' in segment and '长安' in brand_series:
                            print(f"  ✅ 正确！识别出长安品牌")
                        elif brand_series == '【未处理】':
                            print(f"  ❌ 未处理！服务没有填充品牌车系")
                        else:
                            print(f"  ⚠️  处理结果待验证")
                    elif topic_id == 'debug_test_002':
                        # 多车系测试
                        if '长安CS75' in segment and '长安' in brand_series:
                            print(f"  ✅ 正确！识别出长安CS75")
                        elif '吉利博越' in segment and '吉利' in brand_series:
                            print(f"  ✅ 正确！识别出吉利博越")
                        elif '比亚迪汉' in segment and '比亚迪' in brand_series:
                            print(f"  ✅ 正确！识别出比亚迪汉")
                        elif '奇瑞瑞虎8' in segment and '奇瑞' in brand_series:
                            print(f"  ✅ 正确！识别出奇瑞瑞虎8")
                        elif brand_series == '【未处理】':
                            print(f"  ❌ 未处理！服务没有填充品牌车系")
                        else:
                            print(f"  ⚠️  处理结果待验证")
                
                print("=" * 80)
                
                # 打印完整的JSON以供调试
                print("\n🔍 完整响应JSON:")
                print(json.dumps(data, ensure_ascii=False, indent=2))
                
                consumer.close()
                return True
        
        print("⚠️ 30秒内未收到结果")
        consumer.close()
        return False
        
    except Exception as e:
        print(f"❌ 监听失败: {e}")
        return False

if __name__ == "__main__":
    print("调试Kafka测试")
    print(f"Kafka: {KAFKA_SERVER}")
    print("-" * 60)
    
    if send_debug_test():
        time.sleep(3)  # 等待3秒让服务处理
        listen_debug_results()
    
    print("调试测试完成")