"""
品牌车系车型归属判断服务 (Brand Attribution Service)

通过自然语言理解判断观点归属的品牌车系车型
这是UIE流程中的关键NLU模块，补充架构中缺失的环节
"""

import json
import asyncio
import traceback
import sys
import os
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

# 设置工作目录为项目根目录（确保配置文件能被正确加载）
os.chdir(str(project_root))

# 添加当前服务目录到Python路径，用于导入同级模块
sys.path.append(str(Path(__file__).parent))

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import uvicorn

from shared.base import BaseService
from shared.models.schemas import BrandAttributionMessage, SourceData, ModelResultMessage, APIResponse, ErrorMessage
from shared.database.mysql_client import MySQLClient

# 直接导入同级模块
from brand_series_dict import get_all_brand_series, get_brand_series_synonyms


class BrandAttributionRequest(BaseModel):
    """品牌归属判断请求"""
    topic_id: str
    source_data: Dict[str, Any]
    segment: str
    entity: str
    description: str
    brand_series_candidates: List[str] = []


class BrandAttributionResponse(BaseModel):
    """品牌归属判断响应"""
    topic_id: str
    brand: str
    series: str = ""
    model: str = ""
    confidence: float
    attribution_reason: str


class BrandAttributionService(BaseService):
    """品牌车系车型归属判断服务"""
    
    def __init__(self):
        super().__init__('brand-attribution')
        self.brand_series_cache = {}
        self.synonym_map = {}  # 同义词缓存
        self.mysql_client = None  # 品牌车系数据库连接 (voc_ms_be)
        self.filter_mysql_client = None  # 过滤规则数据库连接 (voc_model)
        self.is_test = False  # 测试开关，True时使用硬编码数据
        
        # 智能过滤规则缓存
        self._filter_rules_cache = None
        self._filter_rules_cache_time = 0
        self._cache_ttl = 300  # 5分钟缓存
    
    def _init_service_specific_config(self):
        """初始化服务特定配置"""
        pass
    
    async def _initialize_service_specific(self):
        """服务特定的初始化逻辑"""
        try:
            # 获取数据库配置并修改为使用品牌车系专用库
            db_config = self.config_manager.get_database_config()
            # 覆盖为 MySQL 专用配置（优先环境变量，再读 configs.config.yaml 的 database.mysql.*）
            db_host = self.config_manager._get_env_or_config('DB_HOST', 'database.mysql.host', db_config.host)
            db_port = self.config_manager._get_env_or_config('DB_PORT', 'database.mysql.port', db_config.port)
            db_user = self.config_manager._get_env_or_config('DB_USERNAME', 'database.mysql.username', db_config.username)
            db_pass = self.config_manager._get_env_or_config('DB_PASSWORD', 'database.mysql.password', db_config.password)
            db1 = self.config_manager._get_env_or_config('DB_DATABASE1', 'database.mysql.database1', None)
            if db1:
                db_config.database = db1
            # 应用覆盖项
            db_config.host = db_host
            db_config.port = int(db_port)
            db_config.username = db_user
            db_config.password = db_pass
            
            # 初始化品牌车系数据库客户端 (voc_ms_be)
            self.mysql_client = MySQLClient(db_config)
            await self.mysql_client.initialize()
            
            # 初始化过滤规则数据库客户端 (voc_model)
            from dataclasses import replace
            filter_db_name = self.config_manager._get_env_or_config('MYSQL_DATABASE', 'database.mysql.database', 'voc_model')
            filter_db_config = replace(db_config, database=filter_db_name)
            
            self.filter_mysql_client = MySQLClient(filter_db_config)
            await self.filter_mysql_client.initialize()
            
            print(f"初始化数据库连接: 品牌车系数据库={db_config.database}, 过滤规则数据库={filter_db_config.database}")
            
            # 从 MySQL 加载品牌车系数据
            await self._load_brand_series_from_mysql()
            
            # 构建同义词缓存
            self._build_synonym_cache()
            
            print(f"brand-attribution服务初始化完成，从MySQL加载了 {len(self.brand_series_cache)} 条品牌数据，{len(self.synonym_map)} 条同义词")
            
        except Exception as e:
            print(f"服务初始化失败: {e}")
            # 降级到硬编码数据
            self._load_hardcoded_brand_data()
            self._build_synonym_cache()
            print(f"降级使用硬编码数据: {len(self.brand_series_cache)} 条品牌数据")
    
    def _build_synonym_cache(self):
        """构建同义词缓存"""
        try:
            from brand_series_dict import get_brand_series_synonyms
            self.synonym_map = get_brand_series_synonyms()
        except Exception as e:
            print(f"构建同义词缓存失败: {e}")
            self.synonym_map = {}
    
    async def _load_brand_series_from_mysql(self):
        """从 MySQL 加载品牌车系数据到内存缓存"""
        # 测试开关：如果是测试模式，直接使用硬编码数据
        if self.is_test:
            print("测试模式：使用硬编码品牌数据")
            self._load_hardcoded_brand_data()
            return
            
        try:
            # 查询 MySQL 中的品牌车系数据
            # 根据 seemysql.py 和 数据库信息.txt，使用 ins_brand_info 和 ins_car_series_info 表
            sql = """
            SELECT 
                b.name as brand,
                b.code as brand_code,
                s.name as series,
                s.code as series_code,
                b.alias as brand_alias,
                s.alias as series_alias
            FROM ins_brand_info b
            LEFT JOIN ins_car_series_info s ON b.id = s.brand_id
            WHERE b.del_flag != 1  # 只加载活跃的品牌数据
            ORDER BY b.name, s.name
            """
            
            results = await self.mysql_client.execute_query(sql)
            
            for row in results:
                brand = row.get('brand', '')
                series = row.get('series', '')
                brand_code = row.get('brand_code', '')
                series_code = row.get('series_code', '')
                brand_alias = row.get('brand_alias', '')
                series_alias = row.get('series_alias', '')
                
                if not brand:
                    continue
                
                key = f"{brand}#{series}" if series else brand
                
                if key not in self.brand_series_cache:
                    self.brand_series_cache[key] = {
                        'brand': brand,
                        'series': series or '',
                        'models': [],
                        'synonyms': []
                    }
                
                # 添加品牌别名作为同义词
                if brand_alias:
                    if isinstance(brand_alias, str):
                        # 如果别名是字符串，按逗号分割
                        alias_list = [alias.strip() for alias in brand_alias.split(',') if alias.strip()]
                        for alias in alias_list:
                            if alias and alias not in self.brand_series_cache[key]['synonyms']:
                                self.brand_series_cache[key]['synonyms'].append(alias)
                
                # 添加车系别名作为同义词
                if series_alias:
                    if isinstance(series_alias, str):
                        # 如果别名是字符串，按逗号分割
                        alias_list = [alias.strip() for alias in series_alias.split(',') if alias.strip()]
                        for alias in alias_list:
                            if alias and alias not in self.brand_series_cache[key]['synonyms']:
                                self.brand_series_cache[key]['synonyms'].append(alias)
            
            print(f"从 MySQL 加载品牌车系数据完成，共 {len(self.brand_series_cache)} 条记录")
            
            # 如果 MySQL 中没有数据，使用备用硬编码数据
            if len(self.brand_series_cache) == 0:
                print("MySQL 中没有品牌数据，使用备用硬编码数据")
                self._load_hardcoded_brand_data()
            
        except Exception as e:
            print(f"从 MySQL 加载品牌车系数据失败: {e}")
            # 如果 MySQL 加载失败，降级到硬编码数据
            print("降级使用硬编码品牌数据")
            self._load_hardcoded_brand_data()
    
    def _load_hardcoded_brand_data(self):
        """加载备用品牌车系数据（当MySQL不可用时）"""
        try:
            # 使用brand_series_dict.py中的完整数据
            all_brands = get_all_brand_series()
            synonyms_map = get_brand_series_synonyms()
            
            for brand, series_list in all_brands.items():
                for series in series_list:
                    key = f"{brand}#{series}"
                    self.brand_series_cache[key] = {
                        'brand': brand,
                        'series': series,
                        'models': [],
                        'synonyms': []
                    }
                    
                    # 添加品牌和车系的同义词
                    brand_synonyms = [k for k, v in synonyms_map.items() if v == brand]
                    series_synonyms = [k for k, v in synonyms_map.items() if v == series]
                    
                    self.brand_series_cache[key]['synonyms'].extend(brand_synonyms + series_synonyms)
            
            print(f"加载备用品牌数据完成，共 {len(self.brand_series_cache)} 条记录")
            
        except Exception as e:
            print(f"加载备用品牌数据失败: {e}")
            # 如果导入失败，使用最简化的数据
            self._load_minimal_fallback_data()
    
    def _load_minimal_fallback_data(self):
        """最小化的备用数据（当brand_series_dict.py也无法加载时）"""
        minimal_data = {
            "长安": ["CS75", "CS55", "逸动"],
            "比亚迪": ["汉", "秦", "宋"],
            "吉利": ["博越", "帝豪"]
        }
        
        for brand, series_list in minimal_data.items():
            for series in series_list:
                key = f"{brand}#{series}"
                self.brand_series_cache[key] = {
                    'brand': brand,
                    'series': series,
                    'models': [],
                    'synonyms': []
                }
        
        print(f"使用最小化品牌数据，共 {len(self.brand_series_cache)} 条记录")
    
    async def _load_filter_rules(self) -> Dict[str, List[Dict[str, Any]]]:
        """加载过滤规则，带缓存机制"""
        import time
        current_time = time.time()
        
        # 检查缓存是否有效
        if (self._filter_rules_cache is not None and 
            current_time - self._filter_rules_cache_time < self._cache_ttl):
            return self._filter_rules_cache
        
        try:
            # 从数据库加载过滤规则
            sql = """
            SELECT rule_type, brand_name, series_name, pattern, 
                   required_context, blacklist_context, priority, description
            FROM brand_series_filter_rules 
            WHERE status = 1 
            ORDER BY priority ASC, id ASC
            """
            
            results = await self.filter_mysql_client.execute_query(sql)
            
            # 按规则类型分组
            rules = {
                'global_blacklist': [],
                'context_rule': [],
                'ambiguous_pattern': []
            }
            
            for row in results:
                rule_type = row.get('rule_type', '')
                if rule_type in rules:
                    rules[rule_type].append({
                        'brand_name': row.get('brand_name', ''),
                        'series_name': row.get('series_name', ''),
                        'pattern': row.get('pattern', ''),
                        'required_context': row.get('required_context', ''),
                        'blacklist_context': row.get('blacklist_context', ''),
                        'priority': row.get('priority', 100),
                        'description': row.get('description', '')
                    })
            
            # 更新缓存
            self._filter_rules_cache = rules
            self._filter_rules_cache_time = current_time
            
            print(f"加载过滤规则成功: 全局黑名单{len(rules['global_blacklist'])}条, "
                           f"上下文规则{len(rules['context_rule'])}条, "
                           f"歧义模式{len(rules['ambiguous_pattern'])}条")
            
            return rules
            
        except Exception as e:
            print(f"加载过滤规则失败: {e}")
            # 返回空规则集
            return {'global_blacklist': [], 'context_rule': [], 'ambiguous_pattern': []}
    
    def _check_context_match(self, text: str, pattern: str, start_pos: int, 
                           required_context: str = '', blacklist_context: str = '') -> bool:
        """检查上下文匹配"""
        # 获取匹配位置前后10个字符的上下文
        context_range = 10
        text_len = len(text)
        context_start = max(0, start_pos - context_range)
        context_end = min(text_len, start_pos + len(pattern) + context_range)
        context = text[context_start:context_end].lower()
        
        # 检查黑名单上下文
        if blacklist_context:
            blacklist_words = [word.strip().lower() for word in blacklist_context.split(',') if word.strip()]
            for blacklist_word in blacklist_words:
                if blacklist_word in context:
                    return False  # 命中黑名单，过滤掉
        
        # 检查必需上下文
        if required_context:
            required_words = [word.strip().lower() for word in required_context.split(',') if word.strip()]
            for required_word in required_words:
                if required_word in context:
                    return True  # 找到必需上下文，保留
            return False  # 没有找到必需上下文，过滤掉
        
        return True  # 没有上下文要求，保留
    
    async def _smart_filter_brand_mentions(self, mentions: List[Dict[str, Any]], 
                                         text: str) -> List[Dict[str, Any]]:
        """智能过滤品牌提及结果"""
        if not mentions:
            return mentions
        
        # 如果过滤规则数据库不可用，直接返回原结果
        if not self.filter_mysql_client:
            return mentions
        
        # 加载过滤规则
        filter_rules = await self._load_filter_rules()
        text_lower = text.lower()
        
        filtered_mentions = []
        
        for mention in mentions:
            brand = mention.get('brand', '')
            series = mention.get('series', '')
            should_keep = True
            filter_reason = ""
            
            # 1. 全局黑名单检查
            for rule in filter_rules['global_blacklist']:
                pattern = rule['pattern'].lower()
                if pattern in text_lower:
                    should_keep = False
                    filter_reason = f"全局黑名单: {rule['pattern']}"
                    break
            
            if not should_keep:
                print(f"过滤品牌提及 {brand}-{series}: {filter_reason}")
                continue
            
            # 2. 上下文规则检查
            for rule in filter_rules['context_rule']:
                rule_brand = rule['brand_name']
                rule_series = rule['series_name']
                pattern = rule['pattern'].lower()
                
                # 检查是否匹配当前品牌车系
                if ((rule_brand and brand.lower() == rule_brand.lower()) or 
                    (rule_series and series.lower() == rule_series.lower()) or
                    (not rule_brand and not rule_series and pattern in text_lower)):
                    
                    # 查找匹配位置
                    match_pos = text_lower.find(pattern)
                    if match_pos >= 0:
                        # 检查上下文
                        if not self._check_context_match(text_lower, pattern, match_pos,
                                                       rule['required_context'], 
                                                       rule['blacklist_context']):
                            should_keep = False
                            filter_reason = f"上下文规则: {rule['description']}"
                            break
            
            if not should_keep:
                print(f"过滤品牌提及 {brand}-{series}: {filter_reason}")
                continue
            
            # 3. 歧义模式检查
            for rule in filter_rules['ambiguous_pattern']:
                pattern = rule['pattern'].lower()
                if pattern in text_lower:
                    # 检查黑名单上下文
                    match_pos = text_lower.find(pattern)
                    if match_pos >= 0:
                        if not self._check_context_match(text_lower, pattern, match_pos,
                                                       '', rule['blacklist_context']):
                            should_keep = False
                            filter_reason = f"歧义模式: {rule['description']}"
                            break
            
            if should_keep:
                filtered_mentions.append(mention)
            else:
                print(f"过滤品牌提及 {brand}-{series}: {filter_reason}")
        
        original_count = len(mentions)
        filtered_count = len(filtered_mentions)
        
        if original_count != filtered_count:
            print(f"智能过滤: {original_count} → {filtered_count} "
                           f"(过滤了{original_count - filtered_count}个误匹配)")
        
        return filtered_mentions
    
    def _chinese_boundary_match(self, text: str, pattern: str):
        """中文友好的边界匹配"""
        import re
        
        # 中文标点符号和边界字符
        chinese_boundary_chars = '，。！？；：、”“‘’（）【】《》〈〉{}[]()\s'
        
        # 构建边界匹配模式
        escaped_pattern = re.escape(pattern)
        
        # 匹配模式在开头
        start_pattern = f'^{escaped_pattern}(?=[{chinese_boundary_chars}]|$)'
        
        # 匹配模式在中间
        middle_pattern = f'(?<=[{chinese_boundary_chars}]){escaped_pattern}(?=[{chinese_boundary_chars}]|$)'
        
        # 匹配模式在结尾
        end_pattern = f'(?<=[{chinese_boundary_chars}]){escaped_pattern}$'
        
        # 如果边界匹配失败，尝试简单匹配（优先使用简单匹配）
        simple_match = re.search(escaped_pattern, text)
        if simple_match:
            return simple_match
            
        return None
    
    async def _extract_brand_mentions(self, text: str) -> List[Dict[str, Any]]:
        """从文本中提取品牌车系提及 - 修复重叠匹配问题"""
        import re
        mentions = []
        text_lower = text.lower()
        
        # 按照车系名称长度排序，优先匹配最长的
        sorted_cache = sorted(
            self.brand_series_cache.items(),
            key=lambda x: len(x[1]['series']) if x[1]['series'] else 0,
            reverse=True  # 按长度降序
        )
        
        # 记录已匹配的文本区域，避免重叠
        matched_ranges = []  # [(start, end), ...]
        
        def is_range_overlapping(start, end):
            """检查是否与已匹配区域重叠"""
            for m_start, m_end in matched_ranges:
                if not (end <= m_start or start >= m_end):  # 有重叠
                    return True
            return False
        
        for key, data in sorted_cache:
            brand = data['brand']
            series = data['series']
            
            # 优先匹配车系（更精确）
            if series:
                series_match = self._chinese_boundary_match(text_lower, series.lower())
                if series_match:
                    start_pos = series_match.start()
                    end_pos = series_match.end()
                    
                    # 检查是否与已匹配区域重叠
                    if not is_range_overlapping(start_pos, end_pos):
                        matched_ranges.append((start_pos, end_pos))
                        mentions.append({
                            'brand': brand,
                            'series': series,
                            'type': 'series',
                            'confidence': 0.95,
                            'position': start_pos,
                            'match_type': 'exact'
                        })
            
            # 在没有车系匹配的位置才匹配品牌
            brand_match = self._chinese_boundary_match(text_lower, brand.lower())
            if brand_match:
                start_pos = brand_match.start()
                end_pos = brand_match.end()
                
                # 检查是否与已匹配区域重叠（包括车系匹配）
                if not is_range_overlapping(start_pos, end_pos):
                    # 额外检查：确保品牌匹配不被车系匹配包含
                    is_contained_in_series = any(
                        m['type'] == 'series' and 
                        m['brand'] == brand and
                        start_pos >= m['position'] and 
                        end_pos <= m['position'] + len(m.get('series', ''))
                        for m in mentions
                    )
                    
                    if not is_contained_in_series:
                        matched_ranges.append((start_pos, end_pos))
                        mentions.append({
                            'brand': brand,
                            'series': '',  # 清空车系，表示只找到品牌
                            'type': 'brand',
                            'confidence': 0.9,
                            'position': start_pos,
                            'match_type': 'exact'
                        })
            
            # 检查近义词（使用中文友好的边界匹配）
            for synonym in data['synonyms']:
                synonym_match = self._chinese_boundary_match(text_lower, synonym.lower())
                if synonym_match:
                    start_pos = synonym_match.start()
                    end_pos = synonym_match.end()
                    
                    # 检查是否为歧义词
                    if not self._is_ambiguous_term(synonym, text_lower, start_pos):
                        # 检查是否与已匹配区域重叠
                        if not is_range_overlapping(start_pos, end_pos):
                            matched_ranges.append((start_pos, end_pos))
                            mentions.append({
                                'brand': brand,
                                'series': series,
                                'type': 'synonym',
                                'confidence': 0.85,
                                'position': start_pos,
                                'match_type': 'synonym'
                            })
        
        # 按文本中出现的位置排序
        mentions_sorted = sorted(mentions, key=lambda x: x.get('position', 0))
        
        # 应用智能过滤
        try:
            filtered_mentions = await self._smart_filter_brand_mentions(mentions_sorted, text)
            return filtered_mentions
        except Exception as e:
            print(f"智能过滤失败，使用原始结果: {e}")
            return mentions_sorted
    
    def _is_ambiguous_term(self, term: str, text: str, position: int) -> bool:
        """检查是否为歧义词"""
        try:
            from brand_series_dict import get_ambiguous_filter
            ambiguous_config = get_ambiguous_filter()
            
            # 检查是否在歧义过滤列表中
            remove_patterns = ambiguous_config.get('remove_patterns', [])
            if term.lower() in [p.lower() for p in remove_patterns]:
                # 检查是否有上下文确认
                brand_context = ambiguous_config.get('brand_context', {})
                if term in brand_context:
                    # 检查上下文是否匹配预期品牌
                    expected_brands = brand_context[term]
                    context_window = text[max(0, position-50):min(len(text), position+50)]
                    
                    # 如果上下文中没有预期的品牌，则认为是歧义
                    has_expected_brand = any(brand.lower() in context_window for brand in expected_brands)
                    return not has_expected_brand
                return True
            return False
        except Exception:
            # 如果获取歧义配置失败，保守处理
            return False
    
    def _resolve_coreferences(self, topic_text: str, entity_data: Dict, mentions: List[Dict]) -> Optional[Dict]:
        """增强版指代消解算法 - 支持多种指代模式和上下文分析"""
        
        # 1. 直接从实体数据获取精确位置
        entity_start = entity_data.get('start', -1)
        entity_end = entity_data.get('end', -1)
        
        if entity_start == -1 or entity_end == -1:
            return None  # 没有位置信息，无法进行精确指代消解
        
        # 2. 扩展的中文指代词分类
        pronoun_patterns = {
            'direct': ['它', '其', '该车', '这款', '这个', '这辆', '此车'],  # 直接指代
            'comparative': ['前者', '后者', '上述', '以上', '上面的', '前面的'],  # 比较指代
            'possessive': ['它的', '其的', '该车的', '这款车的', '这个的'],  # 所有格指代
            'contextual': ['同样', '类似', '相同', '一样', '如此', '这样'],  # 上下文指代
            'demonstrative': ['这种', '那种', '此类', '该类', '这类']  # 指示指代
        }
        
        # 3. 多模式指代检测
        detected_patterns = []
        search_window = 50  # 扩大搜索窗口
        search_start = max(0, entity_start - search_window)
        search_end = min(len(topic_text), entity_end + search_window)
        
        context_before = topic_text[search_start:entity_start]
        context_after = topic_text[entity_end:search_end]
        full_context = topic_text[search_start:search_end]
        
        # 检测各种指代模式
        for pattern_type, pronouns in pronoun_patterns.items():
            for pronoun in pronouns:
                if pronoun in context_before:
                    detected_patterns.append({
                        'type': pattern_type,
                        'pronoun': pronoun,
                        'position': context_before.rfind(pronoun) + search_start,
                        'confidence': self._get_pronoun_confidence(pattern_type)
                    })
        
        if not detected_patterns:
            return None  # 没有指代词，不需要指代消解
        
        print(f"调试: 检测到指代模式: {[(p['type'], p['pronoun']) for p in detected_patterns]}")
        
        # 4. 基于指代模式选择最佳候选
        best_pattern = max(detected_patterns, key=lambda x: x['confidence'])
        
        # 5. 根据指代类型采用不同的消解策略
        if best_pattern['type'] in ['direct', 'possessive', 'demonstrative']:
            # 直接指代 - 查找最近的品牌提及
            candidate = self._find_closest_mention_before_position(topic_text, mentions, entity_start)
        elif best_pattern['type'] == 'comparative':
            # 比较指代 - 需要特殊处理前者/后者
            candidate = self._resolve_comparative_reference(topic_text, entity_start, mentions, best_pattern)
        elif best_pattern['type'] == 'contextual':
            # 上下文指代 - 查找语义相似的提及
            candidate = self._resolve_contextual_reference(topic_text, entity_data, mentions, full_context)
        else:
            candidate = self._find_closest_mention_before_position(topic_text, mentions, entity_start)
        
        # 6. 验证候选结果的合理性
        if candidate:
            validation_score = self._validate_coreference_candidate(
                topic_text, entity_data, candidate, best_pattern, full_context
            )
            
            if validation_score > 0.5:  # 只有验证通过才返回结果
                candidate['coreference_confidence'] = validation_score
                candidate['coreference_pattern'] = best_pattern
                print(f"调试: 指代消解成功 - {candidate.get('brand', '')}, 验证分数: {validation_score:.3f}")
                return candidate
            else:
                print(f"调试: 指代消解候选未通过验证 - {candidate.get('brand', '')}, 验证分数: {validation_score:.3f}")
        
        return None
    
    def _get_pronoun_confidence(self, pattern_type: str) -> float:
        """根据指代模式返回置信度"""
        confidence_map = {
            'direct': 0.9,        # 直接指代置信度最高
            'possessive': 0.85,   # 所有格指代
            'demonstrative': 0.8, # 指示指代
            'comparative': 0.75,  # 比较指代
            'contextual': 0.7     # 上下文指代
        }
        return confidence_map.get(pattern_type, 0.6)
    
    def _resolve_comparative_reference(self, topic_text: str, entity_start: int, 
                                     mentions: List[Dict], pattern: Dict) -> Optional[Dict]:
        """处理比较指代（前者/后者）"""
        pronoun = pattern['pronoun']
        
        # 查找实体前的所有品牌提及
        mentions_before = [m for m in mentions if m.get('position', 0) < entity_start]
        if not mentions_before:
            return None
        
        # 按位置排序
        mentions_before_sorted = sorted(mentions_before, key=lambda x: x.get('position', 0))
        
        if pronoun in ['前者', '上述', '以上', '上面的', '前面的']:
            # 前者 - 返回较早出现的
            return mentions_before_sorted[0] if mentions_before_sorted else None
        elif pronoun in ['后者']:
            # 后者 - 返回较晚出现的
            return mentions_before_sorted[-1] if mentions_before_sorted else None
        
        return None
    
    def _resolve_contextual_reference(self, topic_text: str, entity_data: Dict, 
                                    mentions: List[Dict], context: str) -> Optional[Dict]:
        """处理上下文指代"""
        # 分析上下文中的语义信息
        context_lower = context.lower()
        entity_text = entity_data.get('text', '').lower()
        
        # 查找与实体语义相关的品牌提及
        semantic_candidates = []
        
        for mention in mentions:
            # 计算语义相关性（简化版本）
            brand = mention.get('brand', '').lower()
            series = mention.get('series', '').lower() if mention.get('series') else ''
            
            # 检查品牌或车系是否与实体有语义关联
            semantic_score = 0.0
            
            # 如果实体文本包含品牌相关词汇
            if brand in entity_text or (series and series in entity_text):
                semantic_score += 0.5
            
            # 如果上下文中有相关描述
            brand_related_words = [brand, series] + mention.get('synonyms', [])
            context_matches = sum(1 for word in brand_related_words if word and word in context_lower)
            semantic_score += context_matches * 0.2
            
            if semantic_score > 0.1:
                mention_copy = mention.copy()
                mention_copy['semantic_score'] = semantic_score
                semantic_candidates.append(mention_copy)
        
        # 返回语义得分最高的候选
        if semantic_candidates:
            return max(semantic_candidates, key=lambda x: x.get('semantic_score', 0))
        
        return None
    
    def _validate_coreference_candidate(self, topic_text: str, entity_data: Dict, 
                                      candidate: Dict, pattern: Dict, context: str) -> float:
        """验证指代消解候选的合理性"""
        validation_score = 0.0
        
        # 1. 基础置信度
        validation_score += candidate.get('confidence', 0) * 0.3
        
        # 2. 指代模式置信度
        validation_score += pattern.get('confidence', 0) * 0.2
        
        # 3. 距离因子 - 距离越近越可信
        entity_start = entity_data.get('start', 0)
        candidate_pos = candidate.get('position', 0)
        distance = abs(entity_start - candidate_pos)
        distance_score = 1.0 / (1.0 + distance * 0.005)  # 距离因子
        validation_score += distance_score * 0.2
        
        # 4. 上下文一致性检查
        context_lower = context.lower()
        brand = candidate.get('brand', '').lower()
        series = candidate.get('series', '').lower() if candidate.get('series') else ''
        
        # 检查上下文中是否有冲突信息
        conflict_indicators = ['不是', '而不是', '非', '除了']
        has_conflict = any(indicator in context_lower for indicator in conflict_indicators)
        
        if has_conflict:
            # 检查冲突是否针对当前候选
            brand_after_conflict = any(
                brand in context_lower[context_lower.find(indicator):] 
                for indicator in conflict_indicators 
                if indicator in context_lower
            )
            if brand_after_conflict:
                validation_score *= 0.3  # 有冲突大幅降低分数
        
        # 5. 语法一致性检查（简化版）
        entity_text = entity_data.get('text', '').lower()
        
        # 检查实体类型与品牌类型的一致性
        car_related_words = ['车', '汽车', '轿车', 'suv', '越野车', '跑车', '车型', '车系']
        entity_is_car_related = any(word in entity_text for word in car_related_words)
        
        if entity_is_car_related:
            validation_score += 0.1  # 实体与汽车相关，增加分数
        
        return min(validation_score, 1.0)  # 确保分数不超过1.0
    
    def _calculate_domain_analysis_confidence(self, domain_mention: Dict, default_attribution: Dict, 
                                            topic_text: str, entity_data: Dict) -> float:
        """计算影响域分析的综合置信度"""
        base_confidence = domain_mention.get('weighted_confidence', domain_mention.get('confidence', 0.8))
        
        # 1. 语义权重因子
        semantic_weight = domain_mention.get('semantic_weight', 0.8)
        
        # 2. 品牌一致性因子
        consistency_factor = 1.0
        if default_attribution:
            if domain_mention['brand'] == default_attribution['brand']:
                consistency_factor = 1.1  # 与标题一致，增加置信度
            else:
                consistency_factor = 0.9  # 与标题不一致，略微降低置信度
        
        # 3. 实体类型匹配因子
        entity_text = entity_data.get('text', '').lower()
        entity_match_factor = self._calculate_entity_match_factor(entity_text, domain_mention)
        
        # 4. 上下文复杂度因子
        context_complexity = self._calculate_context_complexity(topic_text, entity_data)
        
        # 综合计算置信度
        final_confidence = (base_confidence * semantic_weight * consistency_factor * 
                          entity_match_factor * context_complexity)
        
        return min(final_confidence, 0.98)  # 限制最高置信度
    
    def _resolve_attribution_conflict(self, domain_mention: Dict, default_attribution: Dict, 
                                    topic_text: str, entity_data: Dict) -> Dict:
        """解决归属冲突的智能策略"""
        
        # 1. 分析文本场景类型
        scene_type = self._analyze_text_scene_type(topic_text)
        
        # 2. 计算两个候选的综合得分
        domain_score = self._calculate_candidate_score(domain_mention, topic_text, entity_data, 'domain')
        title_score = self._calculate_candidate_score(default_attribution, topic_text, entity_data, 'title')
        
        print(f"调试: 冲突解决 - 影响域得分: {domain_score:.3f}, 标题得分: {title_score:.3f}, 场景类型: {scene_type}")
        
        # 3. 根据场景类型调整权重
        if scene_type == 'comparison':
            # 对比场景：优先影响域分析
            domain_score *= 1.2
            title_score *= 0.8
        elif scene_type == 'review':
            # 评测场景：平衡考虑
            domain_score *= 1.0
            title_score *= 1.0
        elif scene_type == 'news':
            # 新闻场景：优先标题信息
            domain_score *= 0.9
            title_score *= 1.1
        
        # 4. 选择得分更高的候选
        if domain_score > title_score:
            confidence = min(domain_score, 0.95)
            reason = f"冲突解决：影响域分析优于标题信息 ({scene_type}场景, 得分差: {domain_score - title_score:.3f})"
            return {
                'brand': domain_mention['brand'],
                'series': domain_mention['series'],
                'confidence': confidence,
                'reason': reason
            }
        else:
            confidence = min(title_score, 0.90)
            reason = f"冲突解决：标题信息优于影响域分析 ({scene_type}场景, 得分差: {title_score - domain_score:.3f})"
            return {
                'brand': default_attribution['brand'],
                'series': default_attribution['series'],
                'confidence': confidence,
                'reason': reason
            }
    
    def _calculate_entity_match_factor(self, entity_text: str, mention: Dict) -> float:
        """计算实体与品牌提及的匹配因子"""
        brand = mention.get('brand', '').lower()
        series = mention.get('series', '').lower() if mention.get('series') else ''
        
        match_factor = 1.0
        
        # 直接包含品牌名
        if brand in entity_text:
            match_factor += 0.2
        
        # 直接包含车系名
        if series and series in entity_text:
            match_factor += 0.3
        
        # 包含汽车相关词汇
        car_words = ['车', '汽车', '轿车', 'suv', '越野车', '跑车', '车型', '车系', '座驾']
        if any(word in entity_text for word in car_words):
            match_factor += 0.1
        
        return min(match_factor, 1.3)
    
    def _calculate_context_complexity(self, topic_text: str, entity_data: Dict) -> float:
        """计算上下文复杂度因子"""
        text_length = len(topic_text)
        entity_pos = entity_data.get('start', 0)
        
        # 基础复杂度
        complexity_factor = 1.0
        
        # 文本长度因子
        if text_length > 500:
            complexity_factor *= 0.95  # 长文本略微降低置信度
        elif text_length < 100:
            complexity_factor *= 1.05  # 短文本略微提高置信度
        
        # 实体位置因子
        position_ratio = entity_pos / max(text_length, 1)
        if 0.1 < position_ratio < 0.9:
            complexity_factor *= 1.02  # 实体在中间位置，上下文更充分
        
        # 标点符号密度（表示句子复杂度）
        punctuation_count = sum(1 for char in topic_text if char in '，。！？；：')
        punctuation_density = punctuation_count / max(text_length, 1)
        
        if punctuation_density > 0.05:
            complexity_factor *= 0.98  # 高标点密度，句子复杂，略微降低置信度
        
        return complexity_factor
    
    def _analyze_text_scene_type(self, topic_text: str) -> str:
        """分析文本场景类型"""
        text_lower = topic_text.lower()
        
        # 对比场景关键词
        comparison_keywords = ['对比', '比较', '相比', '不如', '优于', '胜过', '比', 'vs', '对决']
        if any(keyword in text_lower for keyword in comparison_keywords):
            return 'comparison'
        
        # 评测场景关键词
        review_keywords = ['评测', '测试', '试驾', '体验', '感受', '印象', '评价', '测评']
        if any(keyword in text_lower for keyword in review_keywords):
            return 'review'
        
        # 新闻场景关键词
        news_keywords = ['发布', '上市', '推出', '官方', '消息', '报道', '据悉', '宣布']
        if any(keyword in text_lower for keyword in news_keywords):
            return 'news'
        
        # 技术场景关键词
        tech_keywords = ['参数', '配置', '性能', '动力', '油耗', '续航', '马力', '扭矩']
        if any(keyword in text_lower for keyword in tech_keywords):
            return 'technical'
        
        return 'general'
    
    def _calculate_candidate_score(self, candidate: Dict, topic_text: str, 
                                 entity_data: Dict, candidate_type: str) -> float:
        """计算候选归属的综合得分"""
        base_score = candidate.get('confidence', 0.7)
        
        # 1. 候选类型权重
        type_weights = {
            'domain': 1.0,  # 影响域分析
            'title': 0.9,   # 标题分析
            'coreference': 0.95,  # 指代消解
            'sentence': 0.85,     # 同句分析
            'distance': 0.8       # 距离分析
        }
        
        type_weight = type_weights.get(candidate_type, 0.7)
        
        # 2. 语义权重（如果有）
        semantic_weight = candidate.get('semantic_weight', 1.0)
        
        # 3. 品牌车系完整性
        completeness_score = 1.0
        if candidate.get('series') and candidate.get('series') != '无车系':
            completeness_score = 1.2  # 有具体车系，得分更高
        
        # 4. 上下文相关性
        context_relevance = self._calculate_context_relevance(candidate, topic_text, entity_data)
        
        # 综合得分
        final_score = base_score * type_weight * semantic_weight * completeness_score * context_relevance
        
        return min(final_score, 1.0)
    
    def _calculate_context_relevance(self, candidate: Dict, topic_text: str, entity_data: Dict) -> float:
        """计算候选与上下文的相关性"""
        brand = candidate.get('brand', '').lower()
        series = candidate.get('series', '').lower() if candidate.get('series') else ''
        text_lower = topic_text.lower()
        
        relevance_score = 1.0
        
        # 1. 品牌在文本中的出现频率
        brand_count = text_lower.count(brand)
        if brand_count > 1:
            relevance_score += 0.1 * min(brand_count - 1, 3)  # 最多加0.3
        
        # 2. 车系在文本中的出现频率
        if series:
            series_count = text_lower.count(series)
            if series_count > 1:
                relevance_score += 0.15 * min(series_count - 1, 2)  # 最多加0.3
        
        # 3. 相关词汇密度
        related_words = [brand, series] + candidate.get('synonyms', [])
        related_count = sum(1 for word in related_words if word and word in text_lower)
        if related_count > 2:
            relevance_score += 0.05 * min(related_count - 2, 4)  # 最多加0.2
        
        return min(relevance_score, 1.4)
    
    def _find_closest_mention_before_position(self, text: str, mentions: List[Dict], target_pos: int) -> Optional[Dict]:
        """在指定位置前查找最近的品牌提及（基于精确位置）"""
        
        # 过滤出在目标位置之前的提及
        mentions_before = [m for m in mentions if m.get('position', 0) < target_pos]
        if not mentions_before:
            return None
        
        # 按位置排序（从近到远）
        mentions_before_sorted = sorted(mentions_before, key=lambda x: x.get('position', 0), reverse=True)
        
        closest_mention = None
        min_distance = float('inf')
        
        for mention in mentions_before_sorted:
            mention_pos = mention.get('position', 0)
            distance = target_pos - mention_pos
            
            # 检查是否遇到其他品牌（智能停止条件）
            if closest_mention and distance < min_distance:
                # 检查两个提及之间是否有其他品牌
                between_start = mention_pos
                between_end = closest_mention.get('position', mention_pos)
                between_text = text[between_start:between_end]
                
                # 检查中间文本是否包含其他品牌提及
                other_brands = [m for m in mentions
                              if m != mention and m != closest_mention
                              and mention_pos < m.get('position', 0) < between_end]
                
                if other_brands:
                    break  # 遇到其他品牌，停止搜索
            
            if distance < min_distance:
                min_distance = distance
                closest_mention = mention
        
        return closest_mention
    
    def _find_closest_mention_with_context(self, segment: str, entity: str, mentions: List[Dict]) -> Optional[Dict]:
        """查找距离实体最近的品牌提及（考虑语义边界）"""
        import re
        
        segment_lower = segment.lower()
        entity_pos = segment_lower.find(entity.lower())
        
        if entity_pos == -1:
            return None
        
        closest_mention = None
        min_semantic_distance = float('inf')
        
        for mention in mentions:
            brand = mention['brand'].lower()
            series = mention['series'].lower() if mention['series'] else ''
            
            # 查找品牌位置
            brand_pos = segment_lower.find(brand)
            if brand_pos != -1:
                # 计算语义距离（考虑句子边界）
                distance = self._calculate_semantic_distance(segment, entity_pos, brand_pos)
                if distance < min_semantic_distance:
                    min_semantic_distance = distance
                    closest_mention = mention
                    closest_mention['distance_score'] = 1.0 - (distance / len(segment))
            
            # 查找车系位置
            if series:
                series_pos = segment_lower.find(series)
                if series_pos != -1:
                    distance = self._calculate_semantic_distance(segment, entity_pos, series_pos)
                    if distance < min_semantic_distance:
                        min_semantic_distance = distance
                        closest_mention = mention
                        closest_mention['distance_score'] = 1.0 - (distance / len(segment))
        
        return closest_mention
    
    def _calculate_semantic_distance(self, text: str, pos1: int, pos2: int) -> float:
        """计算语义距离（考虑句子边界）"""
        import re
        
        # 基础字符距离
        char_distance = abs(pos1 - pos2)
        
        # 查找句子边界
        sentences = re.split(r'[。！？；;\n]', text)
        
        # 确定两个位置所在的句子索引
        sentence_idx1 = -1
        sentence_idx2 = -1
        current_pos = 0
        
        for i, sentence in enumerate(sentences):
            sentence_end = current_pos + len(sentence)
            if current_pos <= pos1 < sentence_end:
                sentence_idx1 = i
            if current_pos <= pos2 < sentence_end:
                sentence_idx2 = i
            current_pos = sentence_end + 1  # +1 for the punctuation
        
        # 如果不在同一个句子，增加距离惩罚
        sentence_penalty = 0 if sentence_idx1 == sentence_idx2 else 100
        
        return char_distance + sentence_penalty
    
    def _analyze_influence_domain(self, text: str, entity_pos: int, mentions: List[Dict]) -> Optional[Dict]:
        """增强版影响域分析 - 支持语义权重和多层次边界检测"""
        try:
            if not mentions:
                print(f"调试: 影响域分析 - 没有品牌提及")
                return None
            
            # 添加边界检查
            if entity_pos < 0 or entity_pos >= len(text):
                print(f"警告: entity_pos {entity_pos} 超出文本范围 (text 长度: {len(text)})")
                return None
                
            print(f"调试: 影响域分析 - text: '{text}', entity_pos: {entity_pos}, text长度: {len(text)}")
                
            # 多层次边界定义
            strong_boundaries = ['。', '！', '？', '\n']  # 强边界（句子级别）
            medium_boundaries = ['；', '：', '但是', '不过', '然而']  # 中等边界（子句级别）
            weak_boundaries = ['，', '、', '和', '与', '及']  # 弱边界（短语级别）
            comparison_boundaries = ['比', '相比', '对比', '相较于', '不如']  # 对比边界
            
            # 查找实体影响域边界，考虑边界强度
            influence_start, start_boundary_type = self._find_boundary_backward(
                text, entity_pos, strong_boundaries, medium_boundaries, weak_boundaries, comparison_boundaries)
            influence_end, end_boundary_type = self._find_boundary_forward(
                text, entity_pos, strong_boundaries, medium_boundaries, weak_boundaries, comparison_boundaries)
            
            print(f"调试: 影响域范围 - start: {influence_start} ({start_boundary_type}), end: {influence_end} ({end_boundary_type})")
            
            # 在影响域内查找品牌提及并计算语义权重
            domain_mentions = []
            seen_brand_series = set()  # 用于去重
            
            for mention in mentions:
                mention_pos = mention.get('position', -1)
                if mention_pos != -1 and influence_start <= mention_pos <= influence_end:
                    # 创建唯一标识
                    brand_series_key = f"{mention['brand']}:{mention['series']}"
                    if brand_series_key not in seen_brand_series:
                        seen_brand_series.add(brand_series_key)
                        
                        # 计算语义权重
                        semantic_weight = self._calculate_semantic_weight(
                            mention, entity_pos, mention_pos, text, 
                            start_boundary_type, end_boundary_type
                        )
                        
                        # 添加语义权重到提及信息
                        enhanced_mention = mention.copy()
                        enhanced_mention['semantic_weight'] = semantic_weight
                        enhanced_mention['weighted_confidence'] = mention['confidence'] * semantic_weight
                        
                        domain_mentions.append(enhanced_mention)
                        print(f"调试: 找到影响域内品牌提及 - {mention['brand']} at position {mention_pos}, 语义权重: {semantic_weight:.3f}")
            
            if not domain_mentions:
                print(f"调试: 影响域内没有找到品牌提及")
                return None
            
            # 返回加权置信度最高的品牌提及
            best_mention = max(domain_mentions, key=lambda x: x.get('weighted_confidence', 0))
            print(f"调试: 选择最佳品牌提及 - {best_mention['brand']}, 加权置信度: {best_mention['weighted_confidence']:.3f}")
            return best_mention
            
        except Exception as e:
            print(f"错误: 影响域分析异常 - {str(e)}")
            import traceback
            traceback.print_exc()
            return None
    
    def _find_boundary_backward(self, text: str, entity_pos: int, strong_b, medium_b, weak_b, comp_b) -> tuple:
        """向前查找边界，返回位置和边界类型"""
        for i in range(entity_pos - 1, -1, -1):
            if i >= 0 and i < len(text):
                char = text[i]
                # 检查强边界
                if char in strong_b:
                    return i + 1, 'strong'
                # 检查中等边界
                if char in medium_b:
                    return i + 1, 'medium'
                
                # 检查多字符边界词
                for boundary_list, boundary_type in [(medium_b, 'medium'), (comp_b, 'comparison'), (weak_b, 'weak')]:
                    for boundary in boundary_list:
                        if len(boundary) > 1 and i >= len(boundary) - 1:
                            start_idx = i - len(boundary) + 1
                            if start_idx >= 0 and text[start_idx:i+1] == boundary:
                                return i + 1, boundary_type
                
                # 检查弱边界
                if char in weak_b:
                    return i + 1, 'weak'
        
        return 0, 'none'
    
    def _find_boundary_forward(self, text: str, entity_pos: int, strong_b, medium_b, weak_b, comp_b) -> tuple:
        """向后查找边界，返回位置和边界类型"""
        for i in range(entity_pos, len(text)):
            if i >= 0 and i < len(text):
                char = text[i]
                # 检查强边界
                if char in strong_b:
                    return i, 'strong'
                # 检查中等边界
                if char in medium_b:
                    return i, 'medium'
                
                # 检查多字符边界词
                for boundary_list, boundary_type in [(medium_b, 'medium'), (comp_b, 'comparison'), (weak_b, 'weak')]:
                    for boundary in boundary_list:
                        if len(boundary) > 1 and i + len(boundary) <= len(text):
                            if text[i:i+len(boundary)] == boundary:
                                return i, boundary_type
                
                # 检查弱边界
                if char in weak_b:
                    return i, 'weak'
        
        return len(text), 'none'
    
    def _calculate_semantic_weight(self, mention: Dict, entity_pos: int, mention_pos: int, 
                                 text: str, start_boundary_type: str, end_boundary_type: str) -> float:
        """计算语义权重"""
        # 基础权重
        base_weight = 1.0
        
        # 距离惩罚 - 距离越近权重越高
        distance = abs(entity_pos - mention_pos)
        distance_weight = 1.0 / (1.0 + distance * 0.01)
        
        # 边界类型权重
        boundary_weights = {
            'strong': 1.0,    # 句子级别边界，权重最高
            'medium': 0.9,    # 子句级别边界
            'comparison': 0.95, # 对比边界，在对比场景中权重较高
            'weak': 0.8,      # 短语级别边界
            'none': 0.7       # 无明确边界
        }
        
        # 取边界类型权重的平均值
        boundary_weight = (boundary_weights.get(start_boundary_type, 0.7) + 
                          boundary_weights.get(end_boundary_type, 0.7)) / 2
        
        # 上下文语义分析
        context_weight = self._analyze_context_semantics(text, entity_pos, mention_pos)
        
        # 综合权重计算
        final_weight = base_weight * distance_weight * boundary_weight * context_weight
        
        return min(final_weight, 1.0)  # 确保权重不超过1.0
    
    def _analyze_context_semantics(self, text: str, entity_pos: int, mention_pos: int) -> float:
        """分析上下文语义，返回权重因子"""
        start_pos = min(entity_pos, mention_pos)
        end_pos = max(entity_pos, mention_pos)
        context = text[start_pos:end_pos].lower()
        
        # 检测否定词 - 降低权重
        negative_words = ['不是', '不像', '非', '除了', '而非', '不', '没有', '并非']
        has_negative = any(neg in context for neg in negative_words)
        
        # 检测比较词 - 在对比场景中可能需要特殊处理
        comparison_words = ['比', '相比', '对比', '不如', '相较于']
        has_comparison = any(comp in context for comp in comparison_words)
        
        # 检测连接词 - 可能表示并列关系
        conjunction_words = ['和', '与', '及', '以及', '还有']
        has_conjunction = any(conj in context for conj in conjunction_words)
        
        # 计算语义权重
        semantic_weight = 1.0
        
        if has_negative:
            semantic_weight *= 0.3  # 否定词大幅降低权重
        
        if has_comparison:
            semantic_weight *= 0.8  # 比较词适度降低权重
        
        if has_conjunction:
            semantic_weight *= 0.9  # 连接词略微降低权重
        
        return semantic_weight
    
    async def _analyze_context_attribution(self, topic_text: str, segment: str, entity_data: Dict, description: str, mentions: List[Dict], source_data: Dict = None) -> Dict[str, Any]:
        """增强版上下文归属分析 - 支持标题上下文和智能指代消解"""
        # 获取默认标题归属作为上下文参考
        default_attribution = None
        if source_data and source_data.get('title'):
            default_brand_series = await self._extract_default_from_title(source_data.get('title', ''))
            default_attribution = {
                'brand': default_brand_series['brand'],
                'series': default_brand_series['series'],
                'confidence': 0.7,
                'reason': '标题默认归属'
            }
        
        # 如果段落中没有品牌提及，但标题有明确信息，优先使用标题信息
        if not mentions and default_attribution:
            if default_attribution['series'] != '无车系':
                return {
                    'brand': default_attribution['brand'],
                    'series': default_attribution['series'],
                    'model': '',
                    'confidence': 0.8,
                    'reason': f"标题上下文优先，确定归属{default_attribution['brand']}{default_attribution['series']}"
                }
            else:
                return {
                    'brand': default_attribution['brand'],
                    'series': '无车系',
                    'model': '',
                    'confidence': 0.6,
                    'reason': f"标题上下文，确定归属{default_attribution['brand']}"
                }
        
        if not mentions:
            return self._get_default_attribution(source_data)
        
        # 获取实体文本和位置
        entity = entity_data.get('text', '')
        entity_pos = entity_data.get('start', -1)
        segment_lower = segment.lower()
        entity_lower = entity.lower()
        
        # 1. 影响域分析（核心逻辑）- 增强置信度计算
        if entity_pos != -1:
            domain_mention = self._analyze_influence_domain(topic_text, entity_pos, mentions)
            if domain_mention:
                # 计算影响域分析的综合置信度
                domain_confidence = self._calculate_domain_analysis_confidence(
                    domain_mention, default_attribution, topic_text, entity_data
                )
                
                # 如果影响域分析结果与标题默认归属冲突，需要智能判断
                if (default_attribution and 
                    domain_mention['brand'] != default_attribution['brand']):
                    
                    # 冲突解决策略
                    conflict_resolution = self._resolve_attribution_conflict(
                        domain_mention, default_attribution, topic_text, entity_data
                    )
                    
                    return {
                        'brand': conflict_resolution['brand'],
                        'series': conflict_resolution['series'],
                        'model': '',
                        'confidence': conflict_resolution['confidence'],
                        'reason': conflict_resolution['reason']
                    }
                else:
                    return {
                        'brand': domain_mention['brand'],
                        'series': domain_mention['series'],
                        'model': '',
                        'confidence': domain_confidence,
                        'reason': f"影响域分析，确定归属{domain_mention['brand']} (语义权重: {domain_mention.get('semantic_weight', 0):.3f})"
                    }
        
        # 2. 标题上下文增强：如果段落中品牌信息不完整，但标题有明确信息
        if default_attribution:
            # 情况1：只有一个品牌提及且没有车系，结合标题信息
            if (len(mentions) == 1 and 
                mentions[0]['type'] == 'brand' and 
                mentions[0]['series'] == '' and
                default_attribution['series'] != '无车系'):
                return {
                    'brand': default_attribution['brand'],
                    'series': default_attribution['series'],
                    'model': '',
                    'confidence': 0.75,
                    'reason': f"标题上下文增强，确定归属{default_attribution['brand']}{default_attribution['series']}"
                }
            
            # 情况2：段落中品牌与标题品牌一致，但车系信息不完整
            title_brand_mentions = [m for m in mentions if m['brand'] == default_attribution['brand']]
            if (title_brand_mentions and 
                default_attribution['series'] != '无车系' and
                not any(m['series'] for m in title_brand_mentions)):
                return {
                    'brand': default_attribution['brand'],
                    'series': default_attribution['series'],
                    'model': '',
                    'confidence': 0.7,
                    'reason': f"标题上下文补充，确定归属{default_attribution['brand']}{default_attribution['series']}"
                }
        
        # 3. 指代消解：处理代词和话题连续性（使用topic_text和精确位置）
        resolved_mention = self._resolve_coreferences(topic_text, entity_data, mentions)
        if resolved_mention:
            return {
                'brand': resolved_mention['brand'],
                'series': resolved_mention['series'],
                'model': '',
                'confidence': resolved_mention['confidence'] * 0.98,
                'reason': f"指代消解分析，确定归属{resolved_mention['brand']}"
            }
        
        # 4. 优先检查与实体在同一句子中的品牌提及
        entity_sentence_mentions = self._find_mentions_in_entity_sentence(segment, entity, mentions)
        if entity_sentence_mentions:
            best_match = max(entity_sentence_mentions, key=lambda x: x['confidence'])
            return {
                'brand': best_match['brand'],
                'series': best_match['series'],
                'model': '',
                'confidence': best_match['confidence'] * 0.95,
                'reason': f"实体与品牌在同一句子中，高置信度关联"
            }
        
        # 5. 检查距离实体最近的品牌提及（考虑语义边界）
        closest_mention = self._find_closest_mention_with_context(segment, entity, mentions)
        if closest_mention and closest_mention['distance_score'] > 0.6:  # 语义距离阈值
            return {
                'brand': closest_mention['brand'],
                'series': closest_mention['series'],
                'model': '',
                'confidence': closest_mention['confidence'] * closest_mention['distance_score'],
                'reason': f"基于语义距离分析，{closest_mention['brand']}与实体关联度最高"
            }
        
        # 4. 检查描述中的品牌提及
        if description:
            try:
                desc_mentions = await self._extract_brand_mentions(description)
            except:
                desc_mentions = []
            if desc_mentions:
                best_desc_mention = max(desc_mentions, key=lambda x: x['confidence'])
                return {
                    'brand': best_desc_mention['brand'],
                    'series': best_desc_mention['series'],
                    'model': '',
                    'confidence': best_desc_mention['confidence'] * 0.8,
                    'reason': f"观点描述中包含{best_desc_mention['brand']}品牌信息"
                }
        
        # 5. 选择置信度最高的提及
        best_mention = max(mentions, key=lambda x: x.get('confidence', 0))
        return {
            'brand': best_mention.get('brand', ''),
            'series': best_mention.get('series', ''),
            'model': '',
            'confidence': best_mention.get('confidence', 0) * 0.7,
            'reason': f"基于全文分析，最可能归属{best_mention.get('brand', '')}"
        }
    
    def _find_mentions_in_entity_sentence(self, segment: str, entity: str, mentions: List[Dict]) -> List[Dict]:
        """查找与实体在同一句子中的品牌提及"""
        import re
        
        # 分割句子
        sentences = re.split(r'[。！？；;\n]', segment)
        
        # 找到包含实体的句子
        entity_sentence = None
        for sentence in sentences:
            if entity in sentence:
                entity_sentence = sentence.lower()
                break
        
        if not entity_sentence:
            return []
        
        # 检查哪些提及在同一句子中
        same_sentence_mentions = []
        for mention in mentions:
            brand = mention['brand'].lower()
            series = mention['series'].lower() if mention['series'] else ''
            
            if brand in entity_sentence or (series and series in entity_sentence):
                same_sentence_mentions.append(mention)
        
        return same_sentence_mentions
    
    def _find_closest_mention(self, segment: str, entity: str, mentions: List[Dict]) -> Dict[str, Any]:
        """查找距离实体最近的品牌提及"""
        segment_lower = segment.lower()
        entity_pos = segment_lower.find(entity.lower())
        
        if entity_pos == -1:
            return None
        
        closest_mention = None
        min_distance = float('inf')
        
        for mention in mentions:
            brand = mention.get('brand', '').lower()
            series = mention.get('series', '').lower() if mention.get('series') else ''
            
            # 查找品牌位置
            brand_pos = segment_lower.find(brand)
            if brand_pos != -1:
                distance = abs(brand_pos - entity_pos)
                if distance < min_distance:
                    min_distance = distance
                    closest_mention = mention.copy()  # 创建副本避免修改原对象
                    closest_mention['distance'] = distance
            
            # 查找车系位置
            if series:
                series_pos = segment_lower.find(series)
                if series_pos != -1:
                    distance = abs(series_pos - entity_pos)
                    if distance < min_distance:
                        min_distance = distance
                        closest_mention = mention.copy()  # 创建副本避免修改原对象
                        closest_mention['distance'] = distance
        
        return closest_mention
    
    async def _extract_default_from_title(self, title: str) -> Dict[str, str]:
        """增强版从title提取默认品牌车系 - 支持标题信息解析"""
        if not title:
            return {'brand': '长安', 'series': '无车系'}
        
        # 1. 直接提取品牌提及 - 需要在异步上下文中调用
        # 由于这是同步方法，暂时跳过智能过滤
        import asyncio
        try:
            if asyncio.get_event_loop().is_running():
                # 在异步上下文中，暂时关闭智能过滤
                original_filter_client = self.filter_mysql_client
                self.filter_mysql_client = None
                mentions = await self._extract_brand_mentions(title)
                self.filter_mysql_client = original_filter_client
            else:
                # 在同步上下文中，暂时关闭智能过滤
                original_filter_client = self.filter_mysql_client
                self.filter_mysql_client = None
                mentions = asyncio.run(self._extract_brand_mentions(title))
                self.filter_mysql_client = original_filter_client
        except:
            # 降级处理：不使用智能过滤
            mentions = []
        if mentions:
            # 优先选择有车系的提及（更精确）
            series_mentions = [m for m in mentions if m.get('series')]
            if series_mentions:
                best_mention = max(series_mentions, key=lambda x: x.get('confidence', 0))
            else:
                best_mention = max(mentions, key=lambda x: x.get('confidence', 0))
            
            return {
                'brand': best_mention.get('brand', ''),
                'series': best_mention.get('series', '') if best_mention.get('series') else '无车系'
            }
        
        # 2. 如果没有直接提及，解析标题结构
        # 例如："CS75 PLUS深度评测" -> 提取CS75 PLUS
        title_lower = title.lower()
        
        # 按照车系名称长度排序，优先匹配最长的
        sorted_cache = sorted(
            self.brand_series_cache.items(),
            key=lambda x: len(x[1]['series']) if x[1]['series'] else 0,
            reverse=True
        )
        
        # 尝试匹配车系（包含型号、版本等）
        for key, data in sorted_cache:
            brand = data['brand']
            series = data['series']
            
            if series and series.lower() in title_lower:
                return {
                    'brand': brand,
                    'series': series
                }
        
        # 3. 尝试匹配品牌
        for key, data in sorted_cache:
            brand = data['brand']
            if brand.lower() in title_lower:
                return {
                    'brand': brand,
                    'series': '无车系'
                }
        
        return {'brand': '长安', 'series': '无车系'}

    def _get_default_attribution(self, source_data: Dict = None) -> Dict[str, Any]:
        """多级回退的默认归属策略"""
        # 1. 源数据优先
        if source_data and source_data.get('brand'):
            return {
                'brand': source_data['brand'],
                'series': source_data.get('series', '无车系'),
                'model': '',
                'confidence': 0.6,
                'reason': '源数据品牌信息'
            }
        
        # 2. 产品名称解析
        if source_data and (product_name := source_data.get('productName')):
            parsed_brand = self._parse_brand_from_name(product_name)
            if parsed_brand:
                return {
                    'brand': parsed_brand,
                    'series': '无车系', 
                    'model': '',
                    'confidence': 0.5,
                    'reason': f'产品名称解析: {product_name}'
                }
        
        # 3. 论坛系列信息
        if source_data and (forum_series := source_data.get('forumSeries')):
            inferred_brand = self._infer_brand_from_series(forum_series)
            if inferred_brand:
                return {
                    'brand': inferred_brand,
                    'series': forum_series,
                    'model': '',
                    'confidence': 0.4,
                    'reason': f'论坛系列推断: {forum_series}'
                }
        
        # 4. 最终默认值
        return {
            'brand': '长安',
            'series': '无车系',
            'model': '',
            'confidence': 0.3,
            'reason': '默认归属策略'
        }
    
    def _parse_brand_from_name(self, product_name: str) -> Optional[str]:
        """从产品名称解析品牌"""
        if not product_name:
            return None
            
        product_lower = product_name.lower()
        
        # 检查是否包含已知品牌
        for brand in self.brand_series_cache.keys():
            if brand.lower() in product_lower:
                return brand
        
        return None
    
    def _infer_brand_from_series(self, series_name: str) -> Optional[str]:
        """从车系名称推断品牌"""
        if not series_name:
            return None
            
        series_lower = series_name.lower()
        
        # 在缓存中查找匹配的车系
        for key, data in self.brand_series_cache.items():
            if data['series'].lower() == series_lower:
                return data['brand']
        
        return None
    
    async def process_brand_attribution(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理品牌归属判断 - 支持新格式"""
        try:
            topic_id = data.get('topic_id', 'unknown')
            source_data = data.get('source_data', {})
            
            # 获取title用于默认品牌车系提取
            title = source_data.get('title', '')
            default_brand_series = await self._extract_default_from_title(title)
            default_attribution = {
                'brand': default_brand_series['brand'],
                'series': default_brand_series['series'],
                'model': '',
                'confidence': 0.5,
                'reason': f'Title默认归属: {title}'
            }
            
            # 更安全的数据访问 - 支持新旧两种格式
            result = []
            if data.get('result') and isinstance(data['result'], list):
                result = data['result']
            
            if not result:
                print(f"Topic {topic_id}: 没有评价对象数据，跳过品牌归属判断")
                return data  # 返回原始数据，不做修改
            
            print(f"Topic {topic_id}: 开始处理 {len(result)} 个段落")
            
            # 检查是否是新格式（直接包含original_text）
            is_new_format = False
            if result and isinstance(result[0], dict) and 'original_text' in result[0]:
                is_new_format = True
                print(f"Topic {topic_id}: 检测到新格式数据")
            
            processed_count = 0
            
            if is_new_format:
                # 新格式处理逻辑
                for segment_data in result:
                    if not isinstance(segment_data, dict) or 'original_text' not in segment_data:
                        print(f"Topic {topic_id}: 跳过无效的段落: {segment_data}")
                        continue
                    
                    original_text = segment_data.get('original_text', '')
                    aspect = segment_data.get('aspect', {})
                    entity = aspect.get('text', '')
                    
                    # 检查是否已有品牌车系信息
                    if segment_data.get('brand') and segment_data.get('series'):
                        print(f"Topic {topic_id} - 段落: 已有品牌信息，跳过处理")
                        continue
                    
                    try:
                        # 提取品牌提及 - 使用original_text
                        mentions = await self._extract_brand_mentions(original_text)
                        mentions_info = [f"{m['brand']}{m['series']}" for m in mentions]
                        print(f"Topic {topic_id} - 段落: 提取到 {len(mentions)} 个品牌提及: {mentions_info}")
                        
                        # 获取segment和观点信息
                        segment = segment_data.get('segment', original_text)
                        opinion_data = segment_data.get('opinion', {})
                        opinion_text = opinion_data.get('text', '')
                        
                        # 分析归属（使用新格式数据）
                        attribution = await self._analyze_context_attribution(
                            original_text, 
                            segment, 
                            aspect, 
                            opinion_text, 
                            mentions, 
                            None  # 不传入source_data，避免使用旧逻辑
                        )
                        
                        # 如果没有找到任何品牌提及或置信度低，使用标题默认归属
                        if not mentions or attribution['confidence'] < 0.5:
                            attribution = default_attribution
                        
                        print(f"Topic {topic_id} - 段落: 归属结果 {attribution['brand']}{attribution['series']} (置信度: {attribution['confidence']:.2f}, 原因: {attribution['reason']})")
                        
                        # 查询brand和series的code
                        brand_name = attribution['brand']
                        series_name = attribution['series']
                        
                        # 查询对应的code和car_level信息
                        code_info = {"brand_code": "", "series_code": "", "car_level1": "", "car_level2": ""}
                        if self.mysql_client and (brand_name or series_name):
                            try:
                                code_info = await self.mysql_client.query_brand_series_codes(
                                    brand_name=brand_name if brand_name else None,
                                    series_name=series_name if series_name else None
                                )
                            except Exception as e:
                                print(f"Topic {topic_id} - 段落: 查询brand/series code失败: {e}")
                        
                        # 直接回写到段落
                        segment_data['brand'] = code_info.get("brand_code", "")
                        segment_data['series'] = code_info.get("series_code", "")
                        segment_data['car_level1'] = code_info.get("car_level1", "")
                        segment_data['car_level2'] = code_info.get("car_level2", "")
                        
                        processed_count += 1
                        print(f"Topic {topic_id} - 段落: 归属至 {attribution['brand']}{attribution['series']} (置信度: {attribution['confidence']:.2f})")
                    
                    except Exception as segment_error:
                        print(f"Topic {topic_id} - 段落: 处理失败: {segment_error}")
                        # 失败时设置默认值
                        segment_data['brand'] = ""
                        segment_data['series'] = ""
                        segment_data['car_level1'] = ""
                        segment_data['car_level2'] = ""
                        continue
            
            else:
                # 旧格式处理逻辑（保持向后兼容）
                topic_text = source_data.get('topic_text', '')
                
                for obj in result:
                    if not isinstance(obj, dict) or 'text' not in obj:
                        print(f"Topic {topic_id}: 跳过无效的评价对象: {obj}")
                        continue
                        
                    entity = obj.get('text', '')
                    relations = obj.get('relations', {})
                    
                    if not entity:
                        print(f"Topic {topic_id}: 跳过空实体的评价对象")
                        continue
                    
                    # 获取segment和观点
                    segment = topic_text  # 默认使用完整文本
                    if relations.get('segment') and isinstance(relations['segment'], list) and len(relations['segment']) > 0:
                        segment = relations['segment'][0].get('text', topic_text)
                    
                    description = ''
                    if relations.get('观点') and isinstance(relations['观点'], list) and len(relations['观点']) > 0:
                        description = relations['观点'][0].get('text', '')
                    
                    # 检查是否已经有品牌车系信息
                    if (relations.get('品牌车系') and 
                        isinstance(relations['品牌车系'], list) and 
                        len(relations['品牌车系']) > 0 and
                        relations['品牌车系'][0].get('text')):
                        print(f"Topic {topic_id} - {entity}: 已有品牌信息，跳过处理")
                        continue
                    
                    try:
                        # 提取品牌提及
                        mentions = await self._extract_brand_mentions(topic_text)
                        mentions_info = [f"{m['brand']}{m['series']}" for m in mentions]
                        print(f"Topic {topic_id} - {entity}: 提取到 {len(mentions)} 个品牌提及: {mentions_info}")
                        
                        # 分析归属（传入源数据用于智能默认归属）
                        attribution = await self._analyze_context_attribution(topic_text, segment, obj, description, mentions, source_data)
                        print(f"Topic {topic_id} - {entity}: 归属结果 {attribution['brand']}{attribution['series']} (置信度: {attribution['confidence']:.2f}, 原因: {attribution['reason']})")
                        
                        # 格式化结果 - 修正输出格式
                        if attribution['series'] and attribution['series'] != '无车系':
                            brand_series_text = f"{attribution['brand']}{attribution['series']}"
                        else:
                            brand_series_text = f"{attribution['brand']}无车系"
                        
                        # 简化实体映射信息
                        entity_mapping = {
                            'text': brand_series_text
                        }
                        
                        # 更新结果 - 使用正确的字段名 品牌车系
                        if not relations.get('品牌车系'):
                            relations['品牌车系'] = [entity_mapping]
                        else:
                            relations['品牌车系'][0] = entity_mapping
                        
                        processed_count += 1
                        print(f"Topic {topic_id} - {entity}: 归属至 {brand_series_text} (置信度: {attribution['confidence']:.2f})")
                    
                    except Exception as obj_error:
                        print(f"Topic {topic_id} - {entity}: 处理单个对象失败: {obj_error}")
                        # 单个对象失败不影响其他对象处理
                        continue
            
            print(f"Topic {topic_id}: 成功处理 {processed_count}/{len(result)} 个对象")
            return data
            
        except Exception as e:
            error_type = self._classify_error(e)
            print(f"品牌归属判断失败 [{error_type}]: {e}")
            
            if error_type == "data_format_error":
                # 数据格式错误，返回原始数据
                return data
            else:
                # 其他错误继续抛出
                raise
    
    def _classify_error(self, error: Exception) -> str:
        """错误分类 - 保持向后兼容的简化版本"""
        # 使用新的详细分析方法，但只返回错误类型字符串以保持兼容性
        analysis = self._analyze_exception(error, 'general', 'unknown')
        return analysis.get('error_type', 'unknown_error')
    
    async def start_consumers(self):
        """启动消费者 - 增强异常处理和错误路由"""
        async def process_message(message):
            data = None
            topic_id = 'unknown'
            processing_stage = 'initialization'
            
            try:
                processing_stage = 'message_parsing'
                # 处理消息格式
                if hasattr(message, 'value'):
                    # Kafka 消息对象
                    raw_data = message.value.decode('utf-8')
                    data = json.loads(raw_data)
                else:
                    # 直接的字典数据
                    data = message
                
                topic_id = data.get('topic_id', 'unknown')
                print(f"处理消息: {topic_id}")
                
                # 验证消息格式
                processing_stage = 'message_validation'
                validation_result = self._validate_message_format(data)
                if not validation_result['valid']:
                    raise ValueError(f"消息格式验证失败: {validation_result['error']}")
                
                processing_stage = 'brand_attribution_processing'
                # 处理品牌归属判断
                result = await self.process_brand_attribution(data)
                
                processing_stage = 'result_sending'
                # process_brand_attribution 总是返回数据（成功情况）
                # result 要么是处理后的数据，要么是原始数据（都是正常情况）
                self.kafka_client.send_message('result_topic', result)
                print(f"消息处理完成: {topic_id}")
                
            except Exception as e:
                # 增强的异常处理和分类
                error_details = self._analyze_exception(e, processing_stage, topic_id)
                print(f"处理消息失败 [{error_details['error_type']}] 在阶段 [{processing_stage}]: {e}")
                print(traceback.format_exc())
                
                # 根据错误类型决定处理策略
                await self._handle_processing_error(e, data, topic_id, processing_stage, error_details)
        
        # 修复：使用正确的方法名，并设置从earliest开始消费
        self.kafka_client.start_consumer(
            ['brand_attribution_topic'], 
            process_message,
            group_id='brand-attribution-service-group',
            consumer_name=None,
            auto_offset_reset='earliest',  # 从最早的消息开始消费
            enable_auto_commit=False  # 启用手动提交
        )
    
    def _validate_message_format(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """验证消息格式"""
        try:
            # 基本字段检查
            if not isinstance(data, dict):
                return {'valid': False, 'error': '消息不是字典格式'}
            
            # 必需字段检查
            required_fields = ['topic_id']
            missing_fields = [field for field in required_fields if field not in data]
            if missing_fields:
                return {'valid': False, 'error': f'缺少必需字段: {missing_fields}'}
            
            # 数据结构检查
            if 'result' in data:
                result = data['result']
                if not isinstance(result, list):
                    return {'valid': False, 'error': 'result字段必须是列表格式'}
                
                # 检查result内容格式
                if result:
                    sample_item = result[0]
                    if not isinstance(sample_item, dict):
                        return {'valid': False, 'error': 'result列表项必须是字典格式'}
            
            return {'valid': True, 'error': None}
            
        except Exception as e:
            return {'valid': False, 'error': f'验证过程异常: {str(e)}'}
    
    def _analyze_exception(self, exception: Exception, processing_stage: str, topic_id: str) -> Dict[str, Any]:
        """分析异常详情"""
        error_str = str(exception).lower()
        exception_type = type(exception).__name__
        
        # 详细的错误分类
        error_analysis = {
            'error_type': 'unknown_error',
            'severity': 'high',
            'recoverable': False,
            'retry_recommended': False,
            'stage': processing_stage
        }
        
        # JSON解析错误
        if isinstance(exception, json.JSONDecodeError):
            error_analysis.update({
                'error_type': 'json_decode_error',
                'severity': 'high',
                'recoverable': False,
                'retry_recommended': False
            })
        
        # 数据格式错误
        elif isinstance(exception, (KeyError, TypeError, ValueError)):
            error_analysis.update({
                'error_type': 'data_format_error',
                'severity': 'medium',
                'recoverable': True,
                'retry_recommended': False
            })
        
        # 数据库连接错误
        elif "mysql" in error_str or "database" in error_str or "connection" in error_str:
            error_analysis.update({
                'error_type': 'database_error',
                'severity': 'high',
                'recoverable': True,
                'retry_recommended': True
            })
        
        # 超时错误
        elif isinstance(exception, asyncio.TimeoutError) or "timeout" in error_str:
            error_analysis.update({
                'error_type': 'timeout_error',
                'severity': 'medium',
                'recoverable': True,
                'retry_recommended': True
            })
        
        # Kafka错误
        elif "kafka" in error_str:
            error_analysis.update({
                'error_type': 'kafka_error',
                'severity': 'high',
                'recoverable': True,
                'retry_recommended': True
            })
        
        # 内存错误
        elif "memory" in error_str or "oom" in error_str:
            error_analysis.update({
                'error_type': 'memory_error',
                'severity': 'critical',
                'recoverable': False,
                'retry_recommended': False
            })
        
        # 算法处理错误
        elif processing_stage == 'brand_attribution_processing':
            error_analysis.update({
                'error_type': 'algorithm_error',
                'severity': 'medium',
                'recoverable': True,
                'retry_recommended': False
            })
        
        return error_analysis
    
    async def _handle_processing_error(self, exception: Exception, data: Dict, topic_id: str, 
                                     processing_stage: str, error_details: Dict):
        """处理处理错误的统一方法"""
        try:
            # 构建错误消息
            error_message_data = self._build_error_message(
                exception, data, topic_id, processing_stage, error_details
            )
            
            # 根据错误严重程度选择不同的处理策略
            severity = error_details.get('severity', 'high')
            
            if severity == 'critical':
                # 关键错误：立即发送到错误topic，并记录详细日志
                await self._send_to_error_topic_with_retry(error_message_data, max_retries=3)
                print(f"CRITICAL ERROR: {topic_id} - {exception}")
                
            elif severity == 'high':
                # 高严重性错误：发送到错误topic
                await self._send_to_error_topic_with_retry(error_message_data, max_retries=2)
                print(f"HIGH ERROR: {topic_id} - {exception}")
                
            elif severity == 'medium':
                # 中等严重性错误：根据是否可恢复决定处理方式
                if error_details.get('recoverable', False):
                    # 尝试使用降级策略处理
                    fallback_result = await self._try_fallback_processing(data, exception)
                    if fallback_result:
                        # 降级处理成功，发送结果
                        self.kafka_client.send_message('result_topic', fallback_result)
                        print(f"MEDIUM ERROR RECOVERED: {topic_id} - 使用降级策略成功")
                        return
                
                # 降级失败或不可恢复，发送到错误topic
                await self._send_to_error_topic_with_retry(error_message_data, max_retries=1)
                print(f"MEDIUM ERROR: {topic_id} - {exception}")
            
            else:
                # 低严重性错误或其他：发送到错误topic
                await self._send_to_error_topic_with_retry(error_message_data, max_retries=1)
                print(f"ERROR: {topic_id} - {exception}")
                
        except Exception as handle_error:
            # 错误处理本身失败的情况
            print(f"错误处理失败: {handle_error}")
            print(f"原始错误: {exception}")
            # 最后的兜底措施：至少尝试发送一个简单的错误消息
            try:
                simple_error = {
                    'topic_id': topic_id,
                    'error_type': 'error_handling_failure',
                    'error_msg': f"处理错误时发生异常: {str(handle_error)}, 原始错误: {str(exception)}",
                    'timestamp': datetime.now().isoformat()
                }
                self.kafka_client.send_to_error_topic(simple_error)
            except:
                print(f"最终错误处理也失败了，topic_id: {topic_id}")
    
    def _build_error_message(self, exception: Exception, data: Dict, topic_id: str, 
                           processing_stage: str, error_details: Dict) -> Dict:
        """构建标准化错误消息"""
        try:
            # 尝试构建完整的ErrorMessage
            source_data = data.get('source_data', {}) if data else {}
            if isinstance(source_data, dict) and source_data:
                try:
                    source_data = SourceData(**source_data)
                except:
                    # 如果SourceData构造失败，使用原始字典
                    pass
            
            error_message = ErrorMessage(
                topic_id=topic_id,
                source_data=source_data,
                retry_count=data.get('retry_count', 0) if data and isinstance(data, dict) else 0,
                error_type=f"{error_details.get('error_type', 'unknown_error')}_{processing_stage}",
                error_msg=f"[{processing_stage}] {str(exception)}",
                last_ts=data.get('last_ts', '') if data and isinstance(data, dict) else ''
            )
            
            return error_message
            
        except Exception as build_error:
            # 如果构建ErrorMessage失败，返回简化版本
            print(f"构建ErrorMessage失败: {build_error}")
            return {
                'topic_id': topic_id,
                'error_type': f"{error_details.get('error_type', 'unknown_error')}_{processing_stage}",
                'error_msg': f"[{processing_stage}] {str(exception)}",
                'timestamp': datetime.now().isoformat(),
                'build_error': str(build_error)
            }
    
    async def _send_to_error_topic_with_retry(self, error_message, max_retries: int = 2):
        """带重试的错误topic发送"""
        for attempt in range(max_retries + 1):
            try:
                self.kafka_client.send_to_error_topic(error_message)
                print(f"错误消息发送成功 (尝试 {attempt + 1}/{max_retries + 1})")
                return
            except Exception as send_error:
                print(f"发送错误消息失败 (尝试 {attempt + 1}/{max_retries + 1}): {send_error}")
                if attempt == max_retries:
                    print(f"发送错误消息最终失败，放弃重试")
                else:
                    await asyncio.sleep(0.5)  # 短暂等待后重试
    
    async def _try_fallback_processing(self, data: Dict, original_exception: Exception) -> Optional[Dict]:
        """尝试降级处理策略"""
        try:
            if not data or not isinstance(data, dict):
                return None
            
            topic_id = data.get('topic_id', 'unknown')
            print(f"尝试降级处理: {topic_id}")
            
            # 降级策略1：使用默认归属
            if 'result' in data and isinstance(data['result'], list):
                result_copy = data.copy()
                
                # 为每个段落设置默认品牌归属
                for item in result_copy['result']:
                    if isinstance(item, dict):
                        # 检测数据格式
                        if 'original_text' in item:  # 新格式
                            if not item.get('brand') or not item.get('series'):
                                item['brand'] = ""  # 默认空，让下游处理
                                item['series'] = ""
                                item['car_level1'] = ""
                                item['car_level2'] = ""
                        else:  # 旧格式
                            relations = item.get('relations', {})
                            if not relations.get('品牌车系'):
                                relations['品牌车系'] = [{'text': '长安无车系'}]  # 默认归属
                
                print(f"降级处理成功: {topic_id}")
                return result_copy
            
            return None
            
        except Exception as fallback_error:
            print(f"降级处理失败: {fallback_error}")
            return None
    
    def _get_error_recovery_strategy(self, error_details: Dict) -> Dict[str, Any]:
        """获取错误恢复策略"""
        error_type = error_details.get('error_type', 'unknown_error')
        
        recovery_strategies = {
            'database_error': {
                'strategy': 'retry_with_fallback',
                'max_retries': 3,
                'retry_delay': 1.0,
                'fallback_action': 'use_cache_or_default',
                'escalation_threshold': 5
            },
            'timeout_error': {
                'strategy': 'retry_with_backoff',
                'max_retries': 2,
                'retry_delay': 0.5,
                'fallback_action': 'simplified_processing',
                'escalation_threshold': 3
            },
            'memory_error': {
                'strategy': 'immediate_escalation',
                'max_retries': 0,
                'retry_delay': 0,
                'fallback_action': 'service_restart',
                'escalation_threshold': 1
            },
            'data_format_error': {
                'strategy': 'validate_and_fix',
                'max_retries': 1,
                'retry_delay': 0,
                'fallback_action': 'default_attribution',
                'escalation_threshold': 10
            },
            'algorithm_error': {
                'strategy': 'fallback_processing',
                'max_retries': 1,
                'retry_delay': 0,
                'fallback_action': 'simple_attribution',
                'escalation_threshold': 5
            }
        }
        
        return recovery_strategies.get(error_type, {
            'strategy': 'basic_retry',
            'max_retries': 1,
            'retry_delay': 0.5,
            'fallback_action': 'error_topic',
            'escalation_threshold': 3
        })
    
    def _should_escalate_error(self, error_type: str, error_count: int) -> bool:
        """判断是否应该升级错误处理"""
        recovery_strategy = self._get_error_recovery_strategy({'error_type': error_type})
        threshold = recovery_strategy.get('escalation_threshold', 3)
        
        return error_count >= threshold
    
    def _log_error_metrics(self, error_details: Dict, topic_id: str, processing_stage: str):
        """记录错误指标（为监控系统准备）"""
        try:
            # 这里可以集成到监控系统，现在先用日志记录
            metrics = {
                'timestamp': datetime.now().isoformat(),
                'topic_id': topic_id,
                'error_type': error_details.get('error_type'),
                'severity': error_details.get('severity'),
                'stage': processing_stage,
                'recoverable': error_details.get('recoverable'),
                'service': 'brand-attribution'
            }
            
            print(f"ERROR_METRICS: {json.dumps(metrics)}")
            
        except Exception as log_error:
            print(f"记录错误指标失败: {log_error}")
    
    async def _perform_health_recovery_check(self) -> Dict[str, Any]:
        """执行健康恢复检查"""
        recovery_status = {
            'database_connection': False,
            'brand_cache_status': False,
            'filter_rules_status': False,
            'kafka_connection': False,
            'overall_health': False
        }
        
        try:
            # 检查数据库连接
            if self.mysql_client:
                try:
                    # 简单的连接测试
                    await self.mysql_client.execute_query("SELECT 1")
                    recovery_status['database_connection'] = True
                except:
                    recovery_status['database_connection'] = False
            
            # 检查品牌缓存状态
            if self.brand_series_cache and len(self.brand_series_cache) > 0:
                recovery_status['brand_cache_status'] = True
            
            # 检查过滤规则状态
            if self.filter_mysql_client:
                try:
                    rules = await self._load_filter_rules()
                    if rules and any(len(rule_list) > 0 for rule_list in rules.values()):
                        recovery_status['filter_rules_status'] = True
                except:
                    recovery_status['filter_rules_status'] = False
            
            # 检查Kafka连接（简化检查）
            if hasattr(self, 'kafka_client') and self.kafka_client:
                recovery_status['kafka_connection'] = True
            
            # 计算整体健康状态
            critical_components = ['database_connection', 'brand_cache_status']
            critical_healthy = all(recovery_status[comp] for comp in critical_components)
            
            recovery_status['overall_health'] = critical_healthy
            
        except Exception as health_error:
            print(f"健康恢复检查失败: {health_error}")
            recovery_status['overall_health'] = False
        
        return recovery_status
    
    async def _attempt_service_recovery(self, error_type: str) -> bool:
        """尝试服务自恢复"""
        try:
            print(f"尝试服务自恢复，错误类型: {error_type}")
            
            if error_type in ['database_error', 'mysql_error']:
                # 数据库连接恢复
                try:
                    if self.mysql_client:
                        await self.mysql_client.close()
                    if self.filter_mysql_client:
                        await self.filter_mysql_client.close()
                    
                    # 重新初始化数据库连接
                    await self._initialize_service_specific()
                    print("数据库连接恢复成功")
                    return True
                except Exception as db_recovery_error:
                    print(f"数据库连接恢复失败: {db_recovery_error}")
                    return False
            
            elif error_type == 'memory_error':
                # 内存清理恢复
                try:
                    # 清理缓存
                    if hasattr(self, '_filter_rules_cache'):
                        self._filter_rules_cache = None
                    
                    # 强制垃圾回收
                    import gc
                    gc.collect()
                    
                    print("内存清理完成")
                    return True
                except Exception as memory_recovery_error:
                    print(f"内存清理失败: {memory_recovery_error}")
                    return False
            
            elif error_type in ['brand_cache_error', 'algorithm_error']:
                # 重新加载品牌数据
                try:
                    await self._load_brand_series_from_mysql()
                    self._build_synonym_cache()
                    print("品牌缓存重新加载成功")
                    return True
                except Exception as cache_recovery_error:
                    print(f"品牌缓存恢复失败: {cache_recovery_error}")
                    return False
            
            return False
            
        except Exception as recovery_error:
            print(f"服务自恢复过程失败: {recovery_error}")
            return False


    async def _service_specific_health_check(self) -> dict:
        """服务特定的健康检查"""
        return {
            "cached_brands": len(self.brand_series_cache)
        }
    
    async def _shutdown_service_specific(self):
        """服务特定的关闭逻辑"""
        # 关闭品牌车系数据库客户端
        if self.mysql_client:
            await self.mysql_client.close()
        
        # 关闭过滤规则数据库客户端
        if self.filter_mysql_client:
            await self.filter_mysql_client.close()
    
    def _add_custom_routes(self, app: FastAPI):
        """添加服务特定的路由"""
        @app.post("/attribution", response_model=BrandAttributionResponse)
        async def brand_attribution(request: BrandAttributionRequest):
            """品牌归属判断API"""
            try:
                # 提取品牌提及 - 使用segment作为文本分析源
                mentions = await self._extract_brand_mentions(request.segment)
                
                # 为API请求创建模拟的实体数据（因为没有完整的位置信息）
                entity_data = {
                    'text': request.entity,
                    'start': request.segment.find(request.entity),
                    'end': request.segment.find(request.entity) + len(request.entity) if request.segment.find(request.entity) != -1 else -1
                }
                
                # 分析归属
                attribution = await self._analyze_context_attribution(
                    request.segment, 
                    request.segment,  # 对于API，segment和topic_text相同
                    entity_data, 
                    request.description, 
                    mentions
                )
                
                return BrandAttributionResponse(
                    topic_id=request.topic_id,
                    brand=attribution['brand'],
                    series=attribution['series'],
                    model=attribution['model'],
                    confidence=attribution['confidence'],
                    attribution_reason=attribution['reason']
                )
                
            except Exception as e:
                print(f"品牌归属判断API失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @app.post("/test_smart_filter")
        async def test_smart_filter(request: dict):
            """测试智能过滤功能"""
            import time
            try:
                test_texts = request.get('test_texts', [])
                if not test_texts:
                    # 使用默认测试文本
                    test_texts = [
                        "9月8日，恒生电子涨1.91%，成交额14.23亿元，换手率2.15%，总市值665.90亿元。华夏沪深300ETF位居第十大流通股东",
                        "比亚迪汉EV真的很不错，动力强劲，内饰豪华",
                        "华泰柏瑞ETF基金表现良好，收益率达到7.02%",
                        "这个武汉的好汉开着比亚迪元去了大汉口",
                        "海豹突击队使用的装备很先进，海豹家族庞大"
                    ]
                
                results = []
                total_start_time = time.time()
                
                for i, text in enumerate(test_texts):
                    start_time = time.time()
                    
                    # 测试原始品牌提取（临时关闭智能过滤）
                    original_filter_client = self.filter_mysql_client
                    self.filter_mysql_client = None
                    original_mentions = await self._extract_brand_mentions(text)
                    self.filter_mysql_client = original_filter_client
                    
                    # 测试智能过滤后的品牌提取
                    filtered_mentions = await self._extract_brand_mentions(text)
                    
                    end_time = time.time()
                    processing_time = (end_time - start_time) * 1000  # 毫秒
                    
                    results.append({
                        'text': text[:100] + "..." if len(text) > 100 else text,
                        'original_count': len(original_mentions),
                        'filtered_count': len(filtered_mentions),
                        'filtered_mentions': [f"{m['brand']}-{m['series']}" for m in filtered_mentions],
                        'processing_time_ms': round(processing_time, 2),
                        'filter_effectiveness': f"{len(original_mentions) - len(filtered_mentions)} mentions filtered"
                    })
                
                total_end_time = time.time()
                total_time = (total_end_time - total_start_time) * 1000
                
                return APIResponse(
                    success=True,
                    message="Smart filter test completed",
                    data={
                        'test_results': results,
                        'total_processing_time_ms': round(total_time, 2),
                        'average_time_per_text_ms': round(total_time / len(test_texts), 2),
                        'filter_rules_loaded': len(await self._load_filter_rules()) if self.filter_mysql_client else 0
                    }
                )
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))
        
        @app.post("/reload_filter_rules")
        async def reload_filter_rules():
            """重新加载过滤规则"""
            try:
                # 清空缓存
                self._filter_rules_cache = None
                self._filter_rules_cache_time = 0
                
                # 重新加载
                rules = await self._load_filter_rules()
                
                return APIResponse(
                    success=True,
                    message="Filter rules reloaded successfully",
                    data={
                        'global_blacklist_count': len(rules['global_blacklist']),
                        'context_rule_count': len(rules['context_rule']),
                        'ambiguous_pattern_count': len(rules['ambiguous_pattern'])
                    }
                )
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))
        
        @app.get("/brand_cache_info")
        async def brand_cache_info():
            """获取品牌缓存信息"""
            try:
                return APIResponse(
                    success=True,
                    message="Brand cache info retrieved",
                    data={
                        'cached_brands_count': len(self.brand_series_cache),
                        'synonyms_count': len(self.synonym_map),
                        'filter_rules_cached': self._filter_rules_cache is not None,
                        'sample_brands': list(self.brand_series_cache.keys())[:10]
                    }
                )
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))


# 创建服务实例
service = BrandAttributionService()

# 创建FastAPI应用
app = service.create_fastapi_app(
    title="品牌车系车型归属判断服务",
    description="通过NLU判断观点归属的品牌车系车型"
)


if __name__ == "__main__":
    service.run_with_app_string("main:app")