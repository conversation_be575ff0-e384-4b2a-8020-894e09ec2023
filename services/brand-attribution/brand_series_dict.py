#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
品牌车系字典 - 基于Java SerieIdsExtractionClassify实现
"""

# 长安品牌及其车系
CHANGAN_BRAND_SERIES = {
    'brand': '长安',
    'series': [
        'CS75 PLUS',
        'CS75',
        'CS55 PLUS',
        'CS55',
        'CS35 PLUS',
        'CS35',
        'CS95',
        'CS85',
        'CS15',
        '睿行',
        '睿骋',
        '逸动',
        '逸动DT',
        '逸动ET',
        '逸动PLUS',
        '逸动XT',
        '悦翔',
        '悦翔V3',
        '悦翔V5',
        '悦翔V7',
        '奔奔',
        '奔奔E-Star',
        '奔奔EV',
        '奔奔MINI',
        '奔奔LOVE',
        '凌轩',
        '欧力威',
        '欧诺',
        '星卡',
        '神骐',
        '神骐T20',
        '神骐T30',
        '神骐F30',
        '神骐F50',
        '跨越王',
        '跨越者D5',
        '跨越者D3',
        '长安之星',
        '长安之星2',
        '长安之星3',
        '长安之星5',
        '长安之星7',
        '长安之星9',
        '长安星卡',
        '长安星卡C系',
        '长安星卡L系',
        '长安星卡PLUS',
        '长安星卡T30',
        '长安星卡T50',
        '长安星卡T80',
        '长安星卡T100',
        '长安星卡EV',
        '长安星卡C系EV',
        '长安星卡L系EV',
        '长安星卡PLUS EV',
        '长安星卡T30 EV',
        '长安星卡T50 EV',
        '长安星卡T80 EV',
        '长安星卡T100 EV',
        '长安星卡C系纯电动',
        '长安星卡L系纯电动',
        '长安星卡PLUS纯电动',
        '长安星卡T30纯电动',
        '长安星卡T50纯电动',
        '长安星卡T80纯电动',
        '长安星卡T100纯电动',
        '尼欧',
        '欧尚',
        '欧尚X5',
        '欧尚X7',
        '欧尚X70A',
        '欧尚科赛',
        '欧尚科尚',
        '欧尚长行',
        '欧尚A600',
        '欧尚A800',
        '欧尚CX70',
        '欧尚CX70T',
        '欧尚COS1°',
        '欧尚科赛3',
        '欧尚科赛5',
        '欧尚科赛GT',
        '欧尚科赛Pro',
        '欧尚科赛ProGT',
        '欧尚Z6',
        '欧尚Z6 智电iDD',
        '欧尚X5 PLUS',
        '欧尚X7 PLUS',
        '欧尚X7 PLUS 智电iDD',
        '欧尚X5 智电iDD',
        # 新增UNI系列车系
        'UNI-T',
        'UNI-K', 
        'UNI-V',
        'UNI-Z',
        'UNI-K iDD',
        'UNI-V iDD'
    ]
}

# 其他主要品牌及其车系（用于对比和识别）
BRAND_SERIES_DICT = {
    '奇瑞': {
        'series': [
            '艾瑞泽5', '艾瑞泽8', '瑞虎','瑞虎3', '瑞虎5', '瑞虎7', '瑞虎8', '瑞虎9',
            '小蚂蚁', '大蚂蚁', '风云2', 'QQ', 'QQ3', 'QQ6', 'A1', 'A3', 'A5',
            '东方之子', '旗云', '风云', '瑞虎3x', '瑞虎5x', '瑞虎7 PLUS', '瑞虎8 PLUS',
            '瑞虎8 PRO', '瑞虎8 PLUS鲲鹏e+', '探索06', '探索06 C-DM'
        ]
    },
    '吉利': {
        'series': [
            '博越', '博越PRO', '博越L', '缤越', '缤瑞', '帝豪', '帝豪GL', '帝豪GS',
            '远景', '远景X3', '远景X6', '远景S1', '星越', '星越L', '星瑞', '嘉际',
            '豪越', '豪越PRO', 'ICON', '几何A', '几何C', '几何E', '熊猫', '领克01',
            '领克02', '领克03', '领克05', '领克06', '领克09', '极氪001', '极氪009',
            '极氪X', '极氪007', '银河L6', '银河L7', '银河E8', '缤越COOL', '缤瑞COOL'
        ]
    },
    '比亚迪': {
        'series': [
            '汉', '唐', '宋', '元', '秦', '海豚', '海豹', '海狮', '驱逐舰05',
            '驱逐舰07', '护卫舰07', '仰望U8', '仰望U9', '腾势D9', '腾势N7', '腾势N8',
            '方程豹豹5', '宋PLUS', '宋Pro', '秦PLUS', '秦Pro', '元PLUS', '元Pro',
            '汉EV', '汉DM', '唐EV', '唐DM', '宋MAX', '宋MAX DM', 'e2', 'e3',
            'e5', 'e6', 'F3', 'F0', 'S2', 'S6', 'S7'
        ]
    },
    '哈弗': {
        'series': [
            'H1', 'H2', 'H2s', 'H4', 'H5', 'H6', 'H6 Coupe', 'H7', 'H8', 'H9',
            'F5', 'F7', 'F7x', '赤兔', '大狗', '神兽', '酷狗', '初恋', '猛龙',
            '枭龙', '枭龙MAX', 'H6国潮版', 'H6S', 'H6新能源', '神兽DHT', '赤兔DHT'
        ]
    },
    'WEY': {
        'series': [
            'VV5', 'VV6', 'VV7', '摩卡', '拿铁', '玛奇朵', '高山', '蓝山',
            '坦克300', '坦克500', '坦克400', '坦克700', '摩卡DHT-PHEV',
            '拿铁DHT-PHEV', '玛奇朵DHT-PHEV'
        ]
    },
    '大众': {
        'series': [
            '捷达', '桑塔纳', 'POLO', '朗逸', '朗行', '朗境', '宝来', '高尔夫',
            '速腾', '迈腾', '帕萨特', '途观', '途观L', '途昂', '途岳', '途铠',
            '探歌', '探岳', '探影', '揽境', '揽巡', 'ID.3', 'ID.4', 'ID.6',
            'ID.7', 'CC', '甲壳虫', '蔚领', '辉昂', '途锐'
        ]
    },
    '丰田': {
        'series': [
            '卡罗拉', '雷凌', '凯美瑞', '亚洲龙', '亚洲狮', '凌尚', '汉兰达',
            '皇冠陆放', '威兰达', 'RAV4荣放', '奕泽', 'C-HR', '锋兰达',
            '普拉多', '兰德酷路泽', '塞纳', '埃尔法', '威尔法', '威驰',
            '威驰FS', '雅力士', '致炫', '致享', '普锐斯', 'bZ3', 'bZ4X'
        ]
    },
    '本田': {
        'series': [
            '思域', '雅阁', '凌派', '享域', '英仕派', '杰德', '哥瑞', '竞瑞',
            '飞度', '锋范', '奥德赛', '艾力绅', 'CR-V', 'XR-V', 'UR-V',
            'HR-V', '缤智', '冠道', '皓影', 'ZR-V', '型格', '来福酱',
            '思域Hatchback', '思域Type-R'
        ]
    }
}

# 品牌同义词映射
BRAND_SYNONYMS = {
    '长安': ['长安', 'Changan', 'CHANGAN', '长安轿车', '长安商用'],
    '奇瑞': ['奇瑞', 'Chery', 'CHERY', '奇瑞汽车'],
    '吉利': ['吉利', 'Geely', 'GEELY', '吉利汽车', '吉利帝豪'],
    '比亚迪': ['比亚迪', 'BYD', 'BYD汽车', '比亚迪汽车'],
    '哈弗': ['哈弗', 'Haval', 'HAVAL', '哈弗SUV', '哈弗汽车'],
    'WEY': ['WEY', '魏派', '魏牌', 'WEY品牌'],
    '大众': ['大众', 'Volkswagen', 'VW', '大众汽车'],
    '丰田': ['丰田', 'Toyota', 'TOYOTA', '丰田汽车'],
    '本田': ['本田', 'Honda', 'HONDA', '本田汽车']
}

# 车系同义词映射
SERIES_SYNONYMS = {
    'CS75 PLUS': ['CS75 PLUS', 'CS75PLUS', 'CS75+', 'CS75P'],
    'CS75': ['CS75', 'CS-75'],
    'CS55 PLUS': ['CS55 PLUS', 'CS55PLUS', 'CS55+'],
    'CS35 PLUS': ['CS35 PLUS', 'CS35PLUS', 'CS35+'],
    '逸动': ['逸动', 'EADO', 'Eado'],
    '帝豪': ['帝豪', 'Emgrand', 'EMGRAND'],
    '博越': ['博越', 'Boyue', 'BOYUE'],
    '星越': ['星越', 'Xingyue', 'XINGYUE'],
    '汉': ['汉', 'Han', 'HAN'],
    '唐': ['唐', 'Tang', 'TANG'],
    '宋': ['宋', 'Song', 'SONG']
}

# 歧义词过滤列表
AMBIGUOUS_FILTER = {
    'remove_patterns': [
        '风景', '炮', '小金刚', '萨普', '风度', '风驰', '金刚', '远景', 'icon',
        '帝豪', '阳光', '擎天柱', '莲花', '御风', 'life', '高尔夫', 'golf',
        '汉', '秦', '宋', '唐', '元', '海豚', '海豹', '小蚂蚁', '大蚂蚁', '风云2',
        '航海家', '冒险家', '飞行家', '领航员', '赤兔', '大狗', '神兽',
        '摩卡', '拿铁', '玛奇朵', 'iq', '芭蕾猫', '白猫', '好猫', '好猫gt',
        '黑猫', '闪电猫', '奔奔', '尼欧', '星骋', '金牛座', '探险者', '亚洲狮',
        'Q5', '开拓者', 'gl6', 'gl8', '鲸', '悦悦', 'fy11', 'ga3', 'ga4',
        'ga6', 'ga8', 'gs3', 'gs4', 'gs5', 'gs7', 'gs8', 'fit', 'city',
        '世嘉', '驱逐舰', '酷狗', '魔方', '小老虎', '领主', '征途', 'polo',
        '天籁', '楼兰', 'k8', 'k6', 'k3', 'k23', 'hk8', 'hk6', 'hk3',
        'gk8', 'gk6', 'gk3'
    ],
    'brand_context': {
        '风景': ['福田'],
        '炮': ['长城'],
        '小金刚': ['福田'],
        '萨普': ['福田'],
        '风度': ['湖北大运'],
        '风驰': ['湖北大运'],
        '金刚': ['吉利'],
        '远景': ['吉利'],
        'icon': ['吉利'],
        '帝豪': ['吉利'],
        '阳光': ['日产'],
        '擎天柱': ['江淮'],
        '莲花': ['东风'],
        '御风': ['东风'],
        'life': ['本田'],
        '高尔夫': ['大众'],
        'golf': ['大众'],
        '汉': ['比亚迪'],
        '秦': ['比亚迪'],
        '宋': ['比亚迪'],
        '唐': ['比亚迪'],
        '元': ['比亚迪'],
        '海豚': ['比亚迪'],
        '海豹': ['比亚迪'],
        '小蚂蚁': ['奇瑞'],
        '大蚂蚁': ['奇瑞'],
        '风云2': ['奇瑞'],
        '航海家': ['林肯'],
        '冒险家': ['林肯'],
        '飞行家': ['林肯'],
        '领航员': ['林肯'],
        '赤兔': ['哈弗'],
        '大狗': ['哈弗'],
        '神兽': ['哈弗'],
        '摩卡': ['WEY'],
        '拿铁': ['WEY'],
        '玛奇朵': ['WEY'],
        'iq': ['欧拉'],
        '芭蕾猫': ['欧拉'],
        '白猫': ['欧拉'],
        '好猫': ['欧拉'],
        '好猫gt': ['欧拉'],
        '黑猫': ['欧拉'],
        '闪电猫': ['欧拉'],
        '奔奔': ['长安'],
        '尼欧': ['长安', '欧尚'],
        '星骋': ['马自达'],
        '金牛座': ['福特'],
        '探险者': ['福特'],
        '亚洲狮': ['丰田'],
        'Q5': ['奥迪'],
        '开拓者': ['雪佛兰'],
        'gl6': ['别克'],
        'gl8': ['别克'],
        '鲸': ['荣威'],
        '悦悦': ['江淮'],
        'fy11': ['吉利', '星越'],
        'ga3': ['传祺'],
        'ga4': ['传祺'],
        'ga6': ['传祺'],
        'ga8': ['传祺'],
        'gs3': ['传祺'],
        'gs4': ['传祺'],
        'gs5': ['传祺'],
        'gs7': ['传祺'],
        'gs8': ['传祺'],
        'fit': ['本田'],
        'city': ['本田'],
        '世嘉': ['雪铁龙'],
        '驱逐舰': ['比亚迪'],
        '酷狗': ['哈弗'],
        '魔方': ['北京汽车'],
        '小老虎': ['中兴'],
        '领主': ['中兴'],
        '征途': ['五菱', '江淮', '湖北大运'],
        'polo': ['大众'],
        '天籁': ['日产'],
        '楼兰': ['日产'],
        'k8': ['凯马'],
        'k6': ['凯马'],
        'k3': ['凯马'],
        'k23': ['凯马'],
        'hk8': ['凯马'],
        'hk6': ['凯马'],
        'hk3': ['凯马'],
        'gk8': ['凯马'],
        'gk6': ['凯马'],
        'gk3': ['凯马']
    }
}

# 标点符号分割模式
PUNCTUATION_PATTERNS = [
    '，', '。', '！', '？', '；', '：', '、',
    ',', '.', '!', '?', ';', ':', '\n', '\t'
]

# 影响域边界模式
INFLUENCE_BOUNDARIES = [
    '比', '不如', '相比', '对比', '相较', '相对于',
    '和', '与', '跟', '同', '像', '如', '似',
    '的', '之', '在', '对于', '关于'
]

def get_all_brand_series():
    """获取所有品牌车系组合"""
    all_brands = {}
    
    # 添加长安
    all_brands['长安'] = CHANGAN_BRAND_SERIES['series']
    
    # 添加其他品牌
    for brand, data in BRAND_SERIES_DICT.items():
        all_brands[brand] = data['series']
    
    return all_brands

def get_brand_series_synonyms():
    """获取品牌车系同义词映射"""
    synonym_map = {}
    
    # 品牌同义词
    for brand, synonyms in BRAND_SYNONYMS.items():
        for synonym in synonyms:
            synonym_map[synonym.lower()] = brand
    
    # 车系同义词
    for series, synonyms in SERIES_SYNONYMS.items():
        for synonym in synonyms:
            synonym_map[synonym.lower()] = series
    
    return synonym_map

def get_ambiguous_filter():
    """获取歧义词过滤配置"""
    return AMBIGUOUS_FILTER