"""
规则匹配服务
负责精确匹配主体和描述
"""
import asyncio
from mailbox import NotEmptyError
import signal
import sys
from pathlib import Path
from typing import List, Tuple, Optional, Dict, Any

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from fastapi import FastAPI, HTTPException
from contextlib import asynccontextmanager
import uvicorn

from shared.base import BaseService
from shared.utils.text_processor import TextProcessor
from shared.models.schemas import (
    RuleMatchResult, OpinionMatch, APIResponse,
    NormalizedSubject, NormalizedDescription, NormalizedOpinion, StandardViewpoint
)


class RuleMatcherService(BaseService):
    """规则匹配服务"""
    
    def __init__(self):
        super().__init__('rule-matcher')
        self.text_processor = None
        self.rule_config = self.config_manager.get_rule_config()
    
    def _init_service_specific_config(self):
        """初始化服务特定配置"""
        pass
    
    async def _initialize_service_specific(self):
        """服务特定的初始化逻辑"""
        # 初始化Elasticsearch客户端（主体和描述数据都在ES中）
        from shared.elasticsearch.es_client import ElasticsearchClient
        
        self.es_client = ElasticsearchClient(
            self.config_manager.get_elasticsearch_config(),
            self.config_manager.get_vector_config()
        )
        await self.es_client.initialize()
        
        # 初始化MySQL客户端（仅用于标准观点查询和新词记录）
        from shared.database.mysql_client import MySQLClient
        db_config = self.config_manager.get_database_config()
        # 覆盖为 MySQL 专用配置（优先 MYSQL_*，其次 DB_*，再读 configs/config.yaml 的 database.mysql.*）
        db_host = self.config_manager._get_env_or_config('MYSQL_HOST', 'database.mysql.host', self.config_manager._get_env_or_config('DB_HOST', 'database.mysql.host', db_config.host))
        db_port = self.config_manager._get_env_or_config('MYSQL_PORT', 'database.mysql.port', self.config_manager._get_env_or_config('DB_PORT', 'database.mysql.port', db_config.port))
        db_user = self.config_manager._get_env_or_config('MYSQL_USERNAME', 'database.mysql.username', self.config_manager._get_env_or_config('DB_USERNAME', 'database.mysql.username', db_config.username))
        db_pass = self.config_manager._get_env_or_config('MYSQL_PASSWORD', 'database.mysql.password', self.config_manager._get_env_or_config('DB_PASSWORD', 'database.mysql.password', db_config.password))
        # 非品牌车系服务：数据库名仅使用 MYSQL_DATABASE/DB_DATABASE → database.mysql.database
        db_name = self.config_manager._get_env_or_config('MYSQL_DATABASE', 'database.mysql.database', self.config_manager._get_env_or_config('DB_DATABASE', 'database.mysql.database', db_config.database))
        db_config.host = db_host
        db_config.port = int(db_port)
        db_config.username = db_user
        db_config.password = db_pass
        db_config.database = db_name
        self.mysql_client = MySQLClient(db_config)
        await self.mysql_client.initialize()
        
        # 初始化文本处理器（包含内置否定词）
        self.text_processor = TextProcessor()
        await self.text_processor.initialize()
    
    async def match_entity_exact(self, text: str) -> List[Dict[str, Any]]:
        """精确匹配主体"""
        try:
            # 从ES中精确搜索主体
            search_results = await self.es_client.search_entities_in_text(text)
            
            # 转换为兼容格式
            entity_matches = []
            for result in search_results:
                entity_match = {
                    'entity_id': result.source.get('entity_id'),
                    'sim_entity': result.source.get('sim_entity'),
                    'normalized_entity': result.source.get('normalized_entity'),
                    'standard_opinion_id_list': result.source.get('standard_opinion_id_list', []),
                    'confidence': max(result.score, 1.0)  # 确保置信度至少为1.0
                }
                entity_matches.append(entity_match)
            
            self.structured_logger.log_elasticsearch_operation(
                'exact_search', 'entity_synonyms', len(entity_matches), 0.1,
                entity=entity_matches[0].get('sim_entity') if entity_matches else None
            )
            
            return entity_matches
            
        except Exception as e:
            self.logger.error(f"Error in exact entity matching: {e}")
            return []
    
    async def match_description_exact(self, standard_opinion_id: List[str]) -> List[Dict[str, Any]]:
        """在指定标准观点下精确匹配描述"""
        try:
            # 从ES中精确搜索描述
            search_results = await self.es_client.search_exact_descriptions(standard_opinion_id)
            
            # 转换为兼容格式
            description_matches = []
            for result in search_results:
                desc_match = {
                    'description_id': result.source.get('description_id'),
                    'sim_description': result.source.get('sim_description'),
                    'normalized_description': result.source.get('normalized_description'),
                    'standard_opinion_id': result.source.get('standard_opinion_id'),
                    'confidence': result.score
                }
                description_matches.append(desc_match)
            
            self.structured_logger.log_elasticsearch_operation(
                'exact_search', 'description_synonyms', len(description_matches), 0.1,
                opinion_ids_count=len(standard_opinion_id)
            )
            
            return description_matches
            
        except Exception as e:
            self.logger.error(f"Error in exact description matching: {e}")
            return []
    
    async def rule_match_single(
        self, 
        text: str, 
        entity: Dict[str, Any], 
    ) -> List[RuleMatchResult]:
        """单个主体的规则匹配 - 返回所有匹配结果"""
        try:
            self.logger.info(f" [RuleMatcher] 开始规则匹配，主体: {entity}")
            
            # 1. 从entity字典中获取标准观点ID列表
            standard_opinion_ids = entity.get('standard_opinion_id_list', [])
            entity_name = entity.get('sim_entity')
            
            self.logger.info(f"🔍 [RuleMatcher] 提取的标准观点ID: {standard_opinion_ids}")
            self.logger.info(f" [RuleMatcher] 主体名称: {entity_name}")
            self.logger.info(f"🔍 [RuleMatcher] 待匹配文本: {text}")
            
            if not standard_opinion_ids:
                self.logger.warning("⚠️ [RuleMatcher] 没有找到标准观点ID")
                return []
            
            all_matches = []  # 存储所有匹配结果
            matched_descriptions = set()  # 用于去重，记录已匹配的描述
            
            # 2. 遍历每个标准观点ID，查找匹配的描述
            for i, standard_opinion_id in enumerate(standard_opinion_ids):
                self.logger.info(f" [RuleMatcher] 处理标准观点 {i+1}: {standard_opinion_id}")
                
                description_matches = await self.match_description_exact([str(standard_opinion_id)])
                
                if description_matches:
                    self.logger.info(f"✅ [RuleMatcher] 标准观点 {standard_opinion_id} 找到 {len(description_matches)} 条描述匹配")
                    
                    # 3. 遍历每个描述匹配结果
                    for j, desc_match in enumerate(description_matches):
                        self.logger.info(f" [RuleMatcher] 处理描述匹配 {j+1}: {desc_match}")
                        
                        sim_description = desc_match.get('sim_description')
                        normalized_description = desc_match.get('normalized_description')
                        description_id = desc_match.get('description_id')
                        
                        self.logger.info(f" [RuleMatcher] 相似描述: '{sim_description}'")
                        self.logger.info(f"🔍 [RuleMatcher] 标准化描述: '{normalized_description}'")
                        
                        # 3. 在原文中查找描述
                        found_description = None
                        if sim_description and sim_description in text:
                            found_description = sim_description
                            self.logger.info(f"✅ [RuleMatcher] 在原文中找到相似描述: '{sim_description}'")
                        elif normalized_description and normalized_description in text:
                            found_description = normalized_description
                            self.logger.info(f"✅ [RuleMatcher] 在原文中找到标准化描述: '{normalized_description}'")
                        else:
                            self.logger.info(f"❌ [RuleMatcher] 在原文中未找到描述")
                            continue
                        
                        # 检查是否已经匹配过这个描述
                        description_key = f"{entity_name}_{found_description}"
                        if description_key in matched_descriptions:
                            self.logger.info(f"⚠️ [RuleMatcher] 描述 '{found_description}' 已经匹配过，跳过")
                            continue
                        
                        # 4. 检查否定词
                        #获取否定词表
                        negation_words = await self.mysql_client.get_negation_words()
                        self.logger.info(f"negation_words: {negation_words}")
                        
                        negation_ok = not self.text_processor._has_negation_between(text, entity_name, found_description, negation_words)
                        if negation_ok:
                            self.logger.info(f"✅ [RuleMatcher] 否定词检查通过")
                        else:
                            self.logger.info(f"❌ [RuleMatcher] 否定词检查未通过，主体和描述之间有否定词")
                        
                        # 5. 检查距离和标点符号
                        distance_ok = self.text_processor.check_distance_and_punctuation(text, entity_name, found_description)
                        if distance_ok:
                            self.logger.info(f"✅ [RuleMatcher] 距离和标点符号检查通过")
                        else:
                            self.logger.info(f"❌ [RuleMatcher] 距离和标点符号检查未通过")
                        
                        # 6. 所有检查通过，记录匹配结果
                        if distance_ok and negation_ok:
                            self.logger.info(f"✅ [RuleMatcher] 所有检查通过，记录匹配结果")
                            
                            # 标记描述已匹配
                            matched_descriptions.add(description_key)

                            # 改为从 MySQL 的 opinion_synonym 表获取标准观点信息
                            standard_opinion_row = await self.mysql_client.get_opinion_synonym(
                                subject_text=entity_name,
                                description_text=found_description,
                                standard_opinion_id=standard_opinion_id
                            )
                            self.logger.info(f"standard_opinion_row: {standard_opinion_row}")
                            # 构建匹配结果
                            match_result = {
                                'entity_id': entity['entity_id'],
                                'entity_name': entity_name,
                                'description_id': description_id,
                                'description': found_description,
                                'normalized_opinion_id': str(standard_opinion_row.get('opinion_id', '')) if standard_opinion_row else '',
                                'normalized_opinion': standard_opinion_row.get('opinion', '') if standard_opinion_row else '',
                                'standard_opinion_id': standard_opinion_id,
                                'standard_opinion': standard_opinion_row.get('standard_opinion', '') if standard_opinion_row else '',
                                'confidence': entity['confidence'] * desc_match['confidence']
                            }
                            all_matches.append(match_result)
                        else:
                            self.logger.info(f"❌ [RuleMatcher] 检查未通过: distance_ok={distance_ok}, negation_ok={negation_ok}")
                else:
                    self.logger.info(f"❌ [RuleMatcher] 标准观点 {standard_opinion_id} 没有找到描述匹配")
            
            self.logger.info(f"✅ [RuleMatcher] 总共找到 {len(all_matches)} 个规则匹配")
            return all_matches
            
        except Exception as e:
            self.logger.error(f"❌ [RuleMatcher] 规则匹配时发生错误: {e}")
            import traceback
            self.logger.error(f"❌ [RuleMatcher] 错误堆栈: {traceback.format_exc()}")
            return []
    
    async def rule_match_text(self, text: str) -> List[OpinionMatch]:
        """对整个文本进行规则匹配"""
        try:
            self.logger.info(f"=== Starting rule matching for text: {text} ===")
            # 1. 提取主体
            entity_matches = await self.match_entity_exact(text)
            self.logger.info(f"Entity matches found: {len(entity_matches)}")
            for i, match in enumerate(entity_matches):
                self.logger.info(f"  Entity {i+1}: {match}")
            
            if not entity_matches:
                self.logger.warning("No entities found in text")
                return []
            
            all_matches = []  # 存储所有匹配结果
            matched_combinations = set()  # 用于去重，记录已匹配的实体-描述组合
            
            for entity_match in entity_matches:
                self.logger.info(f"Processing entity: {entity_match}")
                # 2. 对每个主体进行规则匹配
                rule_results = await self.rule_match_single(text, entity_match)
                self.logger.info(f"Rule match results: {rule_results}")
                
                # 处理每个匹配结果
                for rule_result in rule_results:
                    if rule_result: # rule_result是字典，直接检查是否存在
                        # 创建去重键：实体名称 + 描述内容
                        entity_name = rule_result.get('entity_name', '')
                        description = rule_result.get('description', '')
                        combination_key = f"{entity_name}_{description}"
                        
                        # 检查是否已经匹配过这个组合
                        if combination_key in matched_combinations:
                            self.logger.info(f"⚠️ [RuleMatcher] 组合 '{combination_key}' 已经匹配过，跳过")
                            continue
                        
                        # 标记组合已匹配
                        matched_combinations.add(combination_key)
                        
                        # 创建标准化的主体信息
                        normalized_subject = NormalizedSubject(
                            id=str(rule_result.get('entity_id', '')),
                            text=rule_result.get('entity_name', ''),
                            score=1.0  # 直接设置为1.0，因为是精确匹配
                        )
                        
                        # 创建标准化的描述信息
                        normalized_description = NormalizedDescription(
                            id=str(rule_result.get('description_id', '')),
                            text=rule_result.get('description', ''),
                            score=1.0
                        )
                        
                        # 创建标准化的观点信息
                        normalized_opinion = NormalizedOpinion(
                            id=str(rule_result.get('normalized_opinion_id', '')),
                            text=rule_result.get('normalized_opinion', ''),
                            score=1.0
                        )
                        
                        # 创建标准观点信息
                        std_viewpoint = StandardViewpoint(
                            id=rule_result.get('standard_opinion_id', ''),
                            text=rule_result.get('standard_opinion', '')
                        )
                        
                        match = OpinionMatch(
                            original_text=text,
                            original_entity=rule_result.get('entity_name', ''),
                            original_description=rule_result.get('description', ''),
                            normalized_subject=normalized_subject,
                            normalized_description=normalized_description,
                            normalized_opinion=normalized_opinion,
                            std_viewpoint=std_viewpoint,
                            match_type="exact"
                        )
                        all_matches.append(match)
            
            self.logger.info(f"✅ [RuleMatcher] 总共找到 {len(all_matches)} 个匹配结果")
            
            self.structured_logger.log_business_event(
                'rule_matching_completed',
                text_length=len(text),
                entity_matches_count=len(entity_matches),
                matches_found=len(all_matches)
            )
            
            return all_matches
            
        except Exception as e:
            self.logger.error(f"Error in text rule matching: {e}")
            return []
    
    async def batch_rule_match(self, texts: List[str]) -> List[List[OpinionMatch]]:
        """批量规则匹配"""
        try:
            results = []
            for text in texts:
                matches = await self.rule_match_text(text)
                results.append(matches)
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error in batch rule matching: {e}")
            return []
    
    async def rule_match_pairs(self, entity_desc_pairs: List[Tuple[str, str, str]]) -> List[Dict]:
        """对主体-描述对进行规则匹配"""
        try:
            matches = []
            
            for entity, description, original_text in entity_desc_pairs:
                # 构造临时文本用于距离检查（简化版本）
                text = f"{entity} {description}"
                
                # 对单个主体-描述对进行规则匹配
                rule_result = await self.rule_match_single(text, entity)
                
                if rule_result and rule_result[0]: # rule_result is (matched, match_result, std_opinion_id, confidence)
                    # 获取标准观点信息
                    standard_opinion = await self.mysql_client.get_standard_opinion(
                        rule_result[2]
                    )
                    
                    if standard_opinion:
                        match = {
                            "original_text": original_text,
                            "original_entity": entity,
                            "original_description": description,
                            "standard_entity": standard_opinion.standard_entity,
                            "standard_description": standard_opinion.standard_description,
                            "standard_opinion_id": rule_result[2],
                            "confidence": rule_result[3],
                            "match_type": "rule"
                        }
                        matches.append(match)
            
            self.structured_logger.log_business_event(
                'rule_pairs_matching_completed',
                pairs_count=len(entity_desc_pairs),
                matches_found=len(matches)
            )
            
            return matches
            
        except Exception as e:
            self.logger.error(f"Error in rule pairs matching: {e}")
            return []
    
    async def start_consumers(self):
        """启动消费者（规则匹配服务通常不需要消费者）"""
        self.logger.info("Rule matcher service: no consumers to start")
    
    async def _service_specific_health_check(self) -> dict:
        """服务特定的健康检查"""
        return {
            'database': 'connected',
            'elasticsearch': 'connected',
            'text_processor': 'ready' if self.text_processor else 'not_ready'
        }
    
    async def _shutdown_service_specific(self):
        """服务特定的关闭逻辑"""
        if hasattr(self, 'es_client') and self.es_client:
            await self.es_client.close()
        
        if hasattr(self, 'mysql_client') and self.mysql_client:
            await self.mysql_client.close()


    def _add_custom_routes(self, app: FastAPI):
        """添加服务特定的路由"""
        @app.post("/match/text")
        async def match_text(request: dict):
            """匹配文本中的观点"""
            try:
                text = request.get('text', '')
                if not text:
                    raise HTTPException(status_code=400, detail="Text is required")
                
                matches = await self.rule_match_text(text)
                return APIResponse(
                    success=True, 
                    message="Rule matching completed", 
                    data={'matches': [match.model_dump() for match in matches]}
                )
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @app.post("/match/batch")
        async def match_batch(request: dict):
            """批量匹配文本"""
            try:
                texts = request.get('texts', [])
                if not texts:
                    raise HTTPException(status_code=400, detail="Texts are required")
                
                results = await self.batch_rule_match(texts)
                return APIResponse(
                    success=True, 
                    message="Batch rule matching completed", 
                    data={'results': [[match.model_dump() for match in matches] for matches in results]}
                )
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @app.post("/match/entity")
        async def match_entity(request: dict):
            """匹配主体"""
            try:
                entity = request.get('entity', '')
                if not entity:
                    raise HTTPException(status_code=400, detail="Entity is required")
                
                matches = await self.match_entity_exact(entity)
                return APIResponse(
                    success=True, 
                    message="Entity matching completed", 
                    data={'matches': [match.model_dump() for match in matches]}
                )
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @app.post("/match/pairs")
        async def match_pairs(request: dict):
            """匹配主体-描述对"""
            try:
                pairs = request.get('pairs', [])
                if not pairs:
                    raise HTTPException(status_code=400, detail="Pairs are required")
                
                # 转换为元组列表
                entity_desc_pairs = [(pair[0], pair[1], pair[2]) for pair in pairs if len(pair) >= 3]
                
                matches = await self.rule_match_pairs(entity_desc_pairs)
                return APIResponse(
                    success=True, 
                    message="Rule pairs matching completed", 
                    data={'matches': matches}
                )
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))


# 创建服务实例
service = RuleMatcherService()

# 创建FastAPI应用
app = service.create_fastapi_app(
    title="Rule Matcher Service",
    description="规则匹配服务"
)


if __name__ == "__main__":
    service.run_with_app_string("main:app")