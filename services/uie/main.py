"""
UIE模型服务
负责使用UIE模型进行主体和描述的抽取
"""
import asyncio
import signal
import sys
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import json
import httpx

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from fastapi import FastAPI, HTTPException
from contextlib import asynccontextmanager
import uvicorn

from shared.base import ModelService
from shared.utils.text_processor import TextProcessor
from shared.models.schemas import (
    CategoryMessage, ModelResultMessage, ErrorMessage, APIResponse, SourceData
)


class UIEService(ModelService):
    """UIE模型服务"""
    
    def __init__(self):
        super().__init__('uie')
        self.text_processor = None
        self.vector_config = self.config_manager.get_vector_config()
        
        # 通用服务地址（从配置文件读取）
        rule_matcher_config = self.config_manager.get_service_config('rule-matcher')
        vector_matcher_config = self.config_manager.get_service_config('vector-matcher')
        
        # 修复服务连接地址：将 0.0.0.0 转换为 localhost，因为 0.0.0.0 不是有效的客户端连接地址
        rule_matcher_host = rule_matcher_config.host if rule_matcher_config.host != '0.0.0.0' else '*************'
        vector_matcher_host = vector_matcher_config.host if vector_matcher_config.host != '0.0.0.0' else '*************'
        
        self.rule_matcher_url = f"http://{rule_matcher_host}:{rule_matcher_config.port}"
        self.vector_matcher_url = f"http://{vector_matcher_host}:{vector_matcher_config.port}"
        # 批量处理配置
        self.batch_size = self.model_config.get('batch_size', 32)  # 批量大小
        self.batch_timeout = self.model_config.get('batch_timeout', 5.0)  # 批量超时时间（秒）
        self.enable_batch_processing = self.model_config.get('enable_batch_processing', True)  # 是否启用批量处理
        
        # 批量消息缓存
        self.message_batch = []  # 存储待处理的消息
        self.current_batch_text_count = 0  # 当前批量的文本总数
        self.batch_lock = asyncio.Lock()  # 批量操作锁
        self.batch_start_time = None  # 批量开始时间
        self.timeout_check_task = None  # 后台超时检查任务
    
    async def _initialize_service_specific(self):
        """UIE服务特定的初始化逻辑"""
        # 初始化文本处理器（仅文本处理功能）
        self.text_processor = TextProcessor()
        await self.text_processor.initialize()
        self.logger.info("Text processor initialized")
        
        # 记录批量处理配置
        self.logger.info(f"UIE批量处理配置: batch_size={self.batch_size}, batch_timeout={self.batch_timeout}, enabled={self.enable_batch_processing}")
        
        # 记录服务地址配置
        self.logger.info(f"Rule Matcher URL: {self.rule_matcher_url}")
        self.logger.info(f"Vector Matcher URL: {self.vector_matcher_url}")
        
        # 测试服务连接
        await self._test_service_connections()
        
        # 启动后台超时检查任务
        if self.enable_batch_processing:
            self.timeout_check_task = asyncio.create_task(self._batch_timeout_checker())
            self.logger.info("Batch timeout checker started")
    

    async def call_vector_matcher(self, entity_description_pairs: List[Dict]) -> List[Dict]:
        """调用向量匹配服务"""
        try:
            # 将实体-描述对转换为向量匹配服务期望的格式，包含topic标记
            pairs = []
            
            for pair in entity_description_pairs:
                original_text = pair['original_text']
                entity_text = pair['entity']['text']
                opinion_text = pair['opinion']['text']
                message_index = pair.get('message_index')  # topic标记
                
                # 扩展格式：[original_text, entity_text, opinion_text, message_index]
                pairs.append([original_text, entity_text, opinion_text, message_index])
            
            self.logger.info(f"Calling Vector Matcher at {self.vector_matcher_url} with {len(pairs)} pairs")
            
            # 使用配置优化的httpx客户端，与UIE API调用保持一致
            async with httpx.AsyncClient(
                timeout=httpx.Timeout(30.0, connect=10.0),
                limits=httpx.Limits(
                    max_connections=1,
                    max_keepalive_connections=0
                ),
                http2=False,
                follow_redirects=True,
                trust_env=False,
                verify=False
            ) as client:
                response = await client.post(
                    f"{self.vector_matcher_url}/match/pairs",
                    json={"pairs": pairs}
                )
            
                self.logger.info(f"Vector Matcher response status: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    matches = result.get('data', {}).get('matches', [])
                    return matches  # 向量匹配服务返回的结果应该已经包含message_index
                else:
                    self.logger.warning(f"Vector matcher returned {response.status_code}")
                    return []
        except Exception as e:
            self.logger.error(f"Error calling vector matcher at {self.vector_matcher_url}: {e}")
            self.logger.error(f"Vector matcher connection details: host={self.vector_matcher_url.split('//')[1].split(':')[0]}, port={self.vector_matcher_url.split(':')[-1]}")
            return []
    
    def _build_uie_schema(self) -> Dict[str, Any]:
        """构建UIE抽取schema"""
        return {
            "评价对象": [
                "大灯", "内饰", "外观", "座椅", "发动机", "变速箱", "悬挂", "轮胎",
                "音响", "空调", "方向盘", "颜值", "设计", "质量", "性能", "舒适性",
                "操控", "油耗", "空间", "配置", "动力", "刹车", "转向"
            ],
            "观点描述": [
                "好看", "难看", "漂亮", "丑", "美观", "时尚", "老气",
                "好", "不好", "棒", "差", "优秀", "糟糕", "满意", "失望",
                "舒服", "不舒服", "舒适", "硬", "软", "宽敞", "狭窄",
                "省油", "费油", "经济", "耗油", "节能", "强劲", "有力", "澎湃"
            ]
        }
    
    async def call_uie_api(self, texts: List[str]) -> List[Dict[str, Any]]:
        """调用UIE API进行抽取"""
        try:
            # 构建请求数据
            request_data = {
                "texts": texts
            }
            self.logger.info(f"Calling UIE API: {self.model_config['api_url']}, request_data={request_data}")
            
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json",
                "User-Agent": "UIE-Service/1.0",
                "Connection": "close"
            }
            
            if self.model_config.get('api_key'):
                headers["Authorization"] = f"Bearer {self.model_config['api_key']}"
            
            self.logger.info(f"Calling UIE API: {self.model_config['api_url']}")
            
            # 使用配置优化的httpx客户端
            async with httpx.AsyncClient(
                timeout=httpx.Timeout(30.0, connect=10.0),
                limits=httpx.Limits(
                    max_connections=1,
                    max_keepalive_connections=0
                ),
                http2=False,
                follow_redirects=True,
                trust_env=False,
                verify=False
            ) as client:
                response = await client.post(
                    self.model_config['api_url'],
                    json=request_data,
                    headers=headers
                )
                
                if response.status_code == 200:
                    response_data = response.json()
                    if response_data.get('code') == 200:
                        return response_data.get('data', [])
                    else:
                        raise Exception(f"UIE API返回错误: {response_data.get('message', 'Unknown error')}")
                else:
                    self.logger.error(f"UIE API请求失败: {response.status_code}")
                    raise Exception(f"UIE API请求失败 ({response.status_code}): {response.text}")
                        
        except Exception as e:
            self.logger.error(f"UIE API调用失败: {e}")
            raise
    
    def _extract_entity_description_pairs(self, uie_results: List[Dict], original_texts: List[str] = None) -> List[Dict]:
        """从UIE结果中提取主体-描述对，保留位置信息"""
        pairs = []
        
        # uie_results和original_texts一一对应
        for text_idx, result_group in enumerate(uie_results):
            # 获取对应的原始文本用于验证
            current_original_text = ""
            if original_texts and text_idx < len(original_texts):
                current_original_text = original_texts[text_idx]
            
            # 处理UIE结果格式：可能是直接的dict，也可能是包含dict的list
            results_to_process = result_group if isinstance(result_group, list) else [result_group]
            
            for result in results_to_process:
                if not isinstance(result, dict) or '评价对象' not in result:
                    continue
                    
                entities = result['评价对象']
                for entity_info in entities:
                    if not isinstance(entity_info, dict):
                        continue
                        
                    entity_text = entity_info.get('text', '').strip()
                    if not entity_text:
                        continue
                        
                    entity_start = entity_info.get('start')
                    entity_end = entity_info.get('end')
                    relations = entity_info.get('relations', {})
                    
                    # 提取观点
                    opinions = relations.get('观点', [])
                    for opinion in opinions:
                        if not isinstance(opinion, dict):
                            continue
                            
                        opinion_text = opinion.get('text', '').strip()
                        if not opinion_text:
                            continue
                            
                        # 验证实体和观点是否存在于原始文本中
                        if current_original_text:
                            if entity_text not in current_original_text or opinion_text not in current_original_text:
                                self.logger.info(f"过滤无效抽取 (文本{text_idx}): 实体='{entity_text}' 观点='{opinion_text}' 不在原文中")
                                continue
                        
                        # 添加有效的实体-观点对
                        pairs.append({
                            'original_text': current_original_text,
                            'entity': {
                                'text': entity_text,
                                'start': entity_start,
                                'end': entity_end
                            },
                            'opinion': {
                                'text': opinion_text,
                                'start': opinion.get('start'),
                                'end': opinion.get('end')
                            }
                        })
        
        return pairs
    
    def _extract_entity_description_pairs_with_mapping(self, uie_results: List[Dict], original_texts: List[str], text_to_message_mapping: List[int]) -> List[Dict]:
        """从UIE结果中提取主体-描述对，保留位置信息和消息映射"""
        pairs = []
        
        # uie_results和original_texts一一对应
        for text_idx, result_group in enumerate(uie_results):
            # 获取对应的原始文本和消息索引
            current_original_text = ""
            message_index = None
            if original_texts and text_idx < len(original_texts):
                current_original_text = original_texts[text_idx]
            if text_to_message_mapping and text_idx < len(text_to_message_mapping):
                message_index = text_to_message_mapping[text_idx]
            
            # 处理UIE结果格式：可能是直接的dict，也可能是包含dict的list
            results_to_process = result_group if isinstance(result_group, list) else [result_group]
            
            for result in results_to_process:
                if not isinstance(result, dict) or '评价对象' not in result:
                    continue
                    
                entities = result['评价对象']
                for entity_info in entities:
                    if not isinstance(entity_info, dict):
                        continue
                        
                    entity_text = entity_info.get('text', '').strip()
                    if not entity_text:
                        continue
                        
                    entity_start = entity_info.get('start')
                    entity_end = entity_info.get('end')
                    relations = entity_info.get('relations', {})
                    
                    # 提取观点
                    opinions = relations.get('观点', [])
                    for opinion in opinions:
                        if not isinstance(opinion, dict):
                            continue
                            
                        opinion_text = opinion.get('text', '').strip()
                        if not opinion_text:
                            continue
                            
                        # 验证实体和观点是否存在于原始文本中
                        if current_original_text:
                            if entity_text not in current_original_text or opinion_text not in current_original_text:
                                self.logger.info(f"过滤无效抽取 (文本{text_idx}): 实体='{entity_text}' 观点='{opinion_text}' 不在原文中")
                                continue
                        
                        # 添加有效的实体-观点对，包含消息映射信息
                        pairs.append({
                            'original_text': current_original_text,
                            'message_index': message_index,  # 新增消息映射
                            'entity': {
                                'text': entity_text,
                                'start': entity_start,
                                'end': entity_end
                            },
                            'opinion': {
                                'text': opinion_text,
                                'start': opinion.get('start'),
                                'end': opinion.get('end')
                            }
                        })
        
        return pairs

    
    def _format_uie_result(self, pairs: List[Dict]) -> Dict[str, Any]:
        """格式化UIE结果为标准格式，包含位置信息"""
        evaluation_objects = []
        
        for pair in pairs:
            entity_text = pair['entity']['text']
            opinion_text = pair['opinion']['text']
            entity_start = pair['entity'].get('start')
            entity_end = pair['entity'].get('end')
            opinion_start = pair['opinion'].get('start')
            opinion_end = pair['opinion'].get('end')
            
            eval_obj = {
                "text": entity_text,
                "start": entity_start,
                "end": entity_end,
                "relations": {
                    "segment": [{"text": ""}],  # 待后续填充
                    "brand_series": [{"text": ""}],  # 待后续填充
                    "sentiment": [{"text": ""}],  # 待后续填充
                    "intent": [{"text": ""}],  # 待后续填充
                    "opinion": [{
                        "text": opinion_text,
                        "start": opinion_start,
                        "end": opinion_end
                    }],
                    "normalized_subject": {
                        "id": f"SUBJ_{hash(entity_text) % 999999:06d}",
                        "text": entity_text,
                        "score": 0.8
                    },
                    "normalized_description": {
                        "id": f"DESC_{hash(opinion_text) % 999999:06d}",
                        "text": opinion_text,
                        "score": 0.8
                    },
                    "normalized_opinion": {
                        "id": f"OP_{hash(entity_text + opinion_text) % 999999:08d}",
                        "text": f"{entity_text}-{opinion_text}"
                    },
                    "std_viewpoint": {
                        "id": f"CA_STD_{hash(entity_text + opinion_text) % 999999:06d}",
                        "text": f"{entity_text}{opinion_text}",
                        "confidence": 0.8
                    }
                }
            }
            evaluation_objects.append(eval_obj)
        
        return evaluation_objects
    
    async def call_rule_matcher_text(self, text: str) -> List[Dict]:
        """调用规则匹配服务处理文本"""
        try:
            self.logger.info(f"Calling Rule Matcher at {self.rule_matcher_url} with text: '{text[:50]}...'")
            
            # 使用配置优化的httpx客户端，与UIE API调用保持一致
            async with httpx.AsyncClient(
                timeout=httpx.Timeout(30.0, connect=10.0),
                limits=httpx.Limits(
                    max_connections=1,
                    max_keepalive_connections=0
                ),
                http2=False,
                follow_redirects=True,
                trust_env=False,
                verify=False
            ) as client:
                response = await client.post(
                    f"{self.rule_matcher_url}/match/text",
                    json={"text": text}
                )
            
                self.logger.info(f"Rule Matcher response status: {response.status_code}")
                if response.status_code == 200:
                    result = response.json()
                    return result.get('data', {}).get('matches', [])
                else:
                    self.logger.warning(f"Rule matcher returned {response.status_code}")
                    return []
        except Exception as e:
            self.logger.error(f"Error calling rule matcher at {self.rule_matcher_url}: {e}")
            self.logger.error(f"Rule matcher connection details: host={self.rule_matcher_url.split('//')[1].split(':')[0]}, port={self.rule_matcher_url.split(':')[-1]}")
            return []

    async def _process_rule_matcher_batch(self, batch_messages: List[CategoryMessage]) -> List[Dict]:
        """批量处理规则匹配，为每个消息分别调用规则匹配服务"""
        all_rule_matches = []
        
        # 为每个消息创建规则匹配任务
        tasks = []
        for message in batch_messages:
            texts = message.raw_split_text if message.raw_split_text else [message.source_data.topic_text]
            combined_text = " ".join(texts)  # 每个消息内部的文本合并
            self.logger.info(f"Rule matcher for message {message.topic_id}: texts={texts}, combined='{combined_text}'")
            task = self.call_rule_matcher_text(combined_text)
            tasks.append(task)
        
        # 并行执行所有规则匹配任务
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果，为每个匹配结果添加原始文本信息
            for msg_idx, (message, result) in enumerate(zip(batch_messages, results)):
                if isinstance(result, Exception):
                    self.logger.error(f"Rule matcher failed for message {message.topic_id}: {result}")
                    continue
                
                # 为每个匹配结果添加原始文本信息
                texts = message.raw_split_text if message.raw_split_text else [message.source_data.topic_text]
                combined_text = " ".join(texts)
                
                for match in result:
                    # 添加原始文本信息，用于后续结果分配
                    match['original_text'] = combined_text
                    match['message_index'] = msg_idx
                    match['topic_id'] = message.topic_id
                    all_rule_matches.append(match)
                    
        except Exception as e:
            self.logger.error(f"Error in rule matcher batch processing: {e}")
        
        return all_rule_matches

    async def _process_uie_vector_path(self, texts: List[str]) -> Dict[str, Any]:
        """处理UIE+向量匹配路径"""
        try:
            # 1. 调用UIE API
            uie_results = await self.call_uie_api(texts)
            
            # 2. 提取主体-描述对
            uie_pairs = self._extract_entity_description_pairs(uie_results, texts)
            
            # 3. 如果有UIE结果，调用向量匹配
            vector_matches = []
            if uie_pairs:
                vector_matches = await self.call_vector_matcher(uie_pairs)
            
            return {
                "uie_pairs": uie_pairs,
                "vector_matches": vector_matches
            }
            
        except Exception as e:
            self.logger.error(f"Error in UIE+Vector path: {e}")
            return {"uie_pairs": [], "vector_matches": []}

    async def _process_uie_vector_path_with_mapping(self, texts: List[str], text_to_message_mapping: List[int]) -> Dict[str, Any]:
        """处理UIE+向量匹配路径，带消息映射信息"""
        try:
            # 1. 调用UIE API
            uie_results = await self.call_uie_api(texts)
            
            # 2. 提取主体-描述对，并添加消息映射信息
            uie_pairs = self._extract_entity_description_pairs_with_mapping(uie_results, texts, text_to_message_mapping)
            
            # 3. 如果有UIE结果，调用向量匹配
            vector_matches = []
            if uie_pairs:
                vector_matches = await self.call_vector_matcher(uie_pairs)
                # message_index信息已经在call_vector_matcher中添加了
            
            return {
                "uie_pairs": uie_pairs,
                "vector_matches": vector_matches
            }
            
        except Exception as e:
            self.logger.error(f"Error in UIE+Vector path with mapping: {e}")
            return {"uie_pairs": [], "vector_matches": []}

    def _merge_two_path_results(
        self, 
        rule_matches: List[Dict], 
        uie_pairs: List[Dict], 
        vector_matches: List[Dict]
    ) -> Dict[str, Any]:
        """合并两路处理的结果"""
        evaluation_objects = []
        
        # 创建一个字典来存储所有匹配结果，避免重复
        all_matches = {}
        
        # 添加规则匹配结果 (路径1)
        for match in rule_matches:
            entity_id = match.get('normalized_subject', '').get('id', '')
            description_id = match.get('normalized_description', '').get('id', '')
            entity = match.get('original_entity', '')
            description = match.get('original_description', '')
            if entity_id and description_id:
                key = f"{entity_id}_{description_id}"
                all_matches[key] = {
                    "original_text": match.get('original_text', ''),
                    "entity": entity,
                    "description": description,
                    "source": "rule",
                    "standard_entity": match.get('standard_entity', entity),
                    "standard_description": match.get('standard_description', description),
                    "rule_match_data": match  # 添加向量匹配的完整数据
                }
        
        # 只添加向量匹配结果 (路径2的向量匹配部分)
        # 向量匹配的输入是UIE的输出，所以只有向量匹配成功的结果才加入最终结果
        self.logger.info(f"vector_matches: {vector_matches}")
        for match in vector_matches:
            entity_id = match.get('normalized_subject', '').get('id', '')
            description_id = match.get('normalized_description', '').get('id', '')
            entity = match.get('original_entity', '')
            description = match.get('original_description', '')
            key = f"{entity_id}_{description_id}"
            if entity_id and description_id and key not in all_matches:
                # 向量匹配结果会新增到结果中,如果与规则匹配结果有相同的key,则不新增
                all_matches[key] = {
                    "original_text": match.get('original_text', ''),
                    "entity": entity,
                    "description": description,
                    "source": "vector",
                    "confidence": match.get('confidence', 0.7),
                    "standard_entity": match.get('standard_entity', entity),
                    "standard_description": match.get('standard_description', description),
                    "vector_match_data": match,  # 添加向量匹配的完整数据
                    # 从UIE pairs中获取位置信息
                    "entity_start": self._get_entity_position(uie_pairs, entity, description, 'entity'),
                    "entity_end": self._get_entity_position(uie_pairs, entity, description, 'entity', 'end'),
                    "opinion_start": self._get_entity_position(uie_pairs, entity, description, 'opinion'),
                    "opinion_end": self._get_entity_position(uie_pairs, entity, description, 'opinion', 'end')
                }
        
        # 构建评价对象
        for match_info in all_matches.values():
            if match_info["source"] == "vector":
                # 向量匹配结果使用向量匹配的完整数据结构
                vector_data = match_info["vector_match_data"]
                eval_obj = {
                    "original_text": match_info.get("original_text", ''),
                    "aspect": {
                        "text": match_info.get("entity"),
                        "start": match_info.get("entity_start"),
                        "end": match_info.get("entity_end")
                        },
                    "segment": "",  # 待intent服务填充
                    "brand": "",  # 待brand-attribution服务填充
                    "series": "",  # 待brand-attribution服务填充
                    "car_level1": "", # 待brand-attribution服务填充
                    "car_level2": "", # 待brand-attribution服务填充
                    "sentiment": "",  # 待intent服务填充
                    "intent": "",  # 待intent服务填充
                    "description": {
                        "text": match_info.get("description"),
                        "start": match_info.get("opinion_start"),
                        "end": match_info.get("opinion_end")
                        },
                    "normalized_subject": vector_data['normalized_subject'],
                    "normalized_description": vector_data['normalized_description'],
                    "normalized_opinion": vector_data['normalized_opinion'],
                    "std_viewpoint": vector_data['std_viewpoint']
                }
            else:
                rule_data = match_info["rule_match_data"]
                eval_obj = {
                    "original_text": match_info.get("original_text", ''),
                    "aspect": {
                        "text": match_info.get("entity"),
                        "start": match_info.get("entity_start"),
                        "end": match_info.get("entity_end")
                    },
                    "segment": "",  # 待intent服务填充
                    "brand": "",  # 待brand-attribution服务填充
                    "series": "",  # 待brand-attribution服务填充
                    "car_level1": "", # 待brand-attribution服务填充
                    "car_level2": "", # 待brand-attribution服务填充
                    "sentiment": "",  # 待intent服务填充
                    "intent": "",  # 待intent服务填充
                    "description": {
                        "text": match_info.get("description"),
                        "start": match_info.get("opinion_start"),
                        "end": match_info.get("opinion_end")
                    },
                    "normalized_subject": rule_data['normalized_subject'],
                    "normalized_description": rule_data['normalized_description'],
                    "normalized_opinion": rule_data['normalized_opinion'],
                    "std_viewpoint": rule_data['std_viewpoint']
                }
            evaluation_objects.append(eval_obj)
        
        return evaluation_objects
    
    def _get_entity_position(self, uie_pairs: List[Dict], entity: str, description: str, entity_type: str, position: str = 'start') -> Optional[int]:
        """从UIE pairs中获取指定实体的位置信息"""
        for pair in uie_pairs:
            if (pair['entity']['text'] == entity and 
                pair['opinion']['text'] == description):
                if entity_type == 'entity':
                    return pair['entity'].get(position)
                elif entity_type == 'opinion':
                    return pair['opinion'].get(position)
        return None

    async def _test_service_connections(self):
        """测试Rule Matcher和Vector Matcher服务连接"""
        self.logger.info("Testing service connections...")
        
        # 测试Rule Matcher
        try:
            response = await self.http_client.get(
                f"{self.rule_matcher_url}/health",
                timeout=5
            )
            if response.status_code == 200:
                self.logger.info(f"✅ Rule Matcher connection successful at {self.rule_matcher_url}")
            else:
                self.logger.warning(f"⚠️ Rule Matcher responded with status {response.status_code}")
        except Exception as e:
            self.logger.error(f"❌ Rule Matcher connection failed at {self.rule_matcher_url}: {e}")
        
        # 测试Vector Matcher
        try:
            response = await self.http_client.get(
                f"{self.vector_matcher_url}/health",
                timeout=5
            )
            if response.status_code == 200:
                self.logger.info(f"✅ Vector Matcher connection successful at {self.vector_matcher_url}")
            else:
                self.logger.warning(f"⚠️ Vector Matcher responded with status {response.status_code}")
        except Exception as e:
            self.logger.error(f"❌ Vector Matcher connection failed at {self.vector_matcher_url}: {e}")
        
        self.logger.info("Service connection tests completed")

    async def _batch_timeout_checker(self):
        """后台任务：定期检查批量超时"""
        while True:
            try:
                # 每秒1秒检查一次
                await asyncio.sleep(1.0)
                
                # 检查批量是否超时
                if self.message_batch and self.batch_start_time:
                    import time
                    elapsed_time = time.time() - self.batch_start_time
                    if elapsed_time >= self.batch_timeout:
                        self.logger.info(f"Background timeout check: batch timeout triggered ({elapsed_time:.1f}s >= {self.batch_timeout}s), processing {len(self.message_batch)} messages")
                        async with self.batch_lock:
                            if self.message_batch:  # 再次检查，防止竞态条件
                                await self._process_message_batch()
                            
            except asyncio.CancelledError:
                self.logger.info("Batch timeout checker task cancelled")
                break
            except Exception as e:
                self.logger.error(f"Error in batch timeout checker: {e}")
                # 继续运行，不要因为单次错误停止检查

    async def _process_message_batch(self):
        """处理当前批量的消息"""
        self.logger.info(f"_process_message_batch called, current batch: {len(self.message_batch)} messages, {self.current_batch_text_count} texts")
        if not self.message_batch:
            self.logger.info("_process_message_batch: message_batch is empty, returning")
            return
            
        try:
            batch_messages = self.message_batch.copy()
            batch_text_count = self.current_batch_text_count
            
            # 清空批量缓存和计数器
            self.message_batch.clear()
            self.current_batch_text_count = 0
            self.batch_start_time = None  # 重置开始时间
            
            # 合并所有texts
            all_texts = []
            text_to_message_mapping = []  # 记录每个text属于哪个消息
            
            for msg_idx, message in enumerate(batch_messages):
                texts = message.raw_split_text if message.raw_split_text else [message.source_data.topic_text]
                self.logger.info(f"Batch message {msg_idx} ({message.topic_id}): texts={texts}")
                for text in texts:
                    all_texts.append(text)
                    text_to_message_mapping.append(msg_idx)
            
            self.logger.info(f"Processing batch: {len(batch_messages)} messages, {len(all_texts)} texts total (expected: {batch_text_count})")
            
            # 执行批量处理
            start_time = asyncio.get_event_loop().time()
            
            # 并行执行两路处理
            # 路径1: 规则匹配(分别处理每个消息的文本)
            rule_task = asyncio.create_task(self._process_rule_matcher_batch(batch_messages))
            # 路径2: UIE抽取 + 向量匹配
            uie_vector_task = asyncio.create_task(self._process_uie_vector_path_with_mapping(all_texts, text_to_message_mapping))
            
            # 等待两路任务完成
            rule_matches, uie_vector_results = await asyncio.gather(
                rule_task, uie_vector_task, return_exceptions=True
            )
            
            duration = asyncio.get_event_loop().time() - start_time
            
            # 处理可能的异常
            if isinstance(rule_matches, Exception):
                self.logger.error(f"Rule matcher call failed: {rule_matches}")
                rule_matches = []
            
            if isinstance(uie_vector_results, Exception):
                self.logger.error(f"UIE+Vector path failed: {uie_vector_results}")
                uie_vector_results = {"uie_pairs": [], "vector_matches": []}
            
            # 按消息分别合并两路结果
            await self._process_and_distribute_batch_results(
                batch_messages,
                rule_matches,
                uie_vector_results.get("uie_pairs", []),
                uie_vector_results.get("vector_matches", []),
                text_to_message_mapping,
                all_texts
            )
            
            self.structured_logger.log_business_event(
                'batch_processing_completed',
                batch_size=len(batch_messages),
                total_texts=len(all_texts),
                processing_duration=duration
            )
            
        except Exception as e:
            self.logger.error(f"Error processing message batch: {e}")
            # 发送错误消息给所有批量中的消息
            for message in self.message_batch:
                await self._send_error_message(message, str(e))

    async def _process_and_distribute_batch_results(
        self,
        batch_messages: List[CategoryMessage],
        rule_matches: List[Dict],
        uie_pairs: List[Dict],
        vector_matches: List[Dict],
        text_to_message_mapping: List[int],
        all_texts: List[str]
    ):
        """按消息分别处理和分配批量结果"""
        try:
            # 为每个消息分别处理结果
            for msg_idx, message in enumerate(batch_messages):
                try:
                    # 获取属于当前消息的规则匹配结果
                    message_rule_matches = [
                        match for match in rule_matches 
                        if match.get('message_index') == msg_idx
                    ]
                    
                    # 使用message_index精确筛选属于当前消息的结果
                    message_texts = message.raw_split_text if message.raw_split_text else [message.source_data.topic_text]
                    
                    message_vector_matches = []
                    message_uie_pairs = []
                    
                    # 从向量匹配结果中筛选属于当前消息的结果
                    for match in vector_matches:
                        if match.get('message_index') == msg_idx:
                            message_vector_matches.append(match)
                    
                    # 从UIE pairs中筛选属于当前消息的结果
                    for pair in uie_pairs:
                        if pair.get('message_index') == msg_idx:
                            message_uie_pairs.append(pair)
                    
                    # 为当前消息合并两路结果
                    message_result = self._merge_two_path_results(
                        message_rule_matches,
                        message_uie_pairs,
                        message_vector_matches
                    )
                    
                    # 发送结果
                    if not message_result:
                        # 没有结果，发送到final_topic
                        final_message = {
                            "nlpParam": {
                                "topic_id": message.topic_id,
                                "source_data": message.source_data.model_dump()
                            },
                            "nlpResult": {
                                "dimensions": []
                            }
                        }
                        
                        self.kafka_client.send_message(
                            self.kafka_client.TOPICS.get('FINAL_TOPIC', 'final_topic'),
                            final_message,
                            key=message.topic_id
                        )
                    else:
                        # 有结果，发送到intent_topic
                        intent_message = {
                            "topic_id": message.topic_id,
                            "source_data": message.source_data.model_dump(),
                            "raw_split_text": message_texts,
                            "cat_type": "uie",
                            "retry_count": message.retry_count,
                            "error_type": message.error_type,
                            "error_msg": message.error_msg,
                            "last_ts": message.last_ts,
                            "result": message_result,
                            "qaresult": message.qaresult
                        }
                        
                        self.kafka_client.send_to_intent_topic(intent_message)
                    
                    self.structured_logger.log_kafka_message(
                        'sent', 'intent_topic', message.topic_id
                    )
                        
                except Exception as e:
                    self.logger.error(f"Error processing result for message {message.topic_id}: {e}")
                    await self._send_error_message(message, str(e))
                    
        except Exception as e:
            self.logger.error(f"Error in process_and_distribute_batch_results: {e}")
            # 发送错误消息给所有消息
            for message in batch_messages:
                await self._send_error_message(message, str(e))

    async def _send_error_message(self, message: CategoryMessage, error_msg: str):
        """发送错误消息"""
        try:
            error_message = ErrorMessage(
                topic_id=message.topic_id,
                source_data=message.source_data,
                retry_count=message.retry_count,
                error_type='uie_batch_processing_error',
                error_msg=error_msg,
                last_ts=message.last_ts
            )
            self.kafka_client.send_to_error_topic(error_message)
        except Exception as e:
            self.logger.error(f"Error sending error message for {message.topic_id}: {e}")


    async def process_uie_message(self, message_data: dict):
        """处理UIE消息 - 支持批量处理和单消息处理"""
        try:
            message = CategoryMessage(**message_data)
            self.structured_logger.log_kafka_message(
                'received', 'uie_topic', message.topic_id
            )
            
            self.logger.info(f"收到UIE消息: topic_id={message.topic_id}, batch_processing_enabled={self.enable_batch_processing}")
            
            if self.enable_batch_processing:
                # 批量处理模式
                async with self.batch_lock:
                    # 计算当前消息的文本数量
                    texts = message.raw_split_text if message.raw_split_text else [message.source_data.topic_text]
                    message_text_count = len(texts)
                    
                    self.logger.info(f"Processing message {message.topic_id}: raw_split_text={message.raw_split_text}, topic_text='{message.source_data.topic_text}', final_texts={texts}")
                    
                    # 检查添加这个消息后是否会超过批量大小
                    if self.current_batch_text_count + message_text_count > self.batch_size:
                        # 会超过批量大小，先处理当前批量
                        if self.message_batch:  # 确保当前批量不为空
                            self.logger.info(f"Adding {message_text_count} texts would exceed batch size ({self.batch_size}), processing current batch with {self.current_batch_text_count} texts")
                            await self._process_message_batch()
                    
                    # 添加消息到批量缓存
                    self.message_batch.append(message)
                    self.current_batch_text_count += message_text_count
                    
                    self.logger.info(f"Added message with {message_text_count} texts, current batch: {len(self.message_batch)} messages, {self.current_batch_text_count} texts")
                    
                    # 检查添加后是否达到或超过批量大小
                    if self.current_batch_text_count >= self.batch_size:
                        self.logger.info(f"Batch size reached ({self.current_batch_text_count}/{self.batch_size} texts), processing batch")
                        await self._process_message_batch()
                    else:
                        # 如果这是批量中的第一个消息，记录开始时间
                        if len(self.message_batch) == 1:
                            import time
                            self.batch_start_time = time.time()
                            self.logger.info(f"Started batch timer, current batch: {len(self.message_batch)} messages, {self.current_batch_text_count} texts")
                        else:
                            self.logger.info(f"Message added to existing batch, batch: {len(self.message_batch)} messages, {self.current_batch_text_count} texts")
            else:
                # 单消息处理模式（原有逻辑）
                await self._process_single_message(message)
            
        except Exception as e:
            self.logger.error(f"Error processing UIE message: {e}")
            
            # 发送错误消息
            try:
                message = CategoryMessage(**message_data)
                await self._send_error_message(message, str(e))
            except Exception as parse_error:
                self.logger.error(f"Error parsing message for error handling: {parse_error}")
                # 尝试发送基本错误信息
                try:
                    source_data = message_data.get('source_data', {})
                    if isinstance(source_data, dict):
                        source_data = SourceData(**source_data)
                        
                    error_message = ErrorMessage(
                        topic_id=message_data.get('topic_id', 'unknown'),
                        source_data=source_data,
                        retry_count=message_data.get('retry_count', 0),
                        error_type='uie_message_parsing_error',
                        error_msg=str(e),
                        last_ts=message_data.get('last_ts', '')
                    )
                    self.kafka_client.send_to_error_topic(error_message)
                except Exception as final_error:
                    self.logger.error(f"Failed to send error message: {final_error}")

    async def _process_single_message(self, message: CategoryMessage):
        """处理单个消息（原有的处理逻辑）"""
        try:
            # 获取分句文本
            texts = message.raw_split_text if message.raw_split_text else [message.source_data.topic_text]
            combined_text = " ".join(texts)  # 合并为完整文本
            
            self.logger.info(f"Single message processing {message.topic_id}: raw_split_text={message.raw_split_text}, topic_text='{message.source_data.topic_text}', final_texts={texts}")
            
            # 并行执行两路处理
            start_time = asyncio.get_event_loop().time()
            
            # 两路并行任务
            # 路径1: 规则匹配(直接处理文本)
            rule_task = asyncio.create_task(self.call_rule_matcher_text(combined_text))
            
            # 路径2: UIE抽取 + 向量匹配
            uie_vector_task = asyncio.create_task(self._process_uie_vector_path(texts))
            
            # 等待两路任务完成
            rule_matches, uie_vector_results = await asyncio.gather(
                rule_task, uie_vector_task, return_exceptions=True
            )
            
            duration = asyncio.get_event_loop().time() - start_time
            
            # 处理可能的异常
            if isinstance(rule_matches, Exception):
                self.logger.error(f"Rule matcher call failed: {rule_matches}")
                rule_matches = []
            
            if isinstance(uie_vector_results, Exception):
                self.logger.error(f"UIE+Vector path failed: {uie_vector_results}")
                uie_vector_results = {"uie_pairs": [], "vector_matches": []}
            
            self.structured_logger.log_model_inference(
                'single_message_processing', len(texts), duration, True,
                topic_id=message.topic_id
            )
            
            # 合并两路结果
            enhanced_result = self._merge_two_path_results(
                rule_matches, 
                uie_vector_results.get("uie_pairs", []), 
                uie_vector_results.get("vector_matches", [])
            )
            
            if not enhanced_result:
                final_message = {
                    "nlpParam": {
                        "topic_id": message.topic_id,
                        "source_data": message.source_data.model_dump()
                    },
                    "nlpResult": {
                        "dimensions": enhanced_result
                    }
                }
                
                # 发送到final_topic
                self.kafka_client.send_message(
                    self.kafka_client.TOPICS.get('FINAL_TOPIC', 'final_topic'),
                    final_message,
                    key=message.topic_id
                )
            else:
                # 构建消息发送到intent_topic
                intent_message = {
                    "topic_id": message.topic_id,
                    "source_data": message.source_data.model_dump(),
                    "raw_split_text": texts,
                    "cat_type": "uie",
                    "retry_count": message.retry_count,
                    "error_type": message.error_type,
                    "error_msg": message.error_msg,
                    "last_ts": message.last_ts,
                    "result": enhanced_result,
                    "qaresult": message.qaresult
                }
                
                # UIE结果发送到intent_topic进行情感和意图分析
                self.kafka_client.send_to_intent_topic(intent_message)
                
                self.structured_logger.log_business_event(
                    'single_message_processing_completed',
                    topic_id=message.topic_id,
                    texts_count=len(texts),
                    rule_matches=len(rule_matches) if isinstance(rule_matches, list) else 0,
                    uie_pairs=len(uie_vector_results.get("uie_pairs", [])),
                    vector_matches=len(uie_vector_results.get("vector_matches", [])),
                    processing_duration=duration
                )
                
        except Exception as e:
            self.logger.error(f"Error processing single message: {e}")
            await self._send_error_message(message, str(e))
    
    async def start_consumers(self):
        """启动消费者"""
        # 启动UIE消息消费者
        self.kafka_client.start_consumer(
            [self.kafka_client.TOPICS['UIE_TOPIC']], 
            self.process_uie_message,
            group_id='uie-service-group',
            consumer_name=None,
            auto_offset_reset='latest',
            enable_auto_commit=False  # 启用手动提交
        )
        
        self.logger.info("UIE consumers started")
    
    async def _cleanup_batch_processing(self):
        """清理批量处理资源"""
        # 停止后台超时检查任务
        if self.timeout_check_task:
            self.logger.info("Stopping batch timeout checker")
            self.timeout_check_task.cancel()
            try:
                await self.timeout_check_task
            except asyncio.CancelledError:
                self.logger.info("Batch timeout checker stopped successfully")
            except Exception as e:
                self.logger.error(f"Error stopping batch timeout checker: {e}")
            finally:
                self.timeout_check_task = None
        
        async with self.batch_lock:
            # 处理剩余的批量消息
            if self.message_batch:
                self.logger.info(f"Processing remaining {len(self.message_batch)} messages ({self.current_batch_text_count} texts) in batch during cleanup")
                await self._process_message_batch()
            
            # 重置状态
            self.batch_start_time = None
    
    async def _service_specific_cleanup(self):
        """UIE服务特定的清理逻辑"""
        await self._cleanup_batch_processing()
        self.logger.info("UIE service cleanup completed")
    
    async def _service_specific_health_check(self) -> dict:
        """UIE服务特定的健康检查"""
        import time
        
        # 使用缓存机制减少频繁的API调用
        # 只在5分钟间隔检查一次外部API，其他时候返回缓存状态
        current_time = time.time()
        
        # 初始化缓存
        if not hasattr(self, '_api_status_cache'):
            self._api_status_cache = {
                'status': 'unknown',
                'last_check': 0
            }
        
        # 如果距离上次检查超过5分钟，才进行实际API检查
        if current_time - self._api_status_cache['last_check'] > 300:  # 5分钟
            try:
                self.logger.info(f"UIE健康检查：正在调用UIE API进行连通性测试...")
                # 使用轻量级测试：发送空数据到API测试连通性
                test_data = {"texts": ["车机死机"]}
                headers = {
                    "Content-Type": "application/json",
                    "Accept": "application/json"
                }
                
                if self.model_config.get('api_key'):
                    headers["Authorization"] = f"Bearer {self.model_config['api_key']}"
                
                self.logger.info(f"UIE健康检查：调用API - {self.model_config['api_url']}")
                response = await self.http_client.post(
                    self.model_config['api_url'],
                    json=test_data,
                    headers=headers,
                    timeout=5
                )
                
                # 更新缓存 - 只有200才是真正健康的
                if response.status_code == 200:
                    self._api_status_cache['status'] = "healthy"
                    self.logger.info(f"UIE健康检查：API响应正常 (200)")
                elif response.status_code in [400, 422]:
                    # 400/422表示API能响应但参数有问题，说明API服务正常但我们的测试数据不合适
                    self._api_status_cache['status'] = "api_reachable_bad_request"
                    self.logger.info(f"UIE健康检查：API可达但参数错误 ({response.status_code})")
                else:
                    # 其他状态码表示服务异常
                    self._api_status_cache['status'] = "unhealthy"
                    self.logger.warning(f"UIE健康检查：API响应异常 ({response.status_code})")
                
                self._api_status_cache['last_check'] = current_time
                
            except Exception as e:
                # API不可用时，服务仍可继续运行
                self._api_status_cache['status'] = "unavailable_fallback"
                self._api_status_cache['last_check'] = current_time
                self.logger.info(f"UIE健康检查：API调用失败 - {e}")
        else:
            self.logger.debug(f"UIE健康检查：使用缓存状态 - {self._api_status_cache['status']}")
        
        return {
            'uie_api': self._api_status_cache['status'],
            'text_processor': 'ready' if self.text_processor else 'not_ready',
            'fallback_method': 'service_continues_running',
            'last_api_check': time.strftime('%H:%M:%S', time.localtime(self._api_status_cache['last_check'])),
            'check_interval': '5min',
            'batch_processing': {
                'enabled': self.enable_batch_processing,
                'batch_size': self.batch_size,
                'batch_timeout': self.batch_timeout,
                'current_batch_messages': len(self.message_batch),
                'current_batch_texts': self.current_batch_text_count,
                'has_pending_timer': self.batch_start_time is not None
            }
        }
    
    def _add_custom_routes(self, app: FastAPI):
        """添加UIE服务特定的路由"""
        
        @app.post("/extract")
        async def extract_entities(request: dict):
            """手动抽取实体（用于测试）"""
            try:
                texts = request.get('texts', [])
                if not texts:
                    raise HTTPException(status_code=400, detail="Texts are required")
                
                results = await self.call_uie_api(texts)
                pairs = self._extract_entity_description_pairs(results, texts)
                formatted = self._format_uie_result(pairs)
                
                return APIResponse(
                    success=True, 
                    message="UIE extraction completed", 
                    data={
                        'raw_results': results,
                        'entity_description_pairs': pairs,
                        'formatted_result': formatted
                    }
                )
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))
        
        @app.get("/batch/status")
        async def get_batch_status():
            """获取批量处理状态"""
            return APIResponse(
                success=True,
                message="Batch status retrieved",
                data={
                    'batch_processing_enabled': self.enable_batch_processing,
                    'batch_size': self.batch_size,
                    'batch_timeout': self.batch_timeout,
                    'current_batch_messages': len(self.message_batch),
                    'current_batch_texts': self.current_batch_text_count,
                    'has_pending_timer': self.batch_start_time is not None
                }
            )
        
        @app.post("/batch/force_process")
        async def force_process_batch():
            """强制处理当前批量（用于测试）"""
            try:
                async with self.batch_lock:
                    if not self.message_batch:
                        return APIResponse(
                            success=True,
                            message="No messages in batch to process",
                            data={'processed_count': 0}
                        )
                    
                    batch_messages = len(self.message_batch)
                    batch_texts = self.current_batch_text_count
                    await self._process_message_batch()
                    
                    return APIResponse(
                        success=True,
                        message=f"Forced processing of {batch_messages} messages with {batch_texts} texts",
                        data={
                            'processed_messages': batch_messages,
                            'processed_texts': batch_texts
                        }
                    )
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))


# 创建服务实例
service = UIEService()

# 创建FastAPI应用
app = service.create_fastapi_app(
    title="UIE Service",
    description="UIE模型服务"
)


if __name__ == "__main__":
    service.run_with_app_string("main:app")