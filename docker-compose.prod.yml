# VOC2.0 生产环境配置 - 多实例版本
# 6意图 + 6UIE + 4大模型 + 1规则匹配 + 1向量匹配
version: '3.8'

# 公共配置
x-common-env: &common-env
  CONFIG_FILE: /app/configs/config_prod.yaml  # 使用生产环境配置
  OPENBLAS_NUM_THREADS: 1
  OMP_NUM_THREADS: 1
  MKL_NUM_THREADS: 1
  USE_SIMPLE_THREADED_LEVEL3: 1
  KMP_AFFINITY: disabled
  KMP_INIT_AT_FORK: FALSE
  MALLOC_ARENA_MAX: 2
  UVLOOP_NO_EXTENSIONS: 1
  PYTHONFAULTHANDLER: 1
  OMP_PROC_BIND: false
  OMP_WAIT_POLICY: PASSIVE

x-common-ulimits: &common-ulimits
  nproc: 65535
  nofile:
    soft: 65535
    hard: 65535

x-common-security: &common-security
  pids_limit: -1
  security_opt:
    - seccomp=unconfined

x-common-volumes: &common-volumes
  - ./shared:/app/shared
  - ./services:/app/services
  - ./configs:/app/configs
  - ./logs/prod:/app/logs

services:
  # 文本处理服务
  text-processor-prod:
    image: voc-base:latest
    container_name: voc-text-processor-prod
    privileged: true
    ports:
      - "8200:8200"
    environment:
      SERVICE_NAME: text-processor
      SERVICE_PORT: "8200"
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod:
        aliases:
          - text-processor
    restart: unless-stopped

  # 规则匹配服务 - 1个实例
  rule-matcher-prod:
    image: voc-base:latest
    container_name: voc-rule-matcher-prod
    privileged: true
    ports:
      - "8110:8110"
    environment:
      SERVICE_NAME: rule-matcher
      SERVICE_PORT: "8110"
      RULE_MATCHER_PORT: "8110"
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod:
        aliases:
          - rule-matcher
    restart: unless-stopped

  # 向量匹配服务 - 1个实例
  vector-matcher-prod:
    image: voc-base:latest
    container_name: voc-vector-matcher-prod
    privileged: true
    ports:
      - "8120:8120"
    environment:
      SERVICE_NAME: vector-matcher
      SERVICE_PORT: "8120"
      VECTOR_MATCHER_PORT: "8120"
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes:
      - ./shared:/app/shared
      - ./services:/app/services
      - ./configs:/app/configs
      - ./logs/prod:/app/logs
      - ./models:/app/models  # 模型文件只读
    networks:
      voc-network-prod:
        aliases:
          - vector-matcher
    restart: unless-stopped

  # UIE模型服务 - 6个实例
  uie-prod-1:
    image: voc-base:latest
    container_name: voc-uie-prod-1
    privileged: true
    ports:
      - "8140:8140"
    environment:
      SERVICE_NAME: uie
      SERVICE_PORT: "8140"
      UIE_API_URL: "http://10.62.133.18:5000/ppuie"  # UIE-1 API地址
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod:
        aliases:
          - uie-1
    restart: unless-stopped

  uie-prod-2:
    image: voc-base:latest
    container_name: voc-uie-prod-2
    privileged: true
    ports:
      - "8141:8140"
    environment:
      SERVICE_NAME: uie
      SERVICE_PORT: "8140"
      UIE_API_URL: "http://10.62.133.18:5001/ppuie"  # UIE-2 API地址
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod:
        aliases:
          - uie-2
    restart: unless-stopped

  uie-prod-3:
    image: voc-base:latest
    container_name: voc-uie-prod-3
    privileged: true
    ports:
      - "8142:8140"
    environment:
      SERVICE_NAME: uie
      SERVICE_PORT: "8140"
      UIE_API_URL: "http://10.62.133.18:5002/ppuie"  # UIE-3 API地址
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod:
        aliases:
          - uie-3
    restart: unless-stopped

  uie-prod-4:
    image: voc-base:latest
    container_name: voc-uie-prod-4
    privileged: true
    ports:
      - "8143:8140"
    environment:
      SERVICE_NAME: uie
      SERVICE_PORT: "8140"
      UIE_API_URL: "http://10.62.133.18:5003/ppuie"  # UIE-4 API地址
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod:
        aliases:
          - uie-4
    restart: unless-stopped

  uie-prod-5:
    image: voc-base:latest
    container_name: voc-uie-prod-5
    privileged: true
    ports:
      - "8144:8140"
    environment:
      SERVICE_NAME: uie
      SERVICE_PORT: "8140"
      UIE_API_URL: "http://10.62.133.18:5004/ppuie"  # UIE-5 API地址
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod:
        aliases:
          - uie-5
    restart: unless-stopped

  uie-prod-6:
    image: voc-base:latest
    container_name: voc-uie-prod-6
    privileged: true
    ports:
      - "8145:8140"
    environment:
      SERVICE_NAME: uie
      SERVICE_PORT: "8140"
      UIE_API_URL: "http://10.62.133.18:5005/ppuie"  # UIE-6 API地址
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod:
        aliases:
          - uie-6
    restart: unless-stopped

  # 大模型服务 - 4个实例
  llm-prod-1:
    image: voc-base:latest
    container_name: voc-llm-prod-1
    privileged: true
    ports:
      - "8150:8150"
    environment:
      SERVICE_NAME: llm
      SERVICE_PORT: "8150"
      LLM_API_URL: "http://10.62.133.18:8001/v1/chat/completions"  # LLM-1 API地址
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod:
        aliases:
          - llm-1
    restart: unless-stopped

  llm-prod-2:
    image: voc-base:latest
    container_name: voc-llm-prod-2
    privileged: true
    ports:
      - "8151:8150"
    environment:
      SERVICE_NAME: llm
      SERVICE_PORT: "8150"
      LLM_API_URL: "http://10.62.133.18:8002/v1/chat/completions"  # LLM-2 API地址
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod:
        aliases:
          - llm-2
    restart: unless-stopped

  llm-prod-3:
    image: voc-base:latest
    container_name: voc-llm-prod-3
    privileged: true
    ports:
      - "8152:8150"
    environment:
      SERVICE_NAME: llm
      SERVICE_PORT: "8150"
      LLM_API_URL: "http://10.62.133.18:8003/v1/chat/completions"  # LLM-3 API地址
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod:
        aliases:
          - llm-3
    restart: unless-stopped

  llm-prod-4:
    image: voc-base:latest
    container_name: voc-llm-prod-4
    privileged: true
    ports:
      - "8153:8150"
    environment:
      SERVICE_NAME: llm
      SERVICE_PORT: "8150"
      LLM_API_URL: "http://10.62.133.18:8004/v1/chat/completions"  # LLM-4 API地址
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod:
        aliases:
          - llm-4
    restart: unless-stopped

  # 意图分析服务 - 6个实例
  intent-prod-1:
    image: voc-base:latest
    container_name: voc-intent-prod-1
    privileged: true
    ports:
      - "8160:8160"
    environment:
      SERVICE_NAME: intent
      SERVICE_PORT: "8160"
      INTENT_API_URL: "http://10.62.133.18:6000/analyze"  # Intent-1 API地址
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod:
        aliases:
          - intent-1
    restart: unless-stopped

  intent-prod-2:
    image: voc-base:latest
    container_name: voc-intent-prod-2
    privileged: true
    ports:
      - "8161:8160"
    environment:
      SERVICE_NAME: intent
      SERVICE_PORT: "8160"
      INTENT_API_URL: "http://10.62.133.18:6001/analyze"  # Intent-2 API地址
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod:
        aliases:
          - intent-2
    restart: unless-stopped

  intent-prod-3:
    image: voc-base:latest
    container_name: voc-intent-prod-3
    privileged: true
    ports:
      - "8162:8160"
    environment:
      SERVICE_NAME: intent
      SERVICE_PORT: "8160"
      INTENT_API_URL: "http://10.62.133.18:6002/analyze"  # Intent-3 API地址
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod:
        aliases:
          - intent-3
    restart: unless-stopped

  intent-prod-4:
    image: voc-base:latest
    container_name: voc-intent-prod-4
    privileged: true
    ports:
      - "8163:8160"
    environment:
      SERVICE_NAME: intent
      SERVICE_PORT: "8160"
      INTENT_API_URL: "http://10.62.133.18:6003/analyze"  # Intent-4 API地址
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod:
        aliases:
          - intent-4
    restart: unless-stopped

  intent-prod-5:
    image: voc-base:latest
    container_name: voc-intent-prod-5
    privileged: true
    ports:
      - "8164:8160"
    environment:
      SERVICE_NAME: intent
      SERVICE_PORT: "8160"
      INTENT_API_URL: "http://10.62.133.18:6004/analyze"  # Intent-5 API地址
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod:
        aliases:
          - intent-5
    restart: unless-stopped

  intent-prod-6:
    image: voc-base:latest
    container_name: voc-intent-prod-6
    privileged: true
    ports:
      - "8165:8160"
    environment:
      SERVICE_NAME: intent
      SERVICE_PORT: "8160"
      INTENT_API_URL: "http://10.62.133.18:6005/analyze"  # Intent-6 API地址
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod:
        aliases:
          - intent-6
    restart: unless-stopped

  # 品牌归属服务
  brand-attribution-prod:
    image: voc-base:latest
    container_name: voc-brand-attribution-prod
    privileged: true
    ports:
      - "8170:8170"
    environment:
      SERVICE_NAME: brand-attribution
      SERVICE_PORT: "8170"
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod:
        aliases:
          - brand-attribution
    restart: unless-stopped

  # 错误处理服务
  error-handler-prod:
    image: voc-base:latest
    container_name: voc-error-handler-prod
    privileged: true
    ports:
      - "8130:8130"
    environment:
      SERVICE_NAME: error-handler
      SERVICE_PORT: "8130"
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod:
        aliases:
          - error-handler
    restart: unless-stopped

  # 后处理服务
  post-processor-prod:
    image: voc-base:latest
    container_name: voc-post-processor-prod
    privileged: true
    ports:
      - "8180:8180"
    environment:
      SERVICE_NAME: post-processor
      SERVICE_PORT: "8180"
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod:
        aliases:
          - post-processor
    restart: unless-stopped

  # API网关
  gateway-prod:
    image: voc-base:latest
    container_name: voc-gateway-prod
    privileged: true
    ports:
      - "8100:8100"
    environment:
      SERVICE_NAME: gateway
      SERVICE_PORT: "8100"
      # 环境配置
      ENV_SUFFIX: "prod"
      # 多实例配置
      LLM_INSTANCE_COUNT: "4"
      UIE_INSTANCE_COUNT: "6"
      INTENT_INSTANCE_COUNT: "6"
      <<: *common-env
    ulimits: *common-ulimits
    <<: *common-security
    volumes: *common-volumes
    networks:
      voc-network-prod:
        aliases:
          - gateway
    depends_on:
      - text-processor-prod
      - rule-matcher-prod
      - vector-matcher-prod
      - uie-prod-1
      - uie-prod-2
      - uie-prod-3
      - uie-prod-4
      - uie-prod-5
      - uie-prod-6
      - llm-prod-1
      - llm-prod-2
      - llm-prod-3
      - llm-prod-4
      - intent-prod-1
      - intent-prod-2
      - intent-prod-3
      - intent-prod-4
      - intent-prod-5
      - intent-prod-6
      - brand-attribution-prod
      - error-handler-prod
      - post-processor-prod
    restart: unless-stopped

networks:
  voc-network-prod:
    driver: bridge
